{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "<PERSON>rip<PERSON>",
            "type": "go",
            "request": "launch",
            "mode": "debug",
            "program": "${workspaceFolder}/cmd/script",
            "env": {},
            "cwd": "${workspaceFolder}",
            "buildFlags": "-gcflags 'all=-N -l'",
            "showLog": true,
        },
        {
            "name": "Api",
            "type": "go",
            "request": "launch",
            "mode": "debug",
            "program": "${workspaceFolder}/cmd/llm_service",
            "env": {},
            "args": ["-env", "local"],
            "cwd": "${workspaceFolder}",
            "buildFlags": "-gcflags 'all=-N -l'",
            "showLog": true,
        }
    ]
}
