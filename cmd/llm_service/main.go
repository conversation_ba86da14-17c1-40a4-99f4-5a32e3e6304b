package main

import (
	"context"
	"flag"
	"llm_service/internal/common"
	"llm_service/internal/pkg/dayu_trace"
	"llm_service/internal/pkg/sync_go"
	"llm_service/pkg/tcm"
	"llm_service/pkg/zlog"
	"os"

	feature "git.100tal.com/znxx_xpp/feature-ab-client-go"
	"git.100tal.com/znxx_xpp/go-libs/traces"

	"llm_service/internal/conf"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"

	_ "go.uber.org/automaxprocs"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string = common.AppNameApi
	// Version is the version of the compiled software.
	Version string = "1.0.0"
	// App Env
	env string
	// Hostname
	id, _ = os.Hostname()
)

func init() {
	flag.StringVar(&env, "env", string(common.LocalEnv), "app env, eg: -env local")
	flag.StringVar(&Name, "name", Name, "app name, eg: -name kratos-layout")
}

func newApp(logger log.Logger, hs *http.Server) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.AfterStop(func(ctx context.Context) error {
			log.NewHelper(logger).Info("app stop")
			return nil
		}),
		kratos.Server(
			hs,
		),
	)
}

func main() {
	flag.Parse()
	common.CurrentEnv = common.Env(env)

	c, err := tcm.NewConfig(Name, common.Env(env), "config.yaml")
	if err != nil {
		panic(err)
	}
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}

	zlog.Init(Name, bc.Log.Filename, int(bc.Log.MaxSize), int(bc.Log.MaxBackup), int(bc.Log.MaxAge), bc.Log.Compress)
	defer zlog.Sync()
	logger := log.With(zlog.NewZapLogger(zlog.STDInstance()),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", id,
		"service.name", Name,
		"service.version", Version,
		"trace_id", tracing.TraceID(),
		"span_id", tracing.SpanID(),
		"dayu_trace_id", dayu_trace.TraceID(),
	)
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	sync_go.Go(nil, log.NewHelper(logger), func() {
		fabConfig := feature.FabConfig{
			Env: feature.Env(env),
		}
		feature.Register(fabConfig, []string{"qwen_lvwang_switch"})
	})

	// 开启阿里云tracing
	if err = traces.StartTrace(ctx, env, Name, Version); err != nil {
		panic(err)
	}

	// c.Watch("data", func(key string, value config.Value) {
	// 	value.Scan(&bc.Data)
	// 	fmt.Printf("data changed: %+v", bc.Data)
	// })

	app, cleanup, err := wireApp(bc.Server, bc.Data, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
