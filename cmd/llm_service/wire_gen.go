// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"llm_service/internal/biz"
	"llm_service/internal/biz/legal"
	"llm_service/internal/biz/llm"
	"llm_service/internal/biz/llm/tal_middleground"
	"llm_service/internal/biz/llm/tal_ws"
	"llm_service/internal/biz/prompt"
	"llm_service/internal/conf"
	"llm_service/internal/data"
	"llm_service/internal/server"
	"llm_service/internal/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, logger log.Logger) (*kratos.App, func(), error) {
	dataData, cleanup, err := data.NewData(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	baseUseCase := llm.NewLLMBaseUseCase(confData)
	talUseCase := tal_ws.NewTalUseCase(baseUseCase, logger)
	chatDao := data.NewChatDao(dataData, logger)
	promptPrompt := prompt.NewPrompt(chatDao, logger)
	sseBaiChuan := biz.NewSSEBaiChuan(confData, logger)
	sseAzure4 := biz.NewSSEAzure4(confData, logger)
	ssegpt4Turbo := biz.NewSSEGPT4Turbo(confData, logger)
	ssegpt4Exclusive := biz.NewSSEGPT4Exclusive(confData, logger)
	sseTal70B := biz.NewSSETal70B(confData, talUseCase, logger)
	talMiddleGroundUseCase := tal_middleground.NewTalMiddleGroundUseCase(baseUseCase, logger)
	sseTalMiddleGround := biz.NewSSETalMiddleGround(confData, talMiddleGroundUseCase, logger)
	sseTal7B := biz.NewSSETal7B(confData, logger)
	sseErnie := biz.NewSSEErnie(confData, logger)
	sseBaidu := biz.NewSSEBaidu(confData, chatDao, logger)
	sseTalComposition := biz.NewSSETalComposition(confData, logger)
	sseTalMathGPTBaseBackend := biz.NewSSETalMathGPTBaseBackend(confData, logger)
	legalBizUseCase := legal.NewLegalBizUseCase(logger, confData)
	sseTongYiQwenMaxOpenAi := biz.NewSSETongYiQwenMaxOpenAi(confData, logger)
	sseTongYiQwenPlusOpenAi := biz.NewSSETongYiQwenPlusOpenAi(confData, logger)
	sseTongYiQwen72BOpenAi := biz.NewSSETongYiQwen72BOpenAi(confData, logger)
	sseLlama370b := biz.NewSseLlama370b(confData, logger)
	sseTalEnDialogue := biz.NewSSETalEnDialogue(confData, logger)
	sseLlama370bModelSquare := biz.NewSSELlama370bModelSquare(confData, logger)
	sseDouBaoLite32kOpenAi := biz.NewSSEDouBaoLite32kOpenAi(confData, logger)
	sseDouBaoPro32kOpenAi := biz.NewSSEDouBaoPro32kOpenAi(confData, logger)
	sseDeepSeekV3OpenAi := biz.NewSSEDeepSeekV3OpenAi(confData, logger)
	sseDeepSeekReasonerOpenAi := biz.NewSSEDeepSeekReasonerOpenAi(confData, logger)
	sseDeepSeekDistillQwen32BOpenAi := biz.NewSSEDeepSeekDistillQwen32BOpenAi(confData, logger)
	multiModalClient := biz.NewMultiModalClient(confData, logger)
	claude37SonnetClient:=biz.NewSSEClaude37SonnetOpenAi(confData,logger)
	sseUseCase := biz.NewSseUseCase(confData, dataData, talUseCase, promptPrompt, sseBaiChuan, sseAzure4, ssegpt4Turbo, ssegpt4Exclusive, sseTal70B, sseTalMiddleGround, sseTal7B, sseErnie, sseBaidu, sseTalComposition, sseTalMathGPTBaseBackend, legalBizUseCase, sseTongYiQwenMaxOpenAi, sseTongYiQwenPlusOpenAi, sseTongYiQwen72BOpenAi, sseLlama370b, sseTalEnDialogue, sseLlama370bModelSquare, sseDouBaoLite32kOpenAi, sseDouBaoPro32kOpenAi, sseDeepSeekReasonerOpenAi,sseDeepSeekV3OpenAi, sseDeepSeekDistillQwen32BOpenAi, multiModalClient,claude37SonnetClient, logger)
	sseService := service.NewSseService(sseUseCase, talUseCase, logger)
	httpServer := server.NewHTTPServer(confServer, sseService, logger)
	app := newApp(logger, httpServer)
	return app, func() {
		cleanup()
	}, nil
}
