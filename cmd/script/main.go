package main

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
)

// ModelConfig 模型配置
type ModelConfig struct {
	ModelName string
	AuthToken string
}

// APIResponse API响应结构
type APIResponse struct {
	ID                string `json:"id"`
	Object            string `json:"object"`
	Model             string `json:"model"`
	Created           int64  `json:"created"`
	SystemFingerprint string `json:"system_fingerprint"`
	Usage             struct {
		PromptTokens     int `json:"prompt_tokens"`
		TotalTokens      int `json:"total_tokens"`
		CompletionTokens int `json:"completion_tokens"`
	} `json:"usage"`
	Choices []struct {
		FinishReason string `json:"finish_reason"`
		Message      struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		Index int `json:"index"`
	} `json:"choices"`
}

// APIRequest API请求结构
type APIRequest struct {
	Model    string       `json:"model"`
	Stream   bool         `json:"stream"`
	Messages []APIMessage `json:"messages"`
}

type APIMessage struct {
	Role    string      `json:"role"`
	Content interface{} `json:"content"`
}

type ContentItem struct {
	Type     string    `json:"type"`
	Text     string    `json:"text,omitempty"`
	ImageURL *ImageURL `json:"image_url,omitempty"`
}

type ImageURL struct {
	URL string `json:"url"`
}

// 模型配置列表
var models = []ModelConfig{
	{
		ModelName: "doubao-pro-vision-32k",
		AuthToken: "Bearer 1000080843:7241c1857f501eddbb1dbdfa6c180ad8",
	},
	{
		ModelName: "qwen-vl-max",
		AuthToken: "Bearer 1000080844:800c0297c807f2aa48cfc76d7a797065",
	},
	{
		ModelName: "glm-4v-flash",
		AuthToken: "Bearer 1000080846:854ae10219ac5ef3bcefbbd5a2196f9c",
	},
}

// imageToBase64 将图片转换为base64编码
func imageToBase64(imagePath string) (string, error) {
	// 读取图片文件
	imageFile, err := os.Open(imagePath)
	if err != nil {
		return "", fmt.Errorf("打开图片文件失败: %v", err)
	}
	defer imageFile.Close()

	// 读取文件内容
	imageData, err := ioutil.ReadAll(imageFile)
	if err != nil {
		return "", fmt.Errorf("读取图片文件失败: %v", err)
	}

	// 转换为base64
	return base64.StdEncoding.EncodeToString(imageData), nil
}

// callAPI 调用API
func callAPI(model ModelConfig, base64Image string, query string) (*APIResponse, error) {
	url := "http://ai-service.tal.com/openai-compatible/v1/chat/completions" // 替换为实际的API地址

	// 构建请求体

	prompt := "背景：你是一个百科助手，会结合孩子输入的图片和问题给孩子进行知识科普，百科答疑。\n" +
		"角色：希望你扮演一个亲和力强、知识渊博的百科助手，能够用简单易懂的语言向孩子解释复杂的问题。\n" +
		"任务：根据孩子输入的图片和query，向孩子进行知识科普，解答其提出的问题。\n" +
		"要求：\n" +
		"1.根据孩子提供的图片和query，简洁明了地解释相关知识点，确保易于理解。\n" +
		"2.确保回答准确可靠，避免误导孩子。\n" +
		"3.采用友好、亲和的语言风格，让孩子对知识产生兴趣和好奇心。"
	request := APIRequest{
		Model:  model.ModelName,
		Stream: false,
		Messages: []APIMessage{
			{
				Role:    "system",
				Content: prompt,
			},
			{
				Role: "user",
				Content: []ContentItem{
					{
						Type: "text",
						Text: query,
					},
					{
						Type: "image_url",
						ImageURL: &ImageURL{
							URL: fmt.Sprintf("data:image/png;base64,%s", base64Image),
						},
					},
				},
			},
		},
	}

	// 转换为JSON
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("JSON编码失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", model.AuthToken)

	// 发送请求
	client := &http.Client{Timeout: 300 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response APIResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &response, nil
}

// 修改函数签名，添加 rowIndex 参数
func getImageFromExcel(f *excelize.File, imageFormula string, rowIndex int) (string, error) {
	// 从公式中提取图片ID
	imageID := strings.TrimPrefix(imageFormula, "=DISPIMG(\"")
	imageID = strings.TrimSuffix(imageID, "\",1)")

	if imageID == imageFormula {
		return "", fmt.Errorf("无效的图片公式格式: %s", imageFormula)
	}

	// 获取所有图片
	images, err := f.GetPictures("Sheet1", fmt.Sprintf("A%d", rowIndex+2))
	if err != nil {
		return "", fmt.Errorf("获取图片失败: %v", err)
	}

	if len(images) == 0 {
		return "", fmt.Errorf("未找到图片")
	}

	// 获取第一张图片的数据
	imageData := images[0].File
	return base64.StdEncoding.EncodeToString(imageData), nil
}

// 添加新的结构体用于存储处理结果
type ProcessResult struct {
	RowIndex  int
	ModelName string
	Content   string
	Error     error
}

// 修改 processExcel 函数
func processExcel(inputFile, outputFile string) error {
	// 打开Excel文件
	f, err := excelize.OpenFile(inputFile)
	if err != nil {
		return fmt.Errorf("打开Excel文件失败: %v", err)
	}
	defer f.Close()

	// 获取第一个sheet
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return fmt.Errorf("读取sheet失败: %v", err)
	}

	// 创建新的Excel文件用于保存结果
	newFile := excelize.NewFile()
	newSheet := "Sheet1"

	// 写入表头
	headers := []string{"图片", "query"}
	for _, model := range models {
		headers = append(headers, model.ModelName)
	}
	for col, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+col)
		newFile.SetCellValue(newSheet, cell, header)
	}

	// 创建结果通道
	resultChan := make(chan ProcessResult, len(models))
	// 创建计数器
	processed := 0
	// 每20条保存一次
	saveInterval := 30

	// 处理每一行数据
	for rowIndex, row := range rows[1:] { // 跳过表头
		imageFormula := row[0]
		query := row[1]

		// 写入原始数据，并复制图片到新文件
		newFile.SetCellValue(newSheet, fmt.Sprintf("A%d", rowIndex+2), imageFormula)
		newFile.SetCellValue(newSheet, fmt.Sprintf("B%d", rowIndex+2), query)

		// 获取图片base64
		base64Image, err := getImageFromExcel(f, imageFormula, rowIndex)
		if err != nil {
			fmt.Printf("处理图片失败 %s: %v\n", imageFormula, err)
			continue
		}

		// 并发调用每个模型的API
		for modelIndex, model := range models {
			go func(mIndex int, m ModelConfig, rIndex int, img string, q string) {
				fmt.Printf("处理第 %d 行，模型: %s\n", rIndex+1, m.ModelName)

				response, err := callAPI(m, img, q)
				result := ProcessResult{
					RowIndex:  rIndex,
					ModelName: m.ModelName,
				}

				if err != nil {
					result.Error = err
					fmt.Printf("API调用失败 %s: %v\n", m.ModelName, err)
				} else {
					result.Content = response.Choices[0].Message.Content
				}

				resultChan <- result
			}(modelIndex, model, rowIndex, base64Image, query)
		}

		// 等待所有模型的结果
		for i := 0; i < len(models); i++ {
			result := <-resultChan
			if result.Error == nil {
				// 找到对应模型的列
				var modelIndex int
				for i, m := range models {
					if m.ModelName == result.ModelName {
						modelIndex = i
						break
					}
				}
				cell := fmt.Sprintf("%c%d", 'C'+modelIndex, result.RowIndex+2)
				newFile.SetCellValue(newSheet, cell, result.Content)
			}
		}

		processed++

		// 每处理 saveInterval 条数据保存一次
		if processed%saveInterval == 0 {
			tempFile := fmt.Sprintf("%s.temp.%d.xlsx", outputFile[:len(outputFile)-5], processed)
			if err := newFile.SaveAs(tempFile); err != nil {
				fmt.Printf("保存临时文件失败: %v\n", err)
			} else {
				fmt.Printf("已处理 %d 条数据，保存临时文件: %s\n", processed, tempFile)
			}
		}

		// 简单的进度显示
		fmt.Printf("进度: %.2f%% (%d/%d)\n", float64(processed)/float64(len(rows)-1)*100, processed, len(rows)-1)
	}

	// 最终保存
	if err := newFile.SaveAs(outputFile); err != nil {
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}

	fmt.Printf("处理完成，结果已保存到: %s\n", outputFile)
	return nil
}

func main() {
	inputFile := "/Users/<USER>/Downloads/query002.xlsx"  // 输入文件路径
	outputFile := "/Users/<USER>/Downloads/query002.xlsx" // 输出文件路径

	if err := processExcel(inputFile, outputFile); err != nil {
		fmt.Printf("处理失败: %v\n", err)
		os.Exit(1)
	}
}
