server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9011
    timeout: 100s
  env: local
data:
  database:
    driver: mysql
    source: taishan:E7XIzjXtQpv7mL32@tcp(rm-2zee82n4243c8o406.mysql.rds.aliyuncs.com:3306)/paas_service?timeout=1s&readTimeout=1s&writeTimeout=1s&parseTime=true&loc=Local&charset=utf8mb4,utf8
  redis:
    host: r-2zeg988izwyy63jzhx.redis.rds.aliyuncs.com
    port: 6379
    password: 820PurringMinutes
    db: 81
  thirdParty:
    legalCheck:
      host: "http://hmi.chengjiukehu.com/censor-service/v1/check/shumei"
      bizMap:
        1:
          serviceId: "300010"
          input: "lui_input"
          output: "lui_output"
        2:
          serviceId: "300010"
          input: "input"
          output: "output"
        3:
          serviceId: "300010"
          input: "input"
          output: "output"
        16:
          serviceId: "300010"
          input: "input"
          output: "output"
        18:
          serviceId: "300010"
          input: "input"
          output: "output"
        19:
          serviceId: "300010"
          input: "input"
          output: "output"
        21:
          serviceId: "300010"
          input: "input"
          output: "output"
        25:
          serviceId: "300010"
          input: "input"
          output: "output"
        29:
          serviceId: "300010"
          input: "input"
          output: "output"
        32:
          serviceId: "300010"
          input: "input"
          output: "output"
        100:
          serviceId: "gongjuceshi"
          input: "yingwenshuru"
          output: "yingwenshuchu"
        1000000:
          serviceId: "gongjuceshi"
          input: "output"
          output: "output"
  llm:
    filterPunctuation: ",:?!：，。？！~"
    azure:
      temperature: 1
      topP: 0.4
      n: 1
      initSwitch: 2
      gpt4ApiKeys: [ "1000080023:********************************" ]
      gpt4TurboApiKeys: [ "1000080456:********************************" ]
      gpt4ExclusiveApiKeys: [ "1000080463:********************************" ]
      apiKeys: [ "zhinengyingjian11&********************************","zhinengyingjian12&70b0e061276944f2944d494a7a35c089" ]
      url: "https://%s.openai.azure.com/openai/deployments/GPT35/chat/completions?api-version=2023-03-15-preview"
      gpt4Url: "http://msai-test.tal.com/openai/deployments/gpt-4/chat/completions?api-version=2023-05-15"
      gpt4TurboUrl: "http://ai-service-test.tal.com/openai/deployments/gpt-4-turbo/chat/completions?api-version=2024-02-01"
      gpt4ExclusiveUrl: "http://ai-service-test.tal.com/openai/deployments/gpt-4-8k/chat/completions?api-version=2024-02-01 "
    baidu:
      clientId: "vgsVsX7Dpz6mCNzgqKDUgAHw"
      clientSecret: "jyetcmhXCevnG35n6RO6luQ3qwi1SkjE"
      grantType: "client_credentials"
      url: "https://aip.baidubce.com/oauth/2.0/token"
      chatUrl: "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant"
      version: "v2"
    tal:
      zh:
        url: "ws://openai.100tal.com/aitext/mathgpt/transfer"
        key: "920701731602448384"
        secret: "da06f3b11be84222b06afcca6db1d301"
        mod:
          200001: "smart-com6b"
      en:
        url: "ws://openai.100tal.com/aitext/mathgpt/transfer"
        key: "920701731602448384"
        secret: "da06f3b11be84222b06afcca6db1d301"
        mod:
          200001: "smart-en-com6b"
      tal70b:
        url: "ws://openai.100tal.com/aitext/multi-70b/multi-70b/ws"
        key: "920701731602448384"
        secret: "da06f3b11be84222b06afcca6db1d301"
        isCheck: 0
      llama3_70b: #停用
        url: "http://openai.100tal.com/aitext/70b3/multi-70b/http"
        key: "920701731602448384"
        secret: "da06f3b11be84222b06afcca6db1d301"
        isCheck: 0
      llama3_70b_model_square:
        model: "llama-3-70b"
        temperature: 0.1
        apiKey: "1000080581:91a71a0e87d5f06a2058f0902b7bb41d"
        url: "http://ai-service-test.tal.com/tal/deployments/llama-3-70b/chat/completions"
      tal7b:
        url: "http://openai.100tal.com/aitext/rag-7b/rag-7b/http"
        key: "920701731602448384"
        secret: "da06f3b11be84222b06afcca6db1d301"
        isCheck: 0
      en_composition:
        url: "https://openai.100tal.com/aimathgpt/en-compostion"
        key: "920701731602448384"
        secret: "da06f3b11be84222b06afcca6db1d301"
        isCheck: 0
      ch_composition:
        url: "https://openai.100tal.com/aimathgpt/ch-compostion"
        key: "920701731602448384"
        secret: "da06f3b11be84222b06afcca6db1d301"
      mathGptBaseBackend:
        url: "https://openai.100tal.com/ai/backend/learning/ask/http"
        key: "920701731602448384"
        secret: "da06f3b11be84222b06afcca6db1d301"
        isCheck: 0
      en_dialogue:
        url: "http://gateway-bp.facethink.com/aitext/jiuzhangduihua/jiuzhangduihua/http"
        key: "4786117720392704"
        secret: "0c5501d4f1a84d508b590846542cdceb"
    baichuan:
      model: "Baichuan2-Turbo"
      temperature: 0.1
      with_search_enhance: true
      apiKey: "1000080078:a57835c672be15d475b7316ffd96bb01"
      url: "http://ai-service-test.tal.com/baichuan/deployments/baichuan-2/chat/completions?api-version=2023-05-15"
      isCheck: 0
    ernie:
      ernieBot8k:
        model: "ernie-bot-8k"
        temperature: 0.1
        apiKey: "1000080083:8bb22314f53b721be7b0aca911d6fff0"
        url: "http://ai-service-test.tal.com/qianfan/deployments/ernie-bot-8k/chat/completions"
        isCheck: 0
      ernieBot:
        model: "ernie-bot"
        temperature: 0.8
        apiKey: "1000080084:f12bca120fa088dabab8b4aa415c1b94"
        url: "http://ai-service-test.tal.com/qianfan/deployments/ernie-bot/chat/completions"
        isCheck: 0
    anthropic:
      claude37Sonnet:
        model: "claude-3.7-sonnet"
        temperature: 1
        apiKey: "1000081138:2543da1e42612175c564109a945205a1"
        url: "http://ai-service-test.tal.com/openai-compatible/v1/chat/completions"
        isCheck: 0
    tongyi:
      qwenMax:
        model: "qwen-max"
        temperature: 0.1
        apiKey: "1000080405:fc446d448b881e78fddcbb91e5951bb1"
        url: "http://ai-service-test.tal.com/openai-compatible/v1/chat/completions"
        isCheck: 0
      qwen72b:
        model: "qwen1.5-72b"
        temperature: 0.1
        apiKey: "1000080537:1d8a2ef859c0471fd2d60afa0c0044c8"
        url: "http://ai-service-test.tal.com/openai-compatible/v1/chat/completions"
        isCheck: 0
      qwenPlus:
        model: "qwen-plus"
        temperature: 0.1
        apiKey: "1000080528:017c0044a8ae44e4e6b53230e1d25702"
        url: "http://ai-service-test.tal.com/openai-compatible/v1/chat/completions"
    doubao:
      douBaoLite32k:
        model: "doubao-lite-32k"
        temperature: 0.1
        apiKey: "1000080703:22b9a27de1b26cc6b89e045e2274118c"
        url: "http://ai-service-test.tal.com/openai-compatible/v1/chat/completions"
        isCheck: 0
      douBaoPro32k:
        model: "doubao-pro-32k"
        temperature: 0.1
        apiKey: "1000080704:9b1c178acdabc2d670ab4c98b2484520"
        url: "http://ai-service-test.tal.com/openai-compatible/v1/chat/completions"
        isCheck: 0
    deepSeek:
      deepSeekR1:
        model: "deepseek-reasoner"
        temperature: 0.1
        apiKey: "1000080930:b566b08c16fe133f244acc5d3b87b642"
        url: "http://ai-service-test.tal.com/openai-compatible/v1/chat/completions"
        isCheck: 0
      deepSeekV3:
        model: "deepseek-chat"
        temperature: 0.1
        apiKey: "1000080929:4d0b303ec5893f1bf0068153a7355138"
        url: "http://ai-service-test.tal.com/openai-compatible/v1/chat/completions"
        isCheck: 0
      deepSeekR1DistillQwen32B:
        model: "deepseek-r1-distill-qwen-32b"
        temperature: 0.1
        apiKey: "1000080982:a826d97ecbf6d6330aa8b1eb72892f77"
        url: "http://ai-service-test.tal.com/openai-compatible/v1/chat/completions"
        isCheck: 0
    multiModal:
      qwenVlMax:
        model: "qwen-vl-max"
        temperature: 0.1
        apiKey: "1000080756:819d453caf39bd372065cba23a3e33dc"
        url: "http://ai-service.tal.com/openai-compatible/v1/chat/completions"
        isCheck: 0
      glm4vflash:
        model: "glm-4v-flash"
        temperature: 0.1
        apiKey: "1000080846:854ae10219ac5ef3bcefbbd5a2196f9c"
        url: "http://ai-service.tal.com/openai-compatible/v1/chat/completions"
        isCheck: 0
      doubaoProVision32k:
        model: "doubao-pro-vision-32k"
        temperature: 0.1
        apiKey: "1000080843:7241c1857f501eddbb1dbdfa6c180ad8"
        url: "http://ai-service.tal.com/openai-compatible/v1/chat/completions"
        isCheck: 0

log:
  filename: "./log/llm_service.log"
  max_size: 1
  max_backup: 5
  max_age: 10
  compress: true
