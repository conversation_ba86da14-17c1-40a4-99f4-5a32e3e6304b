<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM服务Redis统计功能详细说明</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #e74c3c;
        }
        .feature-card h4 {
            color: #c0392b;
            margin-top: 0;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }
        .log-example {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #007acc;
        }
        .architecture-diagram {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .flow-step {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 5px;
            font-weight: bold;
        }
        .arrow {
            font-size: 20px;
            color: #3498db;
            margin: 0 10px;
        }
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .stats-table th, .stats-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .stats-table th {
            background-color: #3498db;
            color: white;
        }
        .stats-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            padding: 15px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
            border-radius: 4px;
        }
        .warning {
            background-color: #f8d7da;
            padding: 15px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 LLM服务Redis统计功能详细说明</h1>
        
        <div class="highlight">
            <strong>📊 功能概述：</strong> 本功能通过Redis实现对LLM模型请求的实时统计监控，包括RPM（每分钟请求数）、QPS（每秒请求数）、错误频率和状态码统计，并在日志中自动显示统计信息。
        </div>

        <h2>🎯 核心功能特性</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>📈 RPM统计</h4>
                <p><strong>Redis Key:</strong> <code>llm:stats:rpm:{model}:{date}:{hour}:{minute}</code></p>
                <p><strong>过期时间:</strong> 25小时</p>
                <p><strong>用途:</strong> 监控每个模型每分钟的请求量</p>
            </div>
            <div class="feature-card">
                <h4>⚡ QPS统计</h4>
                <p><strong>Redis Key:</strong> <code>llm:stats:qps:{model}:{date}:{hour}:{minute}:{second}</code></p>
                <p><strong>过期时间:</strong> 2小时</p>
                <p><strong>用途:</strong> 监控每个模型每秒的请求量</p>
            </div>
            <div class="feature-card">
                <h4>❌ 错误统计</h4>
                <p><strong>Redis Key:</strong> <code>llm:stats:error:{model}:{errorType}:{date}:{hour}</code></p>
                <p><strong>过期时间:</strong> 25小时</p>
                <p><strong>错误类型:</strong> request_error, status_error</p>
            </div>
            <div class="feature-card">
                <h4>🔍 状态码统计</h4>
                <p><strong>Redis Key:</strong> <code>llm:stats:status:{model}:{status}:{date}:{hour}</code></p>
                <p><strong>过期时间:</strong> 25小时</p>
                <p><strong>用途:</strong> 统计各种HTTP状态码的出现频率</p>
            </div>
        </div>

        <h2>🏗️ 架构设计与实现逻辑</h2>
        
        <h3>1. 全局ChatDao设计</h3>
        <div class="architecture-diagram">
            <div class="flow-step">应用启动</div>
            <span class="arrow">→</span>
            <div class="flow-step">SseUseCase初始化</div>
            <span class="arrow">→</span>
            <div class="flow-step">设置全局ChatDao</div>
            <span class="arrow">→</span>
            <div class="flow-step">所有SSE客户端自动获得统计能力</div>
        </div>

        <div class="code-block">
// 全局ChatDao实例，用于统计
var globalChatDao *data.ChatDao

// SetGlobalChatDao 设置全局ChatDao实例
func SetGlobalChatDao(chatDao *data.ChatDao) {
    globalChatDao = chatDao
}

// 在SseUseCase构造函数中自动设置
func NewSseUseCase(..., chatDao *data.ChatDao, ...) *SseUseCase {
    // 设置全局ChatDao，这样所有SSE客户端都可以使用统计功能
    llm.SetGlobalChatDao(chatDao)
    ...
}
        </div>

        <h3>2. 智能回退机制</h3>
        <div class="code-block">
// getStatsInfo 获取模型统计信息字符串
func (c *Client) getStatsInfo(ctx context.Context, modelName string) string {
    chatDao := c.chatDao
    if chatDao == nil {
        chatDao = globalChatDao  // 回退到全局实例
    }
    if chatDao == nil {
        return ""  // 静默失效，不影响主业务
    }
    // ... 获取统计信息
}
        </div>

        <h2>📊 统计数据结构</h2>
        <table class="stats-table">
            <thead>
                <tr>
                    <th>统计类型</th>
                    <th>Redis Key格式</th>
                    <th>数据类型</th>
                    <th>过期时间</th>
                    <th>用途说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>RPM统计</td>
                    <td>llm:stats:rpm:{model}:{date}:{hour}:{minute}</td>
                    <td>Integer</td>
                    <td>25小时</td>
                    <td>每分钟请求数统计</td>
                </tr>
                <tr>
                    <td>QPS统计</td>
                    <td>llm:stats:qps:{model}:{date}:{hour}:{minute}:{second}</td>
                    <td>Integer</td>
                    <td>2小时</td>
                    <td>每秒请求数统计</td>
                </tr>
                <tr>
                    <td>错误统计</td>
                    <td>llm:stats:error:{model}:{errorType}:{date}:{hour}</td>
                    <td>Integer</td>
                    <td>25小时</td>
                    <td>按错误类型统计</td>
                </tr>
                <tr>
                    <td>状态码统计</td>
                    <td>llm:stats:status:{model}:{status}:{date}:{hour}</td>
                    <td>Integer</td>
                    <td>25小时</td>
                    <td>HTTP状态码统计</td>
                </tr>
            </tbody>
        </table>

        <h2>📝 日志输出效果展示</h2>
        
        <h3>正常请求日志</h3>
        <div class="log-example">
2024-01-15 14:30:25 INFO sse common req: {message:"你好", model:"gpt-4"}, type:1, desc:gpt-4, req_times_1 [RPM:45 QPS:2 HourlyErr:3 HourlyReq:2650 ErrRate:0.11%]
        </div>

        <h3>错误请求日志</h3>
        <div class="log-example">
2024-01-15 14:30:26 ERROR request 模型名称:gpt-4 , error: context deadline exceeded , llm body {...} [RPM:45 QPS:2 HourlyErr:4 HourlyReq:2650 ErrRate:0.15%]
        </div>

        <h3>状态码错误日志</h3>
        <div class="log-example">
2024-01-15 14:30:27 ERROR request llm is error: status:500,reqbody:{...},respbody:Internal Server Error [RPM:45 QPS:2 HourlyErr:4 HourlyReq:2650 ErrRate:0.15%]
        </div>

        <h2>🔄 数据流转过程</h2>
        <div class="architecture-diagram">
            <h4>请求处理流程</h4>
            <div style="margin: 20px 0;">
                <div class="flow-step">1. 接收请求</div>
                <span class="arrow">→</span>
                <div class="flow-step">2. 记录RPM/QPS</div>
                <span class="arrow">→</span>
                <div class="flow-step">3. 调用LLM API</div>
                <span class="arrow">→</span>
                <div class="flow-step">4. 处理响应</div>
            </div>
            <div style="margin: 20px 0;">
                <div class="flow-step">5a. 成功：记录成功统计</div>
                <br>
                <div class="flow-step">5b. 失败：记录错误统计</div>
                <br>
                <div class="flow-step">5c. 状态码异常：记录状态码统计</div>
            </div>
            <div style="margin: 20px 0;">
                <span class="arrow">↓</span>
                <div class="flow-step">6. 生成统计信息</div>
                <span class="arrow">→</span>
                <div class="flow-step">7. 输出到日志</div>
            </div>
        </div>

        <h2>⚙️ 核心实现代码</h2>
        
        <h3>统计记录实现</h3>
        <div class="code-block">
// 记录RPM和QPS统计
func (c *Client) Send(ctx context.Context, ...) {
    // 获取模型名称
    modelName := extractModelName(body)
    
    // 记录统计
    chatDao := c.getChatDao()  // 智能获取ChatDao实例
    if chatDao != nil && modelName != "" {
        _ = chatDao.IncrementRPM(ctx, modelName)
        _ = chatDao.IncrementQPS(ctx, modelName)
    }
    
    // 处理请求...
    if err != nil {
        // 记录错误统计
        if chatDao != nil {
            _ = chatDao.IncrementError(ctx, modelName, data.RequestErrorType)
        }
    }
}
        </div>

        <h3>统计信息获取</h3>
        <div class="code-block">
// GetModelStats 获取模型的综合统计信息
func (g ChatDao) GetModelStats(ctx context.Context, modelName string) (*ModelStats, error) {
    stats := &ModelStats{ModelName: modelName}
    
    // 获取当前RPM
    rpm, _ := g.GetCurrentRPM(ctx, modelName)
    stats.CurrentRPM = rpm
    
    // 获取当前QPS
    qps, _ := g.GetCurrentQPS(ctx, modelName)
    stats.CurrentQPS = qps
    
    // 计算错误率
    errorCount, _ := g.GetHourlyErrorCount(ctx, modelName, RequestErrorType)
    totalReq := g.calculateHourlyRequests(ctx, modelName)
    
    if totalReq > 0 {
        errorRate := float64(errorCount) / float64(totalReq) * 100
        stats.ErrorRate = fmt.Sprintf("%.2f%%", errorRate)
    }
    
    return stats, nil
}
        </div>

        <h2>🎯 实际应用效果</h2>
        
        <div class="success">
            <h4>✅ 零配置使用</h4>
            <p>开发者无需修改任何现有代码，统计功能自动生效。所有通过 <code>llm.NewClient()</code> 创建的SSE客户端都会自动具备统计能力。</p>
        </div>

        <div class="success">
            <h4>✅ 实时监控</h4>
            <p>通过日志可以实时看到每个模型的请求量、错误率等关键指标，便于及时发现和处理问题。</p>
        </div>

        <div class="success">
            <h4>✅ 性能友好</h4>
            <p>使用Redis Pipeline批量操作，统计失败不会影响主业务流程，确保系统稳定性。</p>
        </div>

        <div class="warning">
            <h4>⚠️ 注意事项</h4>
            <ul>
                <li>确保Redis服务正常运行</li>
                <li>监控Redis内存使用情况</li>
                <li>根据业务需求调整key的过期时间</li>
                <li>建议配置Redis持久化以防数据丢失</li>
            </ul>
        </div>

        <h2>📈 监控建议</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>🚨 告警设置</h4>
                <ul>
                    <li>RPM超过阈值告警</li>
                    <li>错误率超过5%告警</li>
                    <li>连续500状态码告警</li>
                </ul>
            </div>
            <div class="feature-card">
                <h4>📊 数据分析</h4>
                <ul>
                    <li>使用ELK解析统计日志</li>
                    <li>建立Dashboard展示</li>
                    <li>生成定期报告</li>
                </ul>
            </div>
            <div class="feature-card">
                <h4>🔧 性能优化</h4>
                <ul>
                    <li>根据RPM/QPS调整负载均衡</li>
                    <li>根据错误率优化模型配置</li>
                    <li>根据状态码排查系统问题</li>
                </ul>
            </div>
        </div>

        <div class="highlight">
            <h4>🎉 总结</h4>
            <p>通过Redis统计功能，我们实现了对LLM服务的全面监控，不仅能够实时了解各个模型的使用情况和性能表现，还能快速定位和解决问题。这个功能的设计充分考虑了易用性、性能和可维护性，为LLM服务的稳定运行提供了强有力的支撑。</p>
        </div>
    </div>
</body>
</html>
