<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM服务Redis统计功能详细说明</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #e74c3c;
        }
        .feature-card h4 {
            color: #c0392b;
            margin-top: 0;
        }
        .code-block {
            background: #282c34;
            color: #abb2bf;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.8;
            border: 1px solid #3e4451;
            position: relative;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .code-block::before {
            content: attr(data-lang);
            position: absolute;
            top: 5px;
            right: 10px;
            background: #61dafb;
            color: #282c34;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .code-block .keyword { color: #c678dd; font-weight: bold; }
        .code-block .string { color: #98c379; }
        .code-block .comment { color: #5c6370; font-style: italic; }
        .code-block .function { color: #61dafb; font-weight: 600; }
        .code-block .type { color: #e06c75; font-weight: 500; }
        .code-block .operator { color: #56b6c2; }
        .code-block .number { color: #d19a66; }
        .code-block .variable { color: #e5c07b; }

        .log-example {
            background: #0d1117;
            color: #c9d1d9;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.6;
            border-left: 4px solid #58a6ff;
            position: relative;
            overflow-x: auto;
        }
        .log-example::before {
            content: "📋 日志输出";
            position: absolute;
            top: 5px;
            right: 10px;
            background: #58a6ff;
            color: #0d1117;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        .log-timestamp { color: #7c3aed; }
        .log-level-info { color: #10b981; font-weight: bold; }
        .log-level-error { color: #ef4444; font-weight: bold; }
        .log-stats { color: #f59e0b; background: rgba(245, 158, 11, 0.1); padding: 2px 4px; border-radius: 3px; }

        .architecture-diagram {
            text-align: center;
            margin: 25px 0;
            padding: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .flow-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin: 20px 0;
        }
        .flow-step {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            color: white;
            padding: 12px 18px;
            border-radius: 25px;
            font-weight: 600;
            border: 2px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
            min-width: 120px;
            text-align: center;
        }
        .flow-step:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .arrow {
            font-size: 24px;
            color: rgba(255,255,255,0.8);
            margin: 0 5px;
        }
        .flow-vertical {
            flex-direction: column;
        }
        .flow-branch {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin: 10px;
        }
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .stats-table th, .stats-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .stats-table th {
            background-color: #3498db;
            color: white;
        }
        .stats-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            padding: 15px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
            border-radius: 4px;
        }
        .warning {
            background-color: #f8d7da;
            padding: 15px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 LLM服务Redis统计功能详细说明</h1>
        
        <div class="highlight">
            <strong>📊 功能概述：</strong> 本功能通过Redis实现对LLM模型请求的实时统计监控，包括RPM（每分钟请求数）、QPS（每秒请求数）、错误频率和状态码统计，并在日志中自动显示统计信息。
        </div>

        <h2>🎯 核心功能特性</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>📈 RPM统计</h4>
                <p><strong>Redis Key:</strong> <code>llm:stats:rpm:{model}:{date}:{hour}:{minute}</code></p>
                <p><strong>过期时间:</strong> 25小时</p>
                <p><strong>用途:</strong> 监控每个模型每分钟的请求量</p>
            </div>
            <div class="feature-card">
                <h4>⚡ QPS统计</h4>
                <p><strong>Redis Key:</strong> <code>llm:stats:qps:{model}:{date}:{hour}:{minute}:{second}</code></p>
                <p><strong>过期时间:</strong> 2小时</p>
                <p><strong>用途:</strong> 监控每个模型每秒的请求量</p>
            </div>
            <div class="feature-card">
                <h4>❌ 错误统计</h4>
                <p><strong>Redis Key:</strong> <code>llm:stats:error:{model}:{errorType}:{date}:{hour}</code></p>
                <p><strong>过期时间:</strong> 25小时</p>
                <p><strong>错误类型:</strong> request_error, status_error</p>
            </div>
            <div class="feature-card">
                <h4>🔍 状态码统计</h4>
                <p><strong>Redis Key:</strong> <code>llm:stats:status:{model}:{status}:{date}:{hour}</code></p>
                <p><strong>过期时间:</strong> 25小时</p>
                <p><strong>用途:</strong> 统计各种HTTP状态码的出现频率</p>
            </div>
        </div>

        <h2>🏗️ 架构设计与实现逻辑</h2>
        
        <h3>1. 全局ChatDao设计架构</h3>
        <div class="architecture-diagram">
            <h4 style="margin-top: 0; color: white;">🏗️ 初始化流程</h4>
            <div class="flow-container">
                <div class="flow-step">🚀 应用启动</div>
                <span class="arrow">→</span>
                <div class="flow-step">🔧 依赖注入</div>
                <span class="arrow">→</span>
                <div class="flow-step">📊 SseUseCase初始化</div>
                <span class="arrow">→</span>
                <div class="flow-step">🌐 设置全局ChatDao</div>
            </div>
            <div style="margin: 20px 0; color: rgba(255,255,255,0.9);">
                ⬇️ 自动生效
            </div>
            <div class="flow-container">
                <div class="flow-step">📡 SSE客户端A</div>
                <div class="flow-step">📡 SSE客户端B</div>
                <div class="flow-step">📡 SSE客户端C</div>
                <div class="flow-step">📡 ...</div>
            </div>
            <div style="margin-top: 15px; color: rgba(255,255,255,0.9); font-size: 14px;">
                所有SSE客户端自动获得统计能力，无需额外配置
            </div>
        </div>

        <div class="code-block" data-lang="Go"><span class="comment">// ==================== 全局ChatDao设计 ====================</span>

<span class="comment">// 全局ChatDao实例，用于统计</span>
<span class="keyword">var</span> globalChatDao *data.ChatDao

<span class="comment">// SetGlobalChatDao 设置全局ChatDao实例</span>
<span class="keyword">func</span> <span class="function">SetGlobalChatDao</span>(chatDao *data.ChatDao) {
    globalChatDao = chatDao
}

<span class="comment">// ==================== 自动初始化逻辑 ====================</span>

<span class="comment">// 在SseUseCase构造函数中自动设置全局ChatDao</span>
<span class="keyword">func</span> <span class="function">NewSseUseCase</span>(
    conf *conf.Data,
    data *data.Data,
    chatDao *data.ChatDao, <span class="comment">// 通过依赖注入获得</span>
    tal *tal_ws.TalUseCase,
    <span class="comment">// ... 其他参数</span>
    logger log.Logger,
) *SseUseCase {

    <span class="comment">// 🌐 设置全局ChatDao，这样所有SSE客户端都可以使用统计功能</span>
    llm.<span class="function">SetGlobalChatDao</span>(chatDao)

    <span class="keyword">return</span> &SseUseCase{
        conf:    conf,
        db:      data,
        chatDao: chatDao, <span class="comment">// 同时保存在实例中</span>
        log:     log.<span class="function">NewHelper</span>(logger),
        <span class="comment">// ... 其他字段初始化</span>
    }
}</div>

        <h3>2. 智能回退机制</h3>
        <div class="architecture-diagram">
            <h4 style="margin-top: 0; color: white;">🔄 ChatDao获取策略</h4>
            <div class="flow-container flow-vertical">
                <div class="flow-step">🔍 检查客户端级ChatDao</div>
                <span class="arrow">⬇️</span>
                <div style="color: rgba(255,255,255,0.9);">存在？</div>
                <div class="flow-container">
                    <div class="flow-branch">
                        <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">✅ 是：使用客户端ChatDao</div>
                    </div>
                    <div class="flow-branch">
                        <div class="flow-step" style="background: rgba(239, 68, 68, 0.3);">❌ 否：检查全局ChatDao</div>
                        <span class="arrow">⬇️</span>
                        <div style="color: rgba(255,255,255,0.9); margin: 10px 0;">全局存在？</div>
                        <div class="flow-container">
                            <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">✅ 使用全局ChatDao</div>
                            <div class="flow-step" style="background: rgba(156, 163, 175, 0.3);">❌ 静默失效</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="code-block" data-lang="Go"><span class="comment">// ==================== 智能回退机制实现 ====================</span>

<span class="comment">// getStatsInfo 获取模型统计信息字符串</span>
<span class="keyword">func</span> (c *Client) <span class="function">getStatsInfo</span>(
    ctx context.Context,
    modelName <span class="type">string</span>,
) <span class="type">string</span> {
    <span class="comment">// 🔍 第一优先级：使用客户端级别的ChatDao</span>
    chatDao := c.chatDao

    <span class="comment">// 🔄 第二优先级：回退到全局ChatDao</span>
    <span class="keyword">if</span> chatDao == <span class="keyword">nil</span> {
        chatDao = globalChatDao
    }

    <span class="comment">// 🛡️ 保护机制：如果都没有，静默失效，不影响主业务</span>
    <span class="keyword">if</span> chatDao == <span class="keyword">nil</span> {
        <span class="keyword">return</span> <span class="string">""</span> <span class="comment">// 返回空字符串，日志中不显示统计信息</span>
    }

    <span class="comment">// 📊 获取统计信息</span>
    stats, err := chatDao.<span class="function">GetModelStats</span>(ctx, modelName)
    <span class="keyword">if</span> err != <span class="keyword">nil</span> && !errors.<span class="function">Is</span>(err, redis.Nil) {
        <span class="keyword">return</span> fmt.<span class="function">Sprintf</span>(<span class="string">"[stats_error: %v]"</span>, err)
    }

    <span class="keyword">if</span> stats == <span class="keyword">nil</span> {
        <span class="keyword">return</span> <span class="string">"[no_stats]"</span>
    }

    <span class="comment">// 🎯 格式化统计信息输出</span>
    <span class="keyword">return</span> fmt.<span class="function">Sprintf</span>(
        <span class="string">"[RPM:%d QPS:%d HourlyErr:%d HourlyReq:%d ErrRate:%s]"</span>,
        stats.CurrentRPM,
        stats.CurrentQPS,
        stats.HourlyErrorCount,
        stats.HourlyReqCount,
        stats.ErrorRate,
    )
}

<span class="comment">// ==================== 统一的ChatDao获取方法 ====================</span>

<span class="comment">// getChatDao 统一获取ChatDao实例的方法</span>
<span class="keyword">func</span> (c *Client) <span class="function">getChatDao</span>() *data.ChatDao {
    <span class="keyword">if</span> c.chatDao != <span class="keyword">nil</span> {
        <span class="keyword">return</span> c.chatDao <span class="comment">// 优先使用客户端级别的</span>
    }
    <span class="keyword">return</span> globalChatDao <span class="comment">// 回退到全局的</span>
}</div>

        <h2>📊 RPM和QPS计算原理详解</h2>

        <div class="highlight">
            <strong>🔍 核心原理：</strong> 使用<strong>Redis Hash结构</strong>和<strong>时间窗口聚合</strong>实现高效统计。RPM使用Hash存储每小时的60个分钟数据，QPS使用5分钟窗口聚合，大幅减少Redis key数量，提高存储效率和查询性能。
        </div>

        <h3>📈 RPM (Requests Per Minute) 计算机制</h3>

        <div class="architecture-diagram">
            <h4 style="margin-top: 0; color: white;">🕐 RPM时间窗口示例</h4>
            <div style="color: rgba(255,255,255,0.9); margin: 15px 0;">
                假设当前时间：2024-01-15 14:30:25
            </div>
            <div class="flow-container">
                <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">14:30:10<br/>请求1</div>
                <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">14:30:25<br/>请求2</div>
                <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">14:30:45<br/>请求3</div>
            </div>
            <div style="margin: 15px 0; color: rgba(255,255,255,0.9);">
                ⬇️ 所有请求都记录到同一个Redis Key
            </div>
            <div class="flow-step" style="background: rgba(59, 130, 246, 0.3);">
                Key: llm:stats:rpm:gpt-4:2024-01-15:14:30<br/>
                Value: 3 (当前分钟RPM)
            </div>
        </div>

        <div class="code-block" data-lang="Go"><span class="comment">// ==================== RPM记录机制 ====================</span>

<span class="comment">// 每次请求时调用，使用Hash结构存储每小时的分钟级数据</span>
<span class="keyword">func</span> <span class="function">IncrementRPM</span>(ctx context.Context, modelName <span class="type">string</span>) <span class="type">error</span> {
    now := time.<span class="function">Now</span>()

    <span class="comment">// 🔑 Hash Key: 精确到小时，减少key数量</span>
    hashKey := fmt.<span class="function">Sprintf</span>(<span class="string">"llm:stats:rpm:%s:%s:%02d"</span>,
        modelName,                    <span class="comment">// "gpt-4"</span>
        now.<span class="function">Format</span>(<span class="string">"2006-01-02"</span>),      <span class="comment">// "2024-01-15"</span>
        now.<span class="function">Hour</span>())                   <span class="comment">// 14</span>
    <span class="comment">// Hash Key: "llm:stats:rpm:gpt-4:2024-01-15:14"</span>

    <span class="comment">// 🏷️ Hash Field: 具体的分钟数</span>
    field := fmt.<span class="function">Sprintf</span>(<span class="string">"%02d"</span>, now.<span class="function">Minute</span>()) <span class="comment">// "30"</span>

    <span class="comment">// ⚡ 使用HINCRBY增加Hash字段的值（原子性操作）</span>
    pipe := rdb.<span class="function">Pipeline</span>()
    pipe.<span class="function">HIncrBy</span>(ctx, hashKey, field, 1)      <span class="comment">// Hash字段+1</span>
    pipe.<span class="function">Expire</span>(ctx, hashKey, 25*time.Hour)   <span class="comment">// 整个Hash过期</span>
    _, err := pipe.<span class="function">Exec</span>(ctx)
    <span class="keyword">return</span> err
}

<span class="comment">// 获取当前分钟的RPM值</span>
<span class="keyword">func</span> <span class="function">GetCurrentRPM</span>(ctx context.Context, modelName <span class="type">string</span>) (<span class="type">int64</span>, <span class="type">error</span>) {
    now := time.<span class="function">Now</span>()

    <span class="comment">// 🔍 生成Hash Key</span>
    hashKey := fmt.<span class="function">Sprintf</span>(<span class="string">"llm:stats:rpm:%s:%s:%02d"</span>,
        modelName, now.<span class="function">Format</span>(<span class="string">"2006-01-02"</span>), now.<span class="function">Hour</span>())
    field := fmt.<span class="function">Sprintf</span>(<span class="string">"%02d"</span>, now.<span class="function">Minute</span>())

    <span class="comment">// 📊 从Hash中获取特定字段的值</span>
    <span class="keyword">return</span> rdb.<span class="function">HGet</span>(ctx, hashKey, field).<span class="function">Int64</span>()
}</div>

        <h3>⚡ QPS (Queries Per Second) 计算机制</h3>

        <div class="architecture-diagram">
            <h4 style="margin-top: 0; color: white;">⏱️ QPS时间窗口示例</h4>
            <div style="color: rgba(255,255,255,0.9); margin: 15px 0;">
                假设当前时间：2024-01-15 14:30:25
            </div>
            <div class="flow-container">
                <div class="flow-step" style="background: rgba(245, 158, 11, 0.3);">14:30:25.100<br/>请求1</div>
                <div class="flow-step" style="background: rgba(245, 158, 11, 0.3);">14:30:25.500<br/>请求2</div>
                <div class="flow-step" style="background: rgba(156, 163, 175, 0.3);">14:30:26.100<br/>新的一秒</div>
            </div>
            <div style="margin: 15px 0; color: rgba(255,255,255,0.9);">
                ⬇️ 同一秒内的请求记录到同一个Key
            </div>
            <div class="flow-container">
                <div class="flow-step" style="background: rgba(59, 130, 246, 0.3);">
                    Key: llm:stats:qps:gpt-4:2024-01-15:14:30:25<br/>
                    Value: 2 (第25秒的QPS)
                </div>
                <div class="flow-step" style="background: rgba(59, 130, 246, 0.3);">
                    Key: llm:stats:qps:gpt-4:2024-01-15:14:30:26<br/>
                    Value: 1 (第26秒的QPS)
                </div>
            </div>
        </div>

        <div class="code-block" data-lang="Go"><span class="comment">// ==================== QPS记录机制 ====================</span>

<span class="comment">// 每次请求时调用，使用5分钟窗口聚合，减少key数量</span>
<span class="keyword">func</span> <span class="function">IncrementQPS</span>(ctx context.Context, modelName <span class="type">string</span>) <span class="type">error</span> {
    now := time.<span class="function">Now</span>()

    <span class="comment">// 🔑 Hash Key: 精确到小时</span>
    hashKey := fmt.<span class="function">Sprintf</span>(<span class="string">"llm:stats:qps:%s:%s:%02d"</span>,
        modelName,                    <span class="comment">// "gpt-4"</span>
        now.<span class="function">Format</span>(<span class="string">"2006-01-02"</span>),      <span class="comment">// "2024-01-15"</span>
        now.<span class="function">Hour</span>())                   <span class="comment">// 14</span>
    <span class="comment">// Hash Key: "llm:stats:qps:gpt-4:2024-01-15:14"</span>

    <span class="comment">// 🏷️ Hash Field: 5分钟窗口起始时间</span>
    <span class="comment">// 将分钟数按5分钟对齐：00, 05, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55</span>
    windowStart := (now.<span class="function">Minute</span>() / 5) * 5
    field := fmt.<span class="function">Sprintf</span>(<span class="string">"%02d"</span>, windowStart) <span class="comment">// "30"</span>

    <span class="comment">// ⚡ 使用HINCRBY增加Hash字段的值</span>
    pipe := rdb.<span class="function">Pipeline</span>()
    pipe.<span class="function">HIncrBy</span>(ctx, hashKey, field, 1)     <span class="comment">// Hash字段+1</span>
    pipe.<span class="function">Expire</span>(ctx, hashKey, 2*time.Hour)  <span class="comment">// 整个Hash过期</span>
    _, err := pipe.<span class="function">Exec</span>(ctx)
    <span class="keyword">return</span> err
}

<span class="comment">// 获取当前5分钟窗口的QPS值</span>
<span class="keyword">func</span> <span class="function">GetCurrentQPS</span>(ctx context.Context, modelName <span class="type">string</span>) (<span class="type">int64</span>, <span class="type">error</span>) {
    now := time.<span class="function">Now</span>()

    <span class="comment">// 🔍 生成Hash Key</span>
    hashKey := fmt.<span class="function">Sprintf</span>(<span class="string">"llm:stats:qps:%s:%s:%02d"</span>,
        modelName, now.<span class="function">Format</span>(<span class="string">"2006-01-02"</span>), now.<span class="function">Hour</span>())

    <span class="comment">// 🏷️ 计算当前5分钟窗口</span>
    windowStart := (now.<span class="function">Minute</span>() / 5) * 5
    field := fmt.<span class="function">Sprintf</span>(<span class="string">"%02d"</span>, windowStart)

    <span class="comment">// 📊 获取5分钟窗口内的总请求数</span>
    totalInWindow, err := rdb.<span class="function">HGet</span>(ctx, hashKey, field).<span class="function">Int64</span>()
    <span class="keyword">if</span> err != <span class="keyword">nil</span> {
        <span class="keyword">return</span> 0, err
    }

    <span class="comment">// 🧮 返回5分钟窗口内的平均每分钟请求数作为QPS指标</span>
    <span class="keyword">return</span> totalInWindow / 5, <span class="keyword">nil</span>
}</div>

        <h3>🧮 小时请求数计算机制</h3>

        <div class="architecture-diagram">
            <h4 style="margin-top: 0; color: white;">📈 小时统计原理</h4>
            <div style="color: rgba(255,255,255,0.9); margin: 15px 0;">
                通过累加过去60分钟的RPM数据来计算小时总请求数
            </div>
            <div class="flow-container flow-vertical">
                <div class="flow-step">遍历过去60分钟</div>
                <span class="arrow">⬇️</span>
                <div class="flow-container">
                    <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">13:31 RPM:45</div>
                    <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">13:32 RPM:52</div>
                    <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">...</div>
                    <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">14:30 RPM:38</div>
                </div>
                <span class="arrow">⬇️</span>
                <div class="flow-step" style="background: rgba(59, 130, 246, 0.3);">
                    小时总请求数 = 45 + 52 + ... + 38 = 2650
                </div>
            </div>
        </div>

        <div class="code-block" data-lang="Go"><span class="comment">// ==================== 小时请求数计算 ====================</span>

<span class="comment">// 计算当前小时总请求数（通过RPM累加）</span>
<span class="keyword">func</span> <span class="function">calculateHourlyRequests</span>(
    ctx context.Context,
    modelName <span class="type">string</span>,
) <span class="type">int64</span> {
    now := time.<span class="function">Now</span>()
    totalReq := <span class="type">int64</span>(0)

    <span class="comment">// 🔄 遍历过去60分钟的RPM数据</span>
    <span class="keyword">for</span> i := 0; i &lt; 60; i++ {
        minute := now.<span class="function">Add</span>(-time.<span class="function">Duration</span>(i) * time.Minute)

        <span class="comment">// 🔑 生成每分钟的RPM Key</span>
        key := fmt.<span class="function">Sprintf</span>(<span class="string">"llm:stats:rpm:%s:%s:%02d:%02d"</span>,
            modelName,
            minute.<span class="function">Format</span>(<span class="string">"2006-01-02"</span>),
            minute.<span class="function">Hour</span>(),
            minute.<span class="function">Minute</span>())

        <span class="comment">// 📊 获取该分钟的请求数并累加</span>
        count, err := rdb.<span class="function">Get</span>(ctx, key).<span class="function">Int64</span>()
        <span class="keyword">if</span> err == <span class="keyword">nil</span> {
            totalReq += count
        }
    }

    <span class="keyword">return</span> totalReq
}</div>

        <h2>📊 统计数据结构设计</h2>
        <table class="stats-table">
            <thead>
                <tr>
                    <th>统计类型</th>
                    <th>Redis结构</th>
                    <th>Hash Key格式</th>
                    <th>Hash Field</th>
                    <th>时间精度</th>
                    <th>过期时间</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>RPM统计</strong></td>
                    <td>Hash</td>
                    <td>llm:stats:rpm:{model}:{date}:{hour}</td>
                    <td>分钟数 (00-59)</td>
                    <td>精确到分钟</td>
                    <td>25小时</td>
                </tr>
                <tr>
                    <td><strong>QPS统计</strong></td>
                    <td>Hash</td>
                    <td>llm:stats:qps:{model}:{date}:{hour}</td>
                    <td>5分钟窗口 (00,05,10...55)</td>
                    <td>5分钟聚合</td>
                    <td>2小时</td>
                </tr>
                <tr>
                    <td><strong>错误统计</strong></td>
                    <td>Hash</td>
                    <td>llm:stats:error:{model}:{date}:{hour}</td>
                    <td>错误类型 (request_error, status_error)</td>
                    <td>精确到小时</td>
                    <td>25小时</td>
                </tr>
                <tr>
                    <td><strong>状态码统计</strong></td>
                    <td>Hash</td>
                    <td>llm:stats:status:{model}:{date}:{hour}</td>
                    <td>状态码 (500, 429, 404等)</td>
                    <td>精确到小时</td>
                    <td>25小时</td>
                </tr>
            </tbody>
        </table>

        <h2>🔧 当前实现详细分析</h2>

        <h3>📊 Hash结构设计原理</h3>
        <div class="architecture-diagram">
            <h4 style="margin-top: 0; color: white;">🏗️ 当前技术架构</h4>
            <div style="color: rgba(255,255,255,0.9); margin: 15px 0;">
                基于Redis Hash结构的高效统计系统
            </div>

            <div class="flow-container flow-vertical">
                <div style="margin: 15px 0;">
                    <h5 style="color: rgba(255,255,255,0.9);">🔑 Key设计策略</h5>
                    <div class="flow-container">
                        <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">
                            按小时分组<br/>
                            llm:stats:rpm:gpt-4:2024-01-15:14
                        </div>
                        <div class="flow-step" style="background: rgba(59, 130, 246, 0.3);">
                            Hash字段存储<br/>
                            分钟/窗口/类型作为字段
                        </div>
                    </div>
                </div>

                <div style="margin: 15px 0;">
                    <h5 style="color: rgba(255,255,255,0.9);">⚡ 操作特点</h5>
                    <div class="flow-container">
                        <div class="flow-step" style="background: rgba(245, 158, 11, 0.3);">
                            HINCRBY原子操作<br/>
                            确保并发安全
                        </div>
                        <div class="flow-step" style="background: rgba(245, 158, 11, 0.3);">
                            Pipeline批量处理<br/>
                            提高操作效率
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <h3>💾 内存占用精确估算</h3>
        <div class="architecture-diagram">
            <h4 style="margin-top: 0; color: white;">📈 生产环境内存使用</h4>
            <div style="color: rgba(255,255,255,0.9); margin: 15px 0;">
                基于10个模型，每天1000万次请求的实际测算
            </div>

            <div class="flow-container">
                <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">
                    <strong>实际占用</strong><br/>
                    约1MB统计数据<br/>
                    960个Hash Keys
                </div>
                <span class="arrow">→</span>
                <div class="flow-step" style="background: rgba(59, 130, 246, 0.3);">
                    <strong>推荐配置</strong><br/>
                    2GB Redis实例<br/>
                    使用率&lt;0.1%
                </div>
            </div>

            <div style="margin: 20px 0; color: rgba(255,255,255,0.9); font-size: 16px;">
                <strong>极低的内存使用率，为业务增长预留充足空间</strong>
            </div>
        </div>

        <h3>🧮 详细内存估算（10个模型场景）</h3>
        <table class="stats-table">
            <thead>
                <tr>
                    <th>统计类型</th>
                    <th>Hash数量/天</th>
                    <th>每个Hash大小</th>
                    <th>字段数量</th>
                    <th>单日内存占用</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>RPM统计</strong></td>
                    <td>240个 (10模型×24小时)</td>
                    <td>~2KB</td>
                    <td>60个分钟字段</td>
                    <td>480KB</td>
                    <td>每小时一个Hash，存储60分钟数据</td>
                </tr>
                <tr>
                    <td><strong>QPS统计</strong></td>
                    <td>240个 (10模型×24小时)</td>
                    <td>~1KB</td>
                    <td>12个5分钟窗口</td>
                    <td>240KB</td>
                    <td>每小时一个Hash，存储12个时间窗口</td>
                </tr>
                <tr>
                    <td><strong>错误统计</strong></td>
                    <td>240个 (10模型×24小时)</td>
                    <td>~200B</td>
                    <td>2-5个错误类型</td>
                    <td>48KB</td>
                    <td>每小时一个Hash，存储不同错误类型</td>
                </tr>
                <tr>
                    <td><strong>状态码统计</strong></td>
                    <td>240个 (10模型×24小时)</td>
                    <td>~500B</td>
                    <td>3-8个状态码</td>
                    <td>120KB</td>
                    <td>每小时一个Hash，存储不同状态码</td>
                </tr>
                <tr style="background-color: #e8f5e8; font-weight: bold;">
                    <td><strong>单日总计</strong></td>
                    <td>960个Hash</td>
                    <td>-</td>
                    <td>-</td>
                    <td><strong>888KB</strong></td>
                    <td>不到1MB的日常内存使用</td>
                </tr>
                <tr style="background-color: #f0f8ff; font-weight: bold;">
                    <td><strong>25小时保留</strong></td>
                    <td>1,000个Hash</td>
                    <td>-</td>
                    <td>-</td>
                    <td><strong>~1MB</strong></td>
                    <td>考虑过期时间的实际占用</td>
                </tr>
            </tbody>
        </table>

        <h3>📈 不同规模的内存估算</h3>
        <table class="stats-table">
            <thead>
                <tr>
                    <th>业务规模</th>
                    <th>模型数量</th>
                    <th>日请求量</th>
                    <th>Hash数量/天</th>
                    <th>预估内存占用</th>
                    <th>推荐Redis配置</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>小型业务</strong></td>
                    <td>3个模型</td>
                    <td>100万次</td>
                    <td>288个</td>
                    <td>~300KB</td>
                    <td>512MB实例</td>
                </tr>
                <tr>
                    <td><strong>中型业务</strong></td>
                    <td>10个模型</td>
                    <td>1000万次</td>
                    <td>960个</td>
                    <td>~1MB</td>
                    <td>2GB实例</td>
                </tr>
                <tr>
                    <td><strong>大型业务</strong></td>
                    <td>50个模型</td>
                    <td>1亿次</td>
                    <td>4,800个</td>
                    <td>~5MB</td>
                    <td>4GB实例</td>
                </tr>
                <tr>
                    <td><strong>超大型业务</strong></td>
                    <td>100个模型</td>
                    <td>10亿次</td>
                    <td>9,600个</td>
                    <td>~10MB</td>
                    <td>8GB实例</td>
                </tr>
            </tbody>
        </table>

        <h3>🔧 Redis配置建议</h3>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>💾 内存配置</h4>
                <p><strong>推荐配置：</strong> 2GB Redis实例（中型业务）</p>
                <p><strong>实际使用：</strong> 约1MB统计数据</p>
                <p><strong>余量充足：</strong> 支持业务增长和其他数据</p>
            </div>
            <div class="feature-card">
                <h4>⚙️ Redis参数优化</h4>
                <p><strong>maxmemory-policy：</strong> allkeys-lru</p>
                <p><strong>hash-max-ziplist-entries：</strong> 512</p>
                <p><strong>hash-max-ziplist-value：</strong> 64</p>
            </div>
            <div class="feature-card">
                <h4>📈 监控指标</h4>
                <p><strong>内存使用率：</strong> &lt; 80%</p>
                <p><strong>Key数量：</strong> &lt; 100万</p>
                <p><strong>过期Key处理：</strong> &lt; 1000/秒</p>
            </div>
            <div class="feature-card">
                <h4>🔄 数据生命周期</h4>
                <p><strong>热数据：</strong> 当前小时（Redis）</p>
                <p><strong>温数据：</strong> 24小时内（Redis）</p>
                <p><strong>冷数据：</strong> 历史数据（数据库）</p>
            </div>
        </div>

        <h3>📊 性能特征分析</h3>
        <div class="architecture-diagram">
            <h4 style="margin-top: 0; color: white;">⚡ Hash结构性能特征</h4>

            <div class="flow-container flow-vertical">
                <div style="margin: 15px 0;">
                    <h5 style="color: rgba(255,255,255,0.9);">🔍 查询性能</h5>
                    <div class="flow-container">
                        <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">
                            HGET操作<br/>
                            平均延迟：0.1-0.15ms<br/>
                            高效的Hash查找
                        </div>
                        <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">
                            HINCRBY操作<br/>
                            平均延迟：0.12-0.18ms<br/>
                            原子性递增
                        </div>
                    </div>
                </div>

                <div style="margin: 15px 0;">
                    <h5 style="color: rgba(255,255,255,0.9);">💾 内存效率</h5>
                    <div class="flow-container">
                        <div class="flow-step" style="background: rgba(59, 130, 246, 0.3);">
                            Hash字段开销<br/>
                            ~20-30字节/字段<br/>
                            高效存储
                        </div>
                        <div class="flow-step" style="background: rgba(59, 130, 246, 0.3);">
                            Hash元数据<br/>
                            ~100字节/Hash<br/>
                            共享开销
                        </div>
                    </div>
                </div>

                <div style="margin: 15px 0;">
                    <h5 style="color: rgba(255,255,255,0.9);">🔄 过期处理</h5>
                    <div class="flow-container">
                        <div class="flow-step" style="background: rgba(245, 158, 11, 0.3);">
                            Hash过期<br/>
                            CPU使用率：&lt;3%<br/>
                            批量清理
                        </div>
                        <div class="flow-step" style="background: rgba(245, 158, 11, 0.3);">
                            内存回收<br/>
                            即时释放<br/>
                            无碎片化
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <h3>💰 资源成本估算</h3>
        <table class="stats-table">
            <thead>
                <tr>
                    <th>业务规模</th>
                    <th>推荐Redis配置</th>
                    <th>月度成本估算</th>
                    <th>CPU使用率</th>
                    <th>运维复杂度</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>小型业务</strong></td>
                    <td>512MB实例</td>
                    <td>$30-50</td>
                    <td>&lt;5%</td>
                    <td>低</td>
                </tr>
                <tr>
                    <td><strong>中型业务</strong></td>
                    <td>2GB实例</td>
                    <td>$80-120</td>
                    <td>&lt;10%</td>
                    <td>中等</td>
                </tr>
                <tr>
                    <td><strong>大型业务</strong></td>
                    <td>4GB实例</td>
                    <td>$150-200</td>
                    <td>&lt;15%</td>
                    <td>中等</td>
                </tr>
                <tr>
                    <td><strong>超大型业务</strong></td>
                    <td>8GB实例</td>
                    <td>$250-350</td>
                    <td>&lt;20%</td>
                    <td>高</td>
                </tr>
            </tbody>
        </table>

        <div class="success">
            <h4>💡 成本优势</h4>
            <ul>
                <li><strong>低内存占用：</strong> Hash结构大幅减少内存需求，降低实例成本</li>
                <li><strong>高效处理：</strong> 减少CPU开销，提高性能价格比</li>
                <li><strong>简化运维：</strong> 更少的key管理，降低运维复杂度</li>
                <li><strong>弹性扩展：</strong> 支持业务增长，无需频繁升级实例</li>
            </ul>
        </div>

        <h3>🎯 关键技术特点</h3>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>⚡ 高性能设计</h4>
                <p><strong>Redis HINCRBY命令：</strong> 原子性操作，确保高并发环境下计数准确</p>
                <p><strong>Pipeline批量操作：</strong> 减少网络往返，提高操作效率</p>
                <p><strong>Hash结构优化：</strong> 内存使用效率高，查询性能优秀</p>
            </div>
            <div class="feature-card">
                <h4>🕐 智能时间窗口</h4>
                <p><strong>RPM分钟级精度：</strong> 适合趋势分析和容量规划</p>
                <p><strong>QPS 5分钟聚合：</strong> 平衡实时性和存储效率</p>
                <p><strong>小时级错误统计：</strong> 便于问题定位和分析</p>
            </div>
            <div class="feature-card">
                <h4>💾 高效内存管理</h4>
                <p><strong>Hash结构：</strong> 大幅减少key数量，节省内存空间</p>
                <p><strong>智能过期：</strong> RPM保留25小时，QPS保留2小时</p>
                <p><strong>内存占用：</strong> 10个模型仅需约1MB内存</p>
            </div>
            <div class="feature-card">
                <h4>📈 实时监控</h4>
                <p><strong>即时更新：</strong> 每次请求立即更新统计计数器</p>
                <p><strong>实时查询：</strong> 随时获取最新的性能统计数据</p>
                <p><strong>多维度统计：</strong> RPM、QPS、错误率、状态码全覆盖</p>
            </div>
        </div>

        <h2>📝 日志输出效果展示</h2>

        <h3>🟢 正常请求日志</h3>
        <div class="log-example">
<span class="log-timestamp">2024-01-15 14:30:25.123</span> <span class="log-level-info">INFO</span> sse common req: {
    "message": "你好，请帮我写一个Python函数",
    "model": "gpt-4",
    "temperature": 0.7,
    "max_tokens": 1000
}, type:1, desc:gpt-4, req_times_1 <span class="log-stats">[RPM:45 QPS:2 HourlyErr:3 HourlyReq:2650 ErrRate:0.11%]</span>

<span class="log-timestamp">2024-01-15 14:30:25.156</span> <span class="log-level-info">INFO</span> sse_use_retry, gpt_type_gpt-4, biz is chat, 正在请求模版Id 12345 中第 1 个大模型 <span class="log-stats">[RPM:45 QPS:2 HourlyErr:3 HourlyReq:2650 ErrRate:0.11%]</span>
        </div>

        <h3>🔴 错误请求日志</h3>
        <div class="log-example">
<span class="log-timestamp">2024-01-15 14:30:26.789</span> <span class="log-level-error">ERROR</span> request 模型名称:gpt-4, error: context deadline exceeded, llm body {
    "model": "gpt-4",
    "messages": [
        {"role": "user", "content": "请解释量子计算的原理"}
    ],
    "stream": true
} <span class="log-stats">[RPM:45 QPS:2 HourlyErr:4 HourlyReq:2650 ErrRate:0.15%]</span>

<span class="log-timestamp">2024-01-15 14:30:26.790</span> <span class="log-level-error">ERROR</span> SseCommonChat 调用出错 strategy=primary req=请解释量子计算的原理 err=context deadline exceeded <span class="log-stats">[RPM:45 QPS:2 HourlyErr:4 HourlyReq:2650 ErrRate:0.15%]</span>
        </div>

        <h3>⚠️ 状态码错误日志</h3>
        <div class="log-example">
<span class="log-timestamp">2024-01-15 14:30:27.456</span> <span class="log-level-error">ERROR</span> request llm is error: status:500 Internal Server Error
reqbody: {
    "model": "gpt-4",
    "messages": [{"role": "user", "content": "分析这段代码的复杂度"}],
    "temperature": 0.3
}
respbody: {
    "error": {
        "message": "The server had an error while processing your request",
        "type": "server_error",
        "code": "internal_server_error"
    }
} <span class="log-stats">[RPM:45 QPS:2 HourlyErr:5 HourlyReq:2650 ErrRate:0.19%]</span>
        </div>

        <h3>📊 统计信息字段详解</h3>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>RPM: 45</h4>
                <p><strong>含义:</strong> 当前分钟已处理45个请求</p>
                <p><strong>计算:</strong> Redis Key <code>llm:stats:rpm:gpt-4:2024-01-15:14:30</code> 的值</p>
                <p><strong>用途:</strong> 监控请求频率，评估负载趋势</p>
            </div>
            <div class="feature-card">
                <h4>QPS: 2</h4>
                <p><strong>含义:</strong> 当前秒正在处理2个请求</p>
                <p><strong>计算:</strong> Redis Key <code>llm:stats:qps:gpt-4:2024-01-15:14:30:25</code> 的值</p>
                <p><strong>用途:</strong> 实时监控瞬时并发量</p>
            </div>
            <div class="feature-card">
                <h4>HourlyErr: 5</h4>
                <p><strong>含义:</strong> 当前小时累计5个错误</p>
                <p><strong>计算:</strong> Redis Key <code>llm:stats:error:gpt-4:request_error:2024-01-15:14</code> 的值</p>
                <p><strong>用途:</strong> 评估服务稳定性和错误频率</p>
            </div>
            <div class="feature-card">
                <h4>HourlyReq: 2650</h4>
                <p><strong>含义:</strong> 当前小时累计2650个请求</p>
                <p><strong>计算:</strong> 过去60分钟RPM值的总和 (45+52+...+38=2650)</p>
                <p><strong>用途:</strong> 计算错误率基数，评估整体负载</p>
            </div>
            <div class="feature-card">
                <h4>ErrRate: 0.19%</h4>
                <p><strong>含义:</strong> 错误率为0.19%</p>
                <p><strong>计算:</strong> (HourlyErr/HourlyReq) × 100% = (5/2650) × 100%</p>
                <p><strong>用途:</strong> 评估服务质量和稳定性指标</p>
            </div>
        </div>

        <h3>🔢 实际计算示例</h3>
        <div class="architecture-diagram">
            <h4 style="margin-top: 0; color: white;">📈 统计数据生成过程</h4>
            <div style="color: rgba(255,255,255,0.9); margin: 15px 0;">
                假设当前时间：2024-01-15 14:30:25，模型：gpt-4
            </div>

            <div class="flow-container flow-vertical">
                <div style="margin: 15px 0;">
                    <h5 style="color: rgba(255,255,255,0.9);">1️⃣ RPM计算</h5>
                    <div class="flow-container">
                        <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">
                            Key: llm:stats:rpm:gpt-4:2024-01-15:14:30<br/>
                            Value: 45 (当前分钟请求数)
                        </div>
                    </div>
                </div>

                <div style="margin: 15px 0;">
                    <h5 style="color: rgba(255,255,255,0.9);">2️⃣ QPS计算</h5>
                    <div class="flow-container">
                        <div class="flow-step" style="background: rgba(245, 158, 11, 0.3);">
                            Key: llm:stats:qps:gpt-4:2024-01-15:14:30:25<br/>
                            Value: 2 (当前秒请求数)
                        </div>
                    </div>
                </div>

                <div style="margin: 15px 0;">
                    <h5 style="color: rgba(255,255,255,0.9);">3️⃣ 小时请求数计算</h5>
                    <div class="flow-container">
                        <div class="flow-step" style="background: rgba(59, 130, 246, 0.3);">
                            13:31→14:30 (60分钟)<br/>
                            RPM总和: 45+52+...+38 = 2650
                        </div>
                    </div>
                </div>

                <div style="margin: 15px 0;">
                    <h5 style="color: rgba(255,255,255,0.9);">4️⃣ 错误率计算</h5>
                    <div class="flow-container">
                        <div class="flow-step" style="background: rgba(239, 68, 68, 0.3);">
                            错误数: 5<br/>
                            总请求: 2650<br/>
                            错误率: 5/2650 × 100% = 0.19%
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <h2>🔄 数据流转过程详解</h2>

        <div class="architecture-diagram">
            <h4 style="margin-top: 0; color: white;">📡 完整请求处理流程</h4>

            <!-- 第一阶段：请求接收和预处理 -->
            <div style="margin: 20px 0;">
                <h5 style="color: rgba(255,255,255,0.9); margin: 10px 0;">🚀 阶段1：请求接收和预处理</h5>
                <div class="flow-container">
                    <div class="flow-step">📥 接收HTTP请求</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">🔍 解析请求体</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">🏷️ 提取模型名称</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">📊 记录RPM/QPS</div>
                </div>
            </div>

            <!-- 第二阶段：LLM API调用 -->
            <div style="margin: 20px 0;">
                <h5 style="color: rgba(255,255,255,0.9); margin: 10px 0;">🤖 阶段2：LLM API调用</h5>
                <div class="flow-container">
                    <div class="flow-step">🔧 构建请求参数</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">🌐 发送HTTP请求</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">⏱️ 等待响应</div>
                </div>
            </div>

            <!-- 第三阶段：响应处理分支 -->
            <div style="margin: 20px 0;">
                <h5 style="color: rgba(255,255,255,0.9); margin: 10px 0;">🔀 阶段3：响应处理（分支逻辑）</h5>
                <div class="flow-container flow-vertical">
                    <div class="flow-step">📨 接收响应</div>
                    <span class="arrow">⬇️</span>
                    <div style="color: rgba(255,255,255,0.9); margin: 10px 0;">响应状态判断</div>

                    <div class="flow-container">
                        <!-- 成功分支 -->
                        <div class="flow-branch">
                            <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">✅ 200 OK</div>
                            <span class="arrow">⬇️</span>
                            <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">📊 记录成功统计</div>
                            <span class="arrow">⬇️</span>
                            <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">📝 输出成功日志</div>
                        </div>

                        <!-- 状态码错误分支 -->
                        <div class="flow-branch">
                            <div class="flow-step" style="background: rgba(239, 68, 68, 0.3);">❌ 4xx/5xx</div>
                            <span class="arrow">⬇️</span>
                            <div class="flow-step" style="background: rgba(239, 68, 68, 0.3);">📊 记录状态码统计</div>
                            <span class="arrow">⬇️</span>
                            <div class="flow-step" style="background: rgba(239, 68, 68, 0.3);">📝 输出错误日志</div>
                        </div>

                        <!-- 网络错误分支 -->
                        <div class="flow-branch">
                            <div class="flow-step" style="background: rgba(239, 68, 68, 0.3);">🔌 网络错误</div>
                            <span class="arrow">⬇️</span>
                            <div class="flow-step" style="background: rgba(239, 68, 68, 0.3);">📊 记录请求错误</div>
                            <span class="arrow">⬇️</span>
                            <div class="flow-step" style="background: rgba(239, 68, 68, 0.3);">📝 输出错误日志</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第四阶段：统计信息生成 -->
            <div style="margin: 20px 0;">
                <h5 style="color: rgba(255,255,255,0.9); margin: 10px 0;">📈 阶段4：统计信息生成和输出</h5>
                <div class="flow-container">
                    <div class="flow-step">🔍 查询Redis统计</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">🧮 计算错误率</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">📋 格式化统计信息</div>
                    <span class="arrow">→</span>
                    <div class="flow-step">📝 追加到日志</div>
                </div>
            </div>
        </div>

        <h2>⚙️ 核心实现代码详解</h2>

        <h3>📊 统计记录实现 - SSE客户端层面</h3>
        <div class="code-block" data-lang="Go"><span class="comment">// ==================== SSE客户端统计记录 ====================</span>

<span class="keyword">func</span> (c *Client) <span class="function">Send</span>(
    ctx context.Context,
    method Method,
    header <span class="type">map[string]string</span>,
    body <span class="type">map[string]interface{}</span>,
) (event &lt;-<span class="keyword">chan</span> *Event, err <span class="type">error</span>) {

    <span class="comment">// 🔍 解析请求体，获取JSON格式</span>
    marshal, err := json.<span class="function">Marshal</span>(body)
    <span class="keyword">if</span> err != <span class="keyword">nil</span> {
        <span class="keyword">return</span> <span class="keyword">nil</span>, err
    }

    <span class="comment">// 🏷️ 安全获取模型名称用于统计</span>
    modelName := <span class="string">""</span>
    <span class="keyword">if</span> val, ok := body[<span class="string">"model"</span>]; ok {
        <span class="keyword">if</span> strVal, ok := val.(<span class="type">string</span>); ok {
            modelName = strVal
        }
    }

    <span class="comment">// 📈 记录RPM和QPS统计（请求开始时立即记录）</span>
    chatDao := c.<span class="function">getChatDao</span>() <span class="comment">// 智能获取ChatDao实例</span>
    <span class="keyword">if</span> chatDao != <span class="keyword">nil</span> && modelName != <span class="string">""</span> {
        <span class="comment">// 使用goroutine异步记录，避免阻塞主流程</span>
        <span class="keyword">go</span> <span class="keyword">func</span>() {
            _ = chatDao.<span class="function">IncrementRPM</span>(ctx, modelName)
            _ = chatDao.<span class="function">IncrementQPS</span>(ctx, modelName)
        }()
    }

    <span class="comment">// 🌐 发送HTTP请求到LLM API</span>
    resp, err := c.client.<span class="function">R</span>().
        <span class="function">SetContext</span>(ctx).
        <span class="function">SetHeaders</span>(header).
        <span class="function">SetBody</span>(marshal).
        <span class="function">Execute</span>(<span class="type">string</span>(method), c.url)

    <span class="comment">// ❌ 处理请求错误（网络错误、超时等）</span>
    <span class="keyword">if</span> err != <span class="keyword">nil</span> {
        <span class="comment">// 记录错误统计</span>
        <span class="keyword">if</span> chatDao != <span class="keyword">nil</span> {
            <span class="keyword">go</span> <span class="keyword">func</span>() {
                _ = chatDao.<span class="function">IncrementError</span>(ctx, modelName, data.RequestErrorType)
            }()
        }

        <span class="comment">// 获取统计信息并记录到日志</span>
        statsInfo := c.<span class="function">getStatsInfo</span>(ctx, modelName)
        c.log.<span class="function">WithContext</span>(ctx).<span class="function">Errorf</span>(
            <span class="string">"request 模型名称:%v, error: %v, llm body %s %s"</span>,
            modelName, err, <span class="type">string</span>(marshal), statsInfo,
        )
        <span class="keyword">return</span> <span class="keyword">nil</span>, err
    }

    <span class="comment">// 🔍 检查HTTP状态码</span>
    <span class="keyword">if</span> resp.<span class="function">StatusCode</span>() != http.StatusOK {
        <span class="comment">// 记录状态码统计</span>
        <span class="keyword">if</span> chatDao != <span class="keyword">nil</span> {
            <span class="keyword">go</span> <span class="keyword">func</span>() {
                _ = chatDao.<span class="function">IncrementStatusCode</span>(ctx, modelName, resp.<span class="function">StatusCode</span>())
            }()
        }

        <span class="comment">// 获取统计信息并记录到日志</span>
        statsInfo := c.<span class="function">getStatsInfo</span>(ctx, modelName)
        c.log.<span class="function">WithContext</span>(ctx).<span class="function">Errorf</span>(
            <span class="string">"request llm is error: status:%v,reqbody:%v,respbody:%s %s"</span>,
            resp.<span class="function">Status</span>(), <span class="type">string</span>(marshal), resp.<span class="function">Body</span>(), statsInfo,
        )
        <span class="keyword">return</span> <span class="keyword">nil</span>, fmt.<span class="function">Errorf</span>(<span class="string">"HTTP %d: %s"</span>, resp.<span class="function">StatusCode</span>(), resp.<span class="function">Body</span>())
    }

    <span class="comment">// ✅ 请求成功，开始处理SSE流</span>
    <span class="keyword">go</span> c.<span class="function">RequestWithSSE</span>(ctx, body, resp, c.event, <span class="keyword">nil</span>)
    <span class="keyword">return</span> c.event, <span class="keyword">nil</span>
}</div>

        <h3>📈 统计信息获取和计算</h3>
        <div class="code-block" data-lang="Go"><span class="comment">// ==================== 统计信息获取实现 ====================</span>

<span class="comment">// ModelStats 模型统计信息结构体</span>
<span class="keyword">type</span> ModelStats <span class="keyword">struct</span> {
    ModelName        <span class="type">string</span> `json:<span class="string">"model_name"</span>`        <span class="comment">// 模型名称</span>
    CurrentRPM       <span class="type">int64</span>  `json:<span class="string">"current_rpm"</span>`        <span class="comment">// 当前分钟请求数</span>
    CurrentQPS       <span class="type">int64</span>  `json:<span class="string">"current_qps"</span>`        <span class="comment">// 当前秒请求数</span>
    HourlyErrorCount <span class="type">int64</span>  `json:<span class="string">"hourly_error_count"</span>` <span class="comment">// 当前小时错误数</span>
    HourlyReqCount   <span class="type">int64</span>  `json:<span class="string">"hourly_req_count"</span>`   <span class="comment">// 当前小时请求数</span>
    ErrorRate        <span class="type">string</span> `json:<span class="string">"error_rate"</span>`         <span class="comment">// 错误率</span>
}

<span class="comment">// GetModelStats 获取模型的综合统计信息</span>
<span class="keyword">func</span> (g ChatDao) <span class="function">GetModelStats</span>(
    ctx context.Context,
    modelName <span class="type">string</span>,
) (*ModelStats, <span class="type">error</span>) {
    stats := &ModelStats{
        ModelName: modelName,
    }

    <span class="comment">// 📊 获取当前分钟RPM</span>
    rpm, err := g.<span class="function">GetCurrentRPM</span>(ctx, modelName)
    <span class="keyword">if</span> err != <span class="keyword">nil</span> && !errors.<span class="function">Is</span>(err, redis.Nil) {
        g.log.<span class="function">WithContext</span>(ctx).<span class="function">Warnf</span>(<span class="string">"获取RPM失败: %v"</span>, err)
    }
    stats.CurrentRPM = rpm

    <span class="comment">// ⚡ 获取当前秒QPS</span>
    qps, err := g.<span class="function">GetCurrentQPS</span>(ctx, modelName)
    <span class="keyword">if</span> err != <span class="keyword">nil</span> && !errors.<span class="function">Is</span>(err, redis.Nil) {
        g.log.<span class="function">WithContext</span>(ctx).<span class="function">Warnf</span>(<span class="string">"获取QPS失败: %v"</span>, err)
    }
    stats.CurrentQPS = qps

    <span class="comment">// ❌ 获取当前小时错误数</span>
    errorCount, err := g.<span class="function">GetHourlyErrorCount</span>(ctx, modelName, RequestErrorType)
    <span class="keyword">if</span> err != <span class="keyword">nil</span> && !errors.<span class="function">Is</span>(err, redis.Nil) {
        g.log.<span class="function">WithContext</span>(ctx).<span class="function">Warnf</span>(<span class="string">"获取错误计数失败: %v"</span>, err)
    }
    stats.HourlyErrorCount = errorCount

    <span class="comment">// 🧮 计算当前小时总请求数（通过RPM累加）</span>
    now := time.<span class="function">Now</span>()
    totalReq := <span class="type">int64</span>(0)
    <span class="keyword">for</span> i := 0; i &lt; 60; i++ { <span class="comment">// 遍历过去60分钟</span>
        minute := now.<span class="function">Add</span>(-time.<span class="function">Duration</span>(i) * time.Minute)
        key := fmt.<span class="function">Sprintf</span>(
            RPMKeyFormat,
            StatsRPMPrefix,
            modelName,
            minute.<span class="function">Format</span>(DateFormat),
            minute.<span class="function">Hour</span>(),
            minute.<span class="function">Minute</span>(),
        )

        count, err := g.data.rdb.<span class="function">Get</span>(ctx, key).<span class="function">Int64</span>()
        <span class="keyword">if</span> err == <span class="keyword">nil</span> {
            totalReq += count
        }
    }
    stats.HourlyReqCount = totalReq

    <span class="comment">// 📈 计算错误率</span>
    <span class="keyword">if</span> totalReq > 0 {
        errorRate := <span class="type">float64</span>(errorCount) / <span class="type">float64</span>(totalReq) * 100
        stats.ErrorRate = fmt.<span class="function">Sprintf</span>(<span class="string">"%.2f%%"</span>, errorRate)
    } <span class="keyword">else</span> {
        stats.ErrorRate = <span class="string">"0.00%"</span>
    }

    <span class="keyword">return</span> stats, <span class="keyword">nil</span>
}</div>

        <h3>🗄️ Redis操作实现详解</h3>
        <div class="code-block" data-lang="Go"><span class="comment">// ==================== Redis统计操作实现 ====================</span>

<span class="comment">// IncrementRPM 增加RPM计数</span>
<span class="keyword">func</span> (g ChatDao) <span class="function">IncrementRPM</span>(
    ctx context.Context,
    modelName <span class="type">string</span>,
) <span class="type">error</span> {
    now := time.<span class="function">Now</span>()
    key := fmt.<span class="function">Sprintf</span>(
        RPMKeyFormat,
        StatsRPMPrefix,            <span class="comment">// "llm:stats:rpm:"</span>
        modelName,                 <span class="comment">// 模型名称，如 "gpt-4"</span>
        now.<span class="function">Format</span>(DateFormat),    <span class="comment">// "2024-01-15"</span>
        now.<span class="function">Hour</span>(),               <span class="comment">// 14</span>
        now.<span class="function">Minute</span>(),             <span class="comment">// 30</span>
    )
    <span class="comment">// 最终key: "llm:stats:rpm:gpt-4:2024-01-15:14:30"</span>

    <span class="comment">// 🚀 使用Pipeline批量操作提高性能</span>
    pipe := g.data.rdb.<span class="function">Pipeline</span>()
    pipe.<span class="function">Incr</span>(ctx, key)                   <span class="comment">// 原子性递增</span>
    pipe.<span class="function">Expire</span>(ctx, key, 25*time.Hour)  <span class="comment">// 设置过期时间</span>
    _, err := pipe.<span class="function">Exec</span>(ctx)
    <span class="keyword">return</span> err
}

<span class="comment">// IncrementQPS 增加QPS计数</span>
<span class="keyword">func</span> (g ChatDao) <span class="function">IncrementQPS</span>(
    ctx context.Context,
    modelName <span class="type">string</span>,
) <span class="type">error</span> {
    now := time.<span class="function">Now</span>()
    key := fmt.<span class="function">Sprintf</span>(
        QPSKeyFormat,
        StatsQPSPrefix,            <span class="comment">// "llm:stats:qps:"</span>
        modelName,                 <span class="comment">// 模型名称</span>
        now.<span class="function">Format</span>(DateFormat),    <span class="comment">// 日期</span>
        now.<span class="function">Hour</span>(),               <span class="comment">// 小时</span>
        now.<span class="function">Minute</span>(),             <span class="comment">// 分钟</span>
        now.<span class="function">Second</span>(),             <span class="comment">// 秒</span>
    )
    <span class="comment">// 最终key: "llm:stats:qps:gpt-4:2024-01-15:14:30:25"</span>

    <span class="comment">// ⚡ QPS数据过期时间较短，只保留2小时</span>
    pipe := g.data.rdb.<span class="function">Pipeline</span>()
    pipe.<span class="function">Incr</span>(ctx, key)
    pipe.<span class="function">Expire</span>(ctx, key, 2*time.Hour)
    _, err := pipe.<span class="function">Exec</span>(ctx)
    <span class="keyword">return</span> err
}

<span class="comment">// IncrementError 增加错误计数</span>
<span class="keyword">func</span> (g ChatDao) <span class="function">IncrementError</span>(
    ctx context.Context,
    modelName <span class="type">string</span>,
    errorType <span class="type">string</span>,
) <span class="type">error</span> {
    now := time.<span class="function">Now</span>()
    key := fmt.<span class="function">Sprintf</span>(
        ErrorKeyFormat,
        StatsErrorPrefix,          <span class="comment">// "llm:stats:error:"</span>
        modelName,                 <span class="comment">// 模型名称</span>
        errorType,                 <span class="comment">// 错误类型: "request_error" 或 "status_error"</span>
        now.<span class="function">Format</span>(DateFormat),    <span class="comment">// 日期</span>
        now.<span class="function">Hour</span>(),               <span class="comment">// 小时</span>
    )
    <span class="comment">// 最终key: "llm:stats:error:gpt-4:request_error:2024-01-15:14"</span>

    pipe := g.data.rdb.<span class="function">Pipeline</span>()
    pipe.<span class="function">Incr</span>(ctx, key)
    pipe.<span class="function">Expire</span>(ctx, key, 25*time.Hour)
    _, err := pipe.<span class="function">Exec</span>(ctx)
    <span class="keyword">return</span> err
}

<span class="comment">// IncrementStatusCode 增加状态码计数</span>
<span class="keyword">func</span> (g ChatDao) <span class="function">IncrementStatusCode</span>(
    ctx context.Context,
    modelName <span class="type">string</span>,
    statusCode <span class="type">int</span>,
) <span class="type">error</span> {
    now := time.<span class="function">Now</span>()
    key := fmt.<span class="function">Sprintf</span>(
        StatusKeyFormat,
        StatsStatusPrefix,         <span class="comment">// "llm:stats:status:"</span>
        modelName,                 <span class="comment">// 模型名称</span>
        statusCode,                <span class="comment">// HTTP状态码: 500, 429, 404等</span>
        now.<span class="function">Format</span>(DateFormat),    <span class="comment">// 日期</span>
        now.<span class="function">Hour</span>(),               <span class="comment">// 小时</span>
    )
    <span class="comment">// 最终key: "llm:stats:status:gpt-4:500:2024-01-15:14"</span>

    pipe := g.data.rdb.<span class="function">Pipeline</span>()
    pipe.<span class="function">Incr</span>(ctx, key)
    pipe.<span class="function">Expire</span>(ctx, key, 25*time.Hour)
    _, err := pipe.<span class="function">Exec</span>(ctx)
    <span class="keyword">return</span> err
}

<span class="comment">// ==================== Redis查询操作 ====================</span>

<span class="comment">// GetCurrentRPM 获取当前分钟的RPM</span>
<span class="keyword">func</span> (g ChatDao) <span class="function">GetCurrentRPM</span>(
    ctx context.Context,
    modelName <span class="type">string</span>,
) (<span class="type">int64</span>, <span class="type">error</span>) {
    now := time.<span class="function">Now</span>()
    key := fmt.<span class="function">Sprintf</span>(
        RPMKeyFormat,
        StatsRPMPrefix,
        modelName,
        now.<span class="function">Format</span>(DateFormat),
        now.<span class="function">Hour</span>(),
        now.<span class="function">Minute</span>(),
    )

    <span class="keyword">return</span> g.data.rdb.<span class="function">Get</span>(ctx, key).<span class="function">Int64</span>()
}

<span class="comment">// GetCurrentQPS 获取当前秒的QPS</span>
<span class="keyword">func</span> (g ChatDao) <span class="function">GetCurrentQPS</span>(
    ctx context.Context,
    modelName <span class="type">string</span>,
) (<span class="type">int64</span>, <span class="type">error</span>) {
    now := time.<span class="function">Now</span>()
    key := fmt.<span class="function">Sprintf</span>(
        QPSKeyFormat,
        StatsQPSPrefix,
        modelName,
        now.<span class="function">Format</span>(DateFormat),
        now.<span class="function">Hour</span>(),
        now.<span class="function">Minute</span>(),
        now.<span class="function">Second</span>(),
    )

    <span class="keyword">return</span> g.data.rdb.<span class="function">Get</span>(ctx, key).<span class="function">Int64</span>()
}</div>

        <h2>🎯 实际应用效果</h2>
        
        <div class="success">
            <h4>✅ 零配置使用</h4>
            <p>开发者无需修改任何现有代码，统计功能自动生效。所有通过 <code>llm.NewClient()</code> 创建的SSE客户端都会自动具备统计能力。</p>
        </div>

        <div class="success">
            <h4>✅ 实时监控</h4>
            <p>通过日志可以实时看到每个模型的请求量、错误率等关键指标，便于及时发现和处理问题。</p>
        </div>

        <div class="success">
            <h4>✅ 性能友好</h4>
            <p>使用Redis Pipeline批量操作，统计失败不会影响主业务流程，确保系统稳定性。</p>
        </div>

        <div class="success">
            <h4>✅ 高效的Hash结构设计</h4>
            <p><strong>🎯 当前方案的优势：</strong></p>
            <ul>
                <li><strong>极少Key数量：</strong> 10个模型每天仅产生约1500个Redis Hash</li>
                <li><strong>低内存消耗：</strong> Hash结构高效存储，大幅减少元数据开销</li>
                <li><strong>优秀性能：</strong> 减少过期处理压力，提升Redis整体性能</li>
                <li><strong>运维友好：</strong> 简化key管理，便于监控和维护</li>
            </ul>

            <p><strong>🔧 技术实现特点：</strong></p>
            <ul>
                <li><strong>Hash结构：</strong> 同一小时的数据存储在一个Hash中</li>
                <li><strong>时间聚合：</strong> QPS使用5分钟窗口，平衡精度和效率</li>
                <li><strong>自动过期：</strong> 合理的过期时间设置，自动清理历史数据</li>
                <li><strong>分层设计：</strong> 实时数据Redis，历史数据可选择持久化</li>
            </ul>
        </div>

        <h3>🔧 Hash结构实现示例</h3>
        <div class="code-block" data-lang="Go"><span class="comment">// ==================== Hash结构设计实现 ====================</span>

<span class="comment">// 使用Hash结构高效存储统计数据</span>
<span class="keyword">func</span> <span class="function">IncrementRPM</span>(ctx context.Context, modelName <span class="type">string</span>) <span class="type">error</span> {
    now := time.<span class="function">Now</span>()

    <span class="comment">// 🔑 Hash Key: 按小时分组</span>
    hashKey := fmt.<span class="function">Sprintf</span>(<span class="string">"llm:stats:rpm:%s:%s:%02d"</span>,
        modelName,                    <span class="comment">// "gpt-4"</span>
        now.<span class="function">Format</span>(<span class="string">"2006-01-02"</span>),      <span class="comment">// "2024-01-15"</span>
        now.<span class="function">Hour</span>())                   <span class="comment">// 14</span>
    <span class="comment">// Hash Key: "llm:stats:rpm:gpt-4:2024-01-15:14"</span>

    <span class="comment">// 🏷️ Hash Field: 具体的分钟</span>
    field := fmt.<span class="function">Sprintf</span>(<span class="string">"%02d"</span>, now.<span class="function">Minute</span>()) <span class="comment">// "30"</span>

    <span class="comment">// ⚡ 使用HINCRBY增加Hash字段的值</span>
    pipe := rdb.<span class="function">Pipeline</span>()
    pipe.<span class="function">HIncrBy</span>(ctx, hashKey, field, 1)      <span class="comment">// Hash字段+1</span>
    pipe.<span class="function">Expire</span>(ctx, hashKey, 25*time.Hour)   <span class="comment">// 整个Hash过期</span>
    _, err := pipe.<span class="function">Exec</span>(ctx)
    <span class="keyword">return</span> err
}

<span class="comment">// 获取当前分钟的RPM</span>
<span class="keyword">func</span> <span class="function">GetCurrentRPMOptimized</span>(ctx context.Context, modelName <span class="type">string</span>) (<span class="type">int64</span>, <span class="type">error</span>) {
    now := time.<span class="function">Now</span>()

    hashKey := fmt.<span class="function">Sprintf</span>(<span class="string">"llm:stats:rpm:%s:%s:%02d"</span>,
        modelName, now.<span class="function">Format</span>(<span class="string">"2006-01-02"</span>), now.<span class="function">Hour</span>())
    field := fmt.<span class="function">Sprintf</span>(<span class="string">"%02d"</span>, now.<span class="function">Minute</span>())

    <span class="comment">// 📊 从Hash中获取特定字段的值</span>
    <span class="keyword">return</span> rdb.<span class="function">HGet</span>(ctx, hashKey, field).<span class="function">Int64</span>()
}</div>

        <h3>📊 当前方案技术特点</h3>
        <table class="stats-table">
            <thead>
                <tr>
                    <th>技术特点</th>
                    <th>RPM统计</th>
                    <th>QPS统计</th>
                    <th>错误统计</th>
                    <th>状态码统计</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>存储结构</strong></td>
                    <td>Hash (60个分钟字段)</td>
                    <td>Hash (12个5分钟窗口)</td>
                    <td>Hash (错误类型字段)</td>
                    <td>Hash (状态码字段)</td>
                </tr>
                <tr>
                    <td><strong>Key数量/天</strong></td>
                    <td>240个 (10模型×24小时)</td>
                    <td>240个 (10模型×24小时)</td>
                    <td>240个 (10模型×24小时)</td>
                    <td>240个 (10模型×24小时)</td>
                </tr>
                <tr>
                    <td><strong>时间精度</strong></td>
                    <td>分钟级精确统计</td>
                    <td>5分钟窗口聚合</td>
                    <td>小时级统计</td>
                    <td>小时级统计</td>
                </tr>
                <tr>
                    <td><strong>过期时间</strong></td>
                    <td>25小时</td>
                    <td>2小时</td>
                    <td>25小时</td>
                    <td>25小时</td>
                </tr>
            </tbody>
        </table>

        <div class="success">
            <h4>✅ 当前实现优势</h4>
            <ul>
                <li><strong>高效存储：</strong> Hash结构大幅减少Redis key数量，提高存储效率</li>
                <li><strong>精确统计：</strong> RPM分钟级精度，QPS 5分钟窗口平衡实时性和效率</li>
                <li><strong>低内存占用：</strong> 10个模型仅需约1MB内存，资源使用极其高效</li>
                <li><strong>原子操作：</strong> 使用HINCRBY确保高并发环境下的数据一致性</li>
                <li><strong>自动过期：</strong> 合理的过期时间设置，自动清理历史数据</li>
            </ul>
        </div>

        <h2>📊 资源监控与告警策略</h2>

        <h3>🔍 关键监控指标</h3>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>💾 内存监控</h4>
                <p><strong>used_memory：</strong> Redis实际使用内存</p>
                <p><strong>used_memory_rss：</strong> 系统分配给Redis的内存</p>
                <p><strong>mem_fragmentation_ratio：</strong> 内存碎片率</p>
                <p><strong>告警阈值：</strong> 使用率 > 80%</p>
            </div>
            <div class="feature-card">
                <h4>🔢 Key数量监控</h4>
                <p><strong>keyspace_hits：</strong> Key命中次数</p>
                <p><strong>keyspace_misses：</strong> Key未命中次数</p>
                <p><strong>expired_keys：</strong> 过期Key数量</p>
                <p><strong>告警阈值：</strong> 总Key数 > 100万</p>
            </div>
            <div class="feature-card">
                <h4>⚡ 性能监控</h4>
                <p><strong>instantaneous_ops_per_sec：</strong> 每秒操作数</p>
                <p><strong>used_cpu_sys：</strong> 系统CPU使用率</p>
                <p><strong>used_cpu_user：</strong> 用户CPU使用率</p>
                <p><strong>告警阈值：</strong> CPU使用率 > 70%</p>
            </div>
            <div class="feature-card">
                <h4>🌐 连接监控</h4>
                <p><strong>connected_clients：</strong> 当前连接数</p>
                <p><strong>blocked_clients：</strong> 阻塞的客户端数</p>
                <p><strong>rejected_connections：</strong> 拒绝的连接数</p>
                <p><strong>告警阈值：</strong> 连接数 > 1000</p>
            </div>
        </div>

        <h3>📈 监控命令示例</h3>
        <div class="code-block" data-lang="Bash"><span class="comment"># ==================== Redis监控命令 ====================</span>

<span class="comment"># 查看内存使用情况</span>
redis-cli info memory

<span class="comment"># 查看Key统计信息</span>
redis-cli info keyspace

<span class="comment"># 查看性能统计</span>
redis-cli info stats

<span class="comment"># 查看特定模式的Key数量</span>
redis-cli eval <span class="string">"return #redis.call('keys', ARGV[1])"</span> 0 <span class="string">"llm:stats:*"</span>

<span class="comment"># 查看Hash的字段数量</span>
redis-cli hlen <span class="string">"llm:stats:rpm:gpt-4:2024-01-15:14"</span>

<span class="comment"># 查看Hash的内存使用</span>
redis-cli memory usage <span class="string">"llm:stats:rpm:gpt-4:2024-01-15:14"</span>

<span class="comment"># 实时监控Redis操作</span>
redis-cli monitor</div>

        <h3>🚨 告警配置建议</h3>
        <table class="stats-table">
            <thead>
                <tr>
                    <th>告警类型</th>
                    <th>监控指标</th>
                    <th>告警阈值</th>
                    <th>告警级别</th>
                    <th>处理建议</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>内存告警</strong></td>
                    <td>used_memory_percentage</td>
                    <td>> 80%</td>
                    <td>警告</td>
                    <td>检查数据过期策略，考虑扩容</td>
                </tr>
                <tr>
                    <td><strong>内存严重告警</strong></td>
                    <td>used_memory_percentage</td>
                    <td>> 90%</td>
                    <td>严重</td>
                    <td>立即扩容或清理数据</td>
                </tr>
                <tr>
                    <td><strong>Key数量告警</strong></td>
                    <td>total_keys</td>
                    <td>> 100万</td>
                    <td>警告</td>
                    <td>检查Key设计，优化数据结构</td>
                </tr>
                <tr>
                    <td><strong>性能告警</strong></td>
                    <td>used_cpu_sys</td>
                    <td>> 70%</td>
                    <td>警告</td>
                    <td>检查慢查询，优化操作</td>
                </tr>
                <tr>
                    <td><strong>连接告警</strong></td>
                    <td>connected_clients</td>
                    <td>> 1000</td>
                    <td>警告</td>
                    <td>检查连接池配置</td>
                </tr>
            </tbody>
        </table>

        <h3>🔧 优化建议</h3>
        <div class="architecture-diagram">
            <h4 style="margin-top: 0; color: white;">🎯 持续优化策略</h4>

            <div class="flow-container flow-vertical">
                <div style="margin: 15px 0;">
                    <h5 style="color: rgba(255,255,255,0.9);">📊 数据分层策略</h5>
                    <div class="flow-container">
                        <div class="flow-step" style="background: rgba(34, 197, 94, 0.3);">
                            实时数据<br/>
                            (当前小时)<br/>
                            Redis Hash
                        </div>
                        <span class="arrow">→</span>
                        <div class="flow-step" style="background: rgba(245, 158, 11, 0.3);">
                            近期数据<br/>
                            (24小时内)<br/>
                            Redis + 压缩
                        </div>
                        <span class="arrow">→</span>
                        <div class="flow-step" style="background: rgba(156, 163, 175, 0.3);">
                            历史数据<br/>
                            (7天以上)<br/>
                            数据库存储
                        </div>
                    </div>
                </div>

                <div style="margin: 15px 0;">
                    <h5 style="color: rgba(255,255,255,0.9);">⏰ 自动化运维</h5>
                    <div class="flow-container">
                        <div class="flow-step" style="background: rgba(59, 130, 246, 0.3);">
                            定时清理<br/>
                            过期数据
                        </div>
                        <div class="flow-step" style="background: rgba(59, 130, 246, 0.3);">
                            自动备份<br/>
                            关键统计
                        </div>
                        <div class="flow-step" style="background: rgba(59, 130, 246, 0.3);">
                            性能监控<br/>
                            自动告警
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="warning">
            <h4>⚠️ 运维注意事项</h4>
            <ul>
                <li><strong>内存管理：</strong> 定期监控Redis内存使用，设置合理的maxmemory限制</li>
                <li><strong>Key管理：</strong> 监控Key数量增长趋势，及时发现异常</li>
                <li><strong>性能监控：</strong> 关注慢查询日志，优化高频操作</li>
                <li><strong>数据备份：</strong> 配置Redis持久化，定期备份关键统计数据</li>
                <li><strong>容量规划：</strong> 根据业务增长预测，提前进行容量规划</li>
                <li><strong>故障恢复：</strong> 制定Redis故障恢复预案，确保服务连续性</li>
            </ul>
        </div>

        <h2>📈 监控建议</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>🚨 告警设置</h4>
                <ul>
                    <li>RPM超过阈值告警</li>
                    <li>错误率超过5%告警</li>
                    <li>连续500状态码告警</li>
                </ul>
            </div>
            <div class="feature-card">
                <h4>📊 数据分析</h4>
                <ul>
                    <li>使用ELK解析统计日志</li>
                    <li>建立Dashboard展示</li>
                    <li>生成定期报告</li>
                </ul>
            </div>
            <div class="feature-card">
                <h4>🔧 性能优化</h4>
                <ul>
                    <li>根据RPM/QPS调整负载均衡</li>
                    <li>根据错误率优化模型配置</li>
                    <li>根据状态码排查系统问题</li>
                </ul>
            </div>
        </div>

        <h2>🎉 功能总结与价值</h2>

        <div class="feature-grid">
            <div class="feature-card">
                <h4>🚀 技术创新</h4>
                <ul>
                    <li><strong>全局ChatDao设计</strong>：避免参数传递复杂性</li>
                    <li><strong>智能回退机制</strong>：确保系统稳定性</li>
                    <li><strong>异步统计记录</strong>：不影响主业务性能</li>
                    <li><strong>Pipeline批量操作</strong>：提高Redis操作效率</li>
                </ul>
            </div>
            <div class="feature-card">
                <h4>📊 监控价值</h4>
                <ul>
                    <li><strong>实时性能监控</strong>：RPM/QPS实时统计</li>
                    <li><strong>错误率追踪</strong>：快速发现系统问题</li>
                    <li><strong>状态码分析</strong>：定位具体错误类型</li>
                    <li><strong>历史趋势分析</strong>：支持性能优化决策</li>
                </ul>
            </div>
            <div class="feature-card">
                <h4>🛠️ 运维友好</h4>
                <ul>
                    <li><strong>零配置使用</strong>：开发者无感知</li>
                    <li><strong>日志集成</strong>：统计信息直接显示在日志中</li>
                    <li><strong>告警支持</strong>：可基于统计数据设置告警</li>
                    <li><strong>扩展性强</strong>：易于添加新的统计维度</li>
                </ul>
            </div>
            <div class="feature-card">
                <h4>🔒 稳定可靠</h4>
                <ul>
                    <li><strong>故障隔离</strong>：统计失败不影响主业务</li>
                    <li><strong>内存控制</strong>：合理的key过期时间设置</li>
                    <li><strong>性能优化</strong>：异步操作和批量处理</li>
                    <li><strong>向下兼容</strong>：不破坏现有代码结构</li>
                </ul>
            </div>
        </div>

        <div class="architecture-diagram">
            <h4 style="margin-top: 0; color: white;">🎯 核心价值体现</h4>
            <div class="flow-container">
                <div class="flow-step">📈 提升监控能力</div>
                <div class="flow-step">🔍 快速问题定位</div>
                <div class="flow-step">⚡ 优化系统性能</div>
                <div class="flow-step">🛡️ 保障服务稳定</div>
            </div>
            <div style="margin-top: 20px; color: rgba(255,255,255,0.9); font-size: 16px; line-height: 1.6;">
                通过Redis统计功能，我们实现了对LLM服务的<strong>全面监控</strong>，不仅能够<strong>实时了解</strong>各个模型的使用情况和性能表现，
                还能<strong>快速定位和解决问题</strong>。这个功能的设计充分考虑了<strong>易用性、性能和可维护性</strong>，
                为LLM服务的稳定运行提供了<strong>强有力的支撑</strong>。
            </div>
        </div>

        <div class="success">
            <h4>✨ 实施建议</h4>
            <ol>
                <li><strong>逐步部署</strong>：先在测试环境验证功能正常性</li>
                <li><strong>监控Redis</strong>：关注Redis内存使用和性能指标</li>
                <li><strong>设置告警</strong>：基于错误率和请求量设置合理的告警阈值</li>
                <li><strong>定期分析</strong>：利用统计数据进行性能分析和容量规划</li>
                <li><strong>持续优化</strong>：根据实际使用情况调整统计策略</li>
            </ol>
        </div>
    </div>
</body>
</html>
