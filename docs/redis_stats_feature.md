# Redis统计功能使用说明

## 功能概述

本功能通过Redis实现了对LLM模型请求的实时统计监控，包括RPM（每分钟请求数）、QPS（每秒请求数）、错误频率和状态码统计。统计信息会自动显示在日志中，方便运维人员监控模型性能和状态。

## 功能特性

### 1. RPM统计 (每分钟请求数)
- **Redis Key格式**: `llm:stats:rpm:{model}:{date}:{hour}:{minute}`
- **过期时间**: 25小时
- **用途**: 监控每个模型每分钟的请求量

### 2. QPS统计 (每秒请求数)
- **Redis Key格式**: `llm:stats:qps:{model}:{date}:{hour}:{minute}:{second}`
- **过期时间**: 2小时
- **用途**: 监控每个模型每秒的请求量

### 3. 错误统计
- **Redis Key格式**: `llm:stats:error:{model}:{errorType}:{date}:{hour}`
- **过期时间**: 25小时
- **错误类型**:
  - `request_error`: 请求错误
  - `status_error`: 状态码错误

### 4. 状态码统计
- **Redis Key格式**: `llm:stats:status:{model}:{status}:{date}:{hour}`
- **过期时间**: 25小时
- **用途**: 统计各种HTTP状态码的出现频率

## 日志输出示例

### 正常请求日志
```
sse common req: {...}, type:1, desc:gpt-4, req_times_1 [RPM:45 QPS:2 HourlyErr:3 HourlyReq:2650 ErrRate:0.11%]
```

### 错误请求日志
```
request 模型名称:gpt-4 , error: context deadline exceeded , llm body {...} [RPM:45 QPS:2 HourlyErr:4 HourlyReq:2650 ErrRate:0.15%]
```

### 状态码错误日志
```
request llm is error: status:500,reqbody:{...},respbody:Internal Server Error [RPM:45 QPS:2 HourlyErr:4 HourlyReq:2650 ErrRate:0.15%]
```

## 统计信息字段说明

- **RPM**: 当前分钟的请求数
- **QPS**: 当前秒的请求数  
- **HourlyErr**: 当前小时的错误数
- **HourlyReq**: 当前小时的总请求数
- **ErrRate**: 错误率百分比

## 实现原理

### 1. 全局ChatDao设计
- **全局实例**: 使用全局变量`globalChatDao`，避免在每个客户端中传递参数
- **自动初始化**: 在`SseUseCase`构造时自动设置全局实例
- **优雅降级**: 如果全局实例未设置，统计功能会静默失效，不影响主业务
- **向下兼容**: 支持客户端级别的ChatDao实例，优先级高于全局实例

### 2. 数据收集
- 在SSE客户端的`Send`方法中自动记录RPM和QPS
- 在请求失败时记录错误统计
- 在收到非200状态码时记录状态码统计
- 在`SseUseCase.logModelRequest`方法中记录业务层统计

### 3. 数据存储
- 使用Redis的INCR命令进行原子性计数
- 使用Pipeline批量操作提高性能
- 设置合理的过期时间避免数据堆积

### 4. 数据查询
- `GetCurrentRPM`: 获取当前分钟RPM
- `GetCurrentQPS`: 获取当前秒QPS
- `GetHourlyErrorCount`: 获取当前小时错误数
- `GetModelStats`: 获取综合统计信息

## 配置要求

### Redis配置
确保Redis服务正常运行，并且应用能够正常连接到Redis。

### 全局ChatDao配置
系统使用全局ChatDao实例来提供统计功能，无需在每个SSE客户端中单独配置：

1. **自动设置**: 在`SseUseCase`初始化时会自动设置全局ChatDao
2. **全局访问**: 所有SSE客户端都会自动使用全局ChatDao进行统计
3. **向下兼容**: 如果某个客户端有自己的ChatDao实例，会优先使用自己的

```go
// 在SseUseCase构造函数中自动设置
func NewSseUseCase(..., chatDao *data.ChatDao, ...) *SseUseCase {
    // 设置全局ChatDao，这样所有SSE客户端都可以使用统计功能
    llm.SetGlobalChatDao(chatDao)
    ...
}
```

## 监控建议

### 1. 设置告警阈值
- RPM超过预设阈值时告警
- 错误率超过5%时告警
- 连续出现500状态码时告警

### 2. 日志分析
- 使用ELK或类似工具解析统计日志
- 建立Dashboard展示实时统计数据
- 设置定期报告

### 3. 性能优化
- 根据RPM/QPS数据调整负载均衡策略
- 根据错误率数据优化模型配置
- 根据状态码统计排查系统问题

## 故障排查

### 1. 统计数据不准确
- 检查Redis连接是否正常
- 检查系统时间是否同步
- 检查Redis内存是否充足

### 2. 日志中没有统计信息
- 检查`ChatDao`是否正确注入
- 检查Redis服务是否正常
- 检查日志级别配置

### 3. 性能影响
- Redis操作使用了Pipeline批量处理
- 统计操作是异步的，不会阻塞主流程
- 如有性能问题，可以考虑采样统计

## 扩展功能

### 1. 自定义统计维度
可以根据需要添加更多统计维度，如：
- 按用户ID统计
- 按业务类型统计
- 按地域统计

### 2. 历史数据分析
- 实现按天、按周、按月的统计汇总
- 提供趋势分析接口
- 支持数据导出功能

### 3. 实时告警
- 集成告警系统
- 支持多种告警方式（邮件、短信、钉钉等）
- 提供告警规则配置界面
