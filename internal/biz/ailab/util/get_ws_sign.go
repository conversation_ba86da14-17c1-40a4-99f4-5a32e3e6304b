package util

import (
	"errors"
)

// GetWsSign 获取WS鉴权URL
func GetWsSign(
	accessKeyId string,
	accessKeySecret string,
	timestamp string,
	requestUrl string,
	urlParams map[string]string) (string, error) {

	if StrIsEmpty(accessKeyId) {
		return "", errors.New("参数access_key_id不能为空")
	}
	if StrIsEmpty(accessKeySecret) {
		return "", errors.New("参数access_key_secret不能为空")
	}
	if StrIsEmpty(timestamp) {
		return "", errors.New("参数timestamp不能为空")
	}
	if StrIsEmpty(requestUrl) {
		return "", errors.New("参数requestUrl不能为空")
	}
	if urlParams == nil {
		return "", errors.New("参数urlParams不能为null,会带回签名，至少做初始化")
	}

	urlParams["access_key_id"] = accessKeyId
	urlParams["timestamp"] = timestamp

	signature, signatureNonce := GetSignature(urlParams,
		nil,
		"GET",
		"application/json",
		accessKeySecret)

	urlParams["signature"] = signature
	urlParams["signature_nonce"] = signatureNonce
	urlParams["timestamp"] = timestamp

	requestUrl = requestUrl + "?" + UrlFormat(urlParams)
	return requestUrl, nil

}

// GetSseSign sse 鉴权 url
func GetSseSign(
	accessKeyId string,
	accessKeySecret string,
	timestamp string,
	requestUrl string,
	urlParams map[string]string,
	bodyParams map[string]interface{}) (string, error) {

	if StrIsEmpty(accessKeyId) {
		return "", errors.New("参数access_key_id不能为空")
	}
	if StrIsEmpty(accessKeySecret) {
		return "", errors.New("参数access_key_secret不能为空")
	}
	if StrIsEmpty(timestamp) {
		return "", errors.New("参数timestamp不能为空")
	}
	if StrIsEmpty(requestUrl) {
		return "", errors.New("参数requestUrl不能为空")
	}
	if urlParams == nil {
		return "", errors.New("参数urlParams不能为null,会带回签名，至少做初始化")
	}

	urlParams["access_key_id"] = accessKeyId
	urlParams["timestamp"] = timestamp

	signature, signatureNonce := GetSignature(urlParams,
		bodyParams,
		"POST",
		"application/json",
		accessKeySecret)

	urlParams["signature"] = signature
	urlParams["signature_nonce"] = signatureNonce
	urlParams["timestamp"] = timestamp

	requestUrl = requestUrl + "?" + UrlFormat(urlParams)
	return requestUrl, nil
}
