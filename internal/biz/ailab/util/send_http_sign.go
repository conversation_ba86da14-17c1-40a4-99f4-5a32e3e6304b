package util

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"net/http"
)

// SendRequest 计算签名并发送http请求
func SendRequest(
	accessKeyId string,
	accessKeySecret string,
	timestamp string,
	requestUrl string,
	urlParams map[string]string,
	bodyParams map[string]interface{},
	requestMethod string,
	contentType string) (*http.Response, error) {

	if StrIsEmpty(accessKeyId) {
		return nil, errors.New("参数access_key_id不能为空")
	}
	if StrIsEmpty(accessKeySecret) {
		return nil, errors.New("参数access_key_secret不能为空")
	}
	if StrIsEmpty(timestamp) {
		return nil, errors.New("参数timestamp不能为空")
	}
	if StrIsEmpty(requestUrl) {
		return nil, errors.New("参数requestUrl不能为空")
	}
	if urlParams == nil {
		return nil, errors.New("参数urlParams不能为null,会带回签名，至少做初始化")
	}
	if bodyParams == nil {
		bodyParams = make(map[string]interface{})
	}
	if StrIsEmpty(requestMethod) {
		return nil, errors.New("参数requestMethod不能为空")
	}
	if StrIsEmpty(contentType) {
		return nil, errors.New("参数contentType不能为空")
	}

	urlParams["access_key_id"] = accessKeyId
	urlParams["timestamp"] = timestamp

	signature, signatureNonce := GetSignature(urlParams,
		bodyParams,
		requestMethod,
		contentType,
		accessKeySecret)

	urlParams["signature"] = signature
	urlParams["signature_nonce"] = signatureNonce
	urlParams["timestamp"] = timestamp

	requestUrl = requestUrl + "?" + UrlFormat(urlParams)

	return DoStreamPost(requestUrl, contentType, bodyParams)
}

func DoStreamPost(url string, contentType string, bodyParams map[string]interface{}) (*http.Response, error) {
	var body io.Reader
	if contentType == ApplicationXWwwFormUrlencoded {
		body = bytes.NewBufferString(bodyFormat(bodyParams))
	} else if contentType == MultiPartFormData {
		//body := bodyParams[Multipart_formdata_body]
	} else if contentType == Binary {
		//body := bytes.NewBuffer(bodyParams[BinaryBody].([]byte))
	} else {
		bytesData, err := json.Marshal(bodyParams)
		if err != nil {
			return nil, errors.New("json.Marshal body_params error")
		}
		body = bytes.NewReader(bytesData)
	}

	client := &http.Client{}
	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", contentType)
	return client.Do(req)
}

func Post(url string, body []byte, appKeyStr, secretStr string) (*http.Response, error) {
	var err error

	var pbody map[string]interface{}

	if err = json.Unmarshal(body, &pbody); err != nil {
		return nil, err
	}
	urls := make(map[string]string)
	return SendRequest(
		appKeyStr, secretStr, GetCurrentDate(), url, urls,
		pbody, "POST", "application/json",
	)

}

func TalRequest(url string, body []byte, appKeyStr, secretStr string) (*http.Response, error) {
	var r *http.Response
	var err error
	r, err = Post(url, body, appKeyStr, secretStr)
	if err != nil {
		return nil, err
	}
	return r, nil
}
