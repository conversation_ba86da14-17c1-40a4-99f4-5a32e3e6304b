package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"llm_service/internal/biz/llm/azure4"
	"llm_service/internal/common"
	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"strings"
	"time"
)

type SSEAzure4 struct {
	confD *conf.Data
	log   *log.Helper
}

func NewSSEAzure4(confD *conf.Data, logger log.Logger) *SSEAzure4 {
	return &SSEAzure4{
		confD: confD,
		log:   log.NewHelper(logger),
	}
}

func (s *SSEAzure4) SseCommonChat(ctx context.Context, biz request.LlmBiz, chat dto.SSECommonChat) (eventCh <-chan dto.Data, err error) {
	apiKeys := s.confD.Llm.Azure.Gpt4ApiKeys
	s.log.WithContext(ctx).Infof("used gpt4_azure biz:%v chat_param: %v", biz, chat)
	if len(apiKeys) <= 0 {
		return nil, common.NewErr(common.ECode110050, errors.New("请检查账号配置"))
	}
	accountKeys := strings.Split(apiKeys[0], ":")
	if len(accountKeys) < 2 {
		return nil, common.NewErr(common.ECode110050, errors.New("请检查账号配置"))
	}
	options := []azure4.ClientOption{azure4.WithModel()}
	if chat.Temperature != 0 {
		options = append(options, azure4.WithTemperature(chat.Temperature))
	}
	// 配置
	openAiClient := azure4.NewClient(s.confD.Llm.Azure.Gpt4Url, time.Second*300, apiKeys[0], s.log, options...)
	ch, err := openAiClient.Send(ctx, azure4.AzureServer, chat.Prompt, chat.Message, chat.History)
	if err != nil {
		return nil, err
	}

	return ch, nil
}
