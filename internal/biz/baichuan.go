package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"llm_service/internal/biz/llm/baichuan"
	"llm_service/internal/common"
	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"time"
)

type SSEBaiChuan struct {
	confD *conf.Data
	log   *log.Helper
}

func NewSSEBaiChuan(confD *conf.Data, logger log.Logger) *SSEBaiChuan {
	return &SSEBaiChuan{
		confD: confD,
		log:   log.NewHelper(logger),
	}
}

func (s *SSEBaiChuan) SseCommonChat(ctx context.Context, biz request.LlmBiz, chat dto.SSECommonChat) (eventCh <-chan dto.Data, err error) {
	apiKey := s.confD.Llm.Baichuan.ApiKey
	s.log.WithContext(ctx).Infof("used sse_baichuan biz:%v chat_param: %v", biz, chat)
	if apiKey == "" {
		return nil, common.NewErr(common.ECode110050, errors.New("请检查账号配置"))
	}

	// 配置
	options := []baichuan.ClientOption{baichuan.WithModel(chat.Model)}
	if chat.Temperature != 0 {
		options = append(options, baichuan.WithTemperature(chat.Temperature))
	}

	if chat.WithSearchEnhance != nil {
		options = append(options, baichuan.WithSearchEnhance(*chat.WithSearchEnhance))
	}

	client := baichuan.NewClient(s.confD.Llm.Baichuan.Url, time.Second*300, apiKey, s.log, options...)
	ch, err := client.Send(ctx, baichuan.AiServiceServer, chat.Message, chat.History)
	if err != nil {
		return nil, err
	}
	return ch, nil
}
