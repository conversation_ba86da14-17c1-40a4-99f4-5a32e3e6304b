package biz

import (
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"io/ioutil"
	"llm_service/internal/biz/llm/baidu"
	"llm_service/internal/conf"
	"llm_service/internal/data"
	"llm_service/internal/data/ailab/util"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"net/http"
	"strings"
	"time"
)

type Server string

const (
	AiServiceServer Server = "baidu"
)

type SSEBaidu struct {
	confD *conf.Data
	repo  *data.ChatDao
	log   *log.Helper
}

func NewSSEBaidu(confD *conf.Data, repo *data.ChatDao, logger log.Logger) *SSEBaidu {
	return &SSEBaidu{
		confD: confD,
		repo:  repo,
		log:   log.NewHelper(logger),
	}
}

func (s *SSEBaidu) SseCommonChat(ctx context.Context, biz request.LlmBiz, chat dto.SSECommonChat) (eventCh <-chan dto.Data, err error) {
	s.log.WithContext(ctx).Infof("used sse_baidu biz:%v chat_param: %v", biz, chat)
	token, err := s.GetWenXinToken(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("获取百度文心的token失败 %v", errors.WithStack(err))
		return nil, err
	}
	if chat.Version == "" {
		chat.Version = "v1"
	}
	// 配置
	options := []baidu.ClientOption{baidu.WithStream(true)}
	chat.Message = chat.Prompt
	baiduClient := baidu.NewClient(time.Second*300, chat.Version, token, s.log, options...)
	ch, err := baiduClient.Send(ctx, chat.Message, chat.History)
	if err != nil {
		s.log.WithContext(ctx).Errorf("used baidu biz:%v chat_param: %v", biz, chat)
		return nil, err
	}
	return ch, nil
}

func (s *SSEBaidu) GetWenXinToken(ctx context.Context) (string, error) {
	token, err := s.repo.GetWenXinTokenByCache(ctx)
	//token不存在或者异常，过期
	if len(token) == 0 || err != nil {
		s.log.WithContext(ctx).Warnf("获取缓存中百度文心一言token失败%v", err)
		urlMap := make(map[string]string)
		urlMap["client_id"] = s.confD.Llm.Baidu.ClientId
		urlMap["client_secret"] = s.confD.Llm.Baidu.ClientSecret
		urlMap["grant_type"] = s.confD.Llm.Baidu.GrantType
		format := util.UrlFormat(urlMap)

		url := s.confD.Llm.Baidu.Url + "?" + format
		payload := strings.NewReader(``)
		client := &http.Client{}
		req, err := http.NewRequest("POST", url, payload)

		if err != nil {
			s.log.WithContext(ctx).Errorf("获取百度文心一言token失败%v", err)
			return "", err
		}
		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Accept", "application/json")

		res, err := client.Do(req)
		if err != nil {
			s.log.WithContext(ctx).Errorf("send获取百度文心一言token失败%v", err)
			return "", err
		}
		defer res.Body.Close()

		body, err := ioutil.ReadAll(res.Body)
		if err != nil {
			s.log.WithContext(ctx).Errorf("get获取百度文心一言token失败%v", err)
			return "", err
		}
		type BaiduToken struct {
			AccessToken string `json:"access_token"`
		}
		var baiduToken BaiduToken
		err = json.Unmarshal(body, &baiduToken)
		if err != nil {
			s.log.WithContext(ctx).Errorf("解析百度文心一言token失败%v , resp=%s", err, string(body))
			return "", err
		}
		s.repo.SetWenXinTokenByCache(ctx, baiduToken.AccessToken)
		return baiduToken.AccessToken, nil
	}
	return token, nil
}
