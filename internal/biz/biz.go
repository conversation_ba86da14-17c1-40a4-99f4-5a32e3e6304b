package biz

import (
	"llm_service/internal/biz/legal"
	"llm_service/internal/biz/llm"
	"llm_service/internal/biz/llm/tal_middleground"
	"llm_service/internal/biz/llm/tal_ws"
	"llm_service/internal/biz/prompt"

	"github.com/google/wire"
)

// ProviderSet is biz providers.
var ProviderSet = wire.NewSet(
	NewSseUseCase,
	llm.NewLLMBaseUseCase,
	tal_ws.NewTalUseCase,
	prompt.NewPrompt,
	NewSSEBaiChuan,
	NewSSEErnie,
	NewSSETalComposition,
	NewSSETongYiQwenMaxOpenAi,
	NewSSETongYiQwenPlusOpenAi,
	NewSSETongYiQwen72BOpenAi,
	NewSSEDouBaoPro32kOpenAi,
	NewSSEDouBaoLite32kOpenAi,
	legal.NewLegalBizUseCase,
	NewMultiModalClient,
	NewSSELlama370bModelSquare,
	NewSSEAzure4,
	NewSSEGPT4Turbo,
	NewSSEGPT4Exclusive,
	NewSSETal70B,
	NewSSETal7B,
	NewSSEBaidu,
	NewSSETalMathGPTBaseBackend,
	NewSseLlama370b,
	NewSSETalEnDialogue,
	NewSSEDeepSeekReasonerOpenAi,
	NewSSEDeepSeekDistillQwen32BOpenAi,
	tal_middleground.NewTalMiddleGroundUseCase,
	NewSSETalMiddleGround,
	NewSSEClaude37SonnetOpenAi,
)
