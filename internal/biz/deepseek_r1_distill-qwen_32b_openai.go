package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"llm_service/internal/biz/llm/openAICommon"
	"llm_service/internal/common"
	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"time"
)

type SSEDeepSeekDistillQwen32BOpenAi struct {
	confD *conf.Data
	log   *log.Helper
}

func NewSSEDeepSeekDistillQwen32BOpenAi(confD *conf.Data, logger log.Logger) *SSEDeepSeekDistillQwen32BOpenAi {
	return &SSEDeepSeekDistillQwen32BOpenAi{
		confD: confD,
		log:   log.NewHelper(logger),
	}
}

func (s *SSEDeepSeekDistillQwen32BOpenAi) SseCommonChat(ctx context.Context, biz request.LlmBiz, chat dto.SSECommonChat) (eventCh <-chan dto.Data, err error) {
	if chat.ApiKey == "" {
		return nil, common.NewErr(common.ECode110050, errors.New("请检查账号配置"))
	}
	s.log.WithContext(ctx).Infof("used sse_deepseek_r1_distill-qwen_32b_openai biz:%v chat_param: %v", biz, chat)
	options := []openAICommon.ClientOption{openAICommon.WithModel(chat.Model)}
	if chat.Temperature != 0 {
		options = append(options, openAICommon.WithTemperature(chat.Temperature))
	} else if s.confD.Llm.DeepSeek.DeepSeekR1DistillQwen32B.Temperature != 0 {
		options = append(options, openAICommon.WithTemperature(s.confD.Llm.DeepSeek.DeepSeekR1DistillQwen32B.Temperature))
	}
	options = append(options, openAICommon.WithStreamOptionsIncludeUsage(true))
	options = append(options, openAICommon.WithStream(true))

	client := openAICommon.NewOpenAIClient(chat.URL, chat.ApiKey, common.GptTypeMap[common.GptTypeDeepSeekR1DistillQwen32B], time.Second*60, s.log, options...)
	ch, err := client.Send(ctx, chat.Prompt, chat.Message, common.GptTypeMap[common.GptTypeDeepSeekR1DistillQwen32B], chat.History)
	if err != nil {
		return nil, err
	}
	return ch, nil
}
