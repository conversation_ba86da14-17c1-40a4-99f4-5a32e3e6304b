package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"llm_service/internal/biz/llm/openAICommon"
	"llm_service/internal/common"
	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"time"
)

type SSEDouBaoPro32kOpenAi struct {
	confD *conf.Data
	log   *log.Helper
}

func NewSSEDouBaoPro32kOpenAi(confD *conf.Data, logger log.Logger) *SSEDouBaoPro32kOpenAi {
	return &SSEDouBaoPro32kOpenAi{
		confD: confD,
		log:   log.NewHelper(logger),
	}
}

func (s *SSEDouBaoPro32kOpenAi) SseCommonChat(ctx context.Context, biz request.LlmBiz, chat dto.SSECommonChat) (eventCh <-chan dto.Data, err error) {
	if chat.ApiKey == "" {
		return nil, common.NewErr(common.ECode110050, errors.New("请检查账号配置"))
	}
	s.log.WithContext(ctx).Infof("used sse_doubao_pro_32k biz:%v chat_param: %v", biz, chat)
	options := []openAICommon.ClientOption{openAICommon.WithModel(chat.Model)}
	if chat.Temperature != 0 {
		options = append(options, openAICommon.WithTemperature(chat.Temperature))
	} else if s.confD.Llm.Doubao.DouBaoPro32K.Temperature != 0 {
		options = append(options, openAICommon.WithTemperature(s.confD.Llm.Doubao.DouBaoLite32K.Temperature))
	}
	options = append(options, openAICommon.WithStreamOptionsIncludeUsage(true))
	options = append(options, openAICommon.WithStream(true))
	if chat.EnableSearch {
		options = append(options, openAICommon.WithEnableSearch(true))
	}

	client := openAICommon.NewOpenAIClient(chat.URL, chat.ApiKey, common.GptTypeMap[common.GptTypeDouBaoPro32k], time.Second*600, s.log, options...)
	ch, err := client.Send(ctx, chat.Prompt, chat.Message, common.GptTypeMap[common.GptTypeDouBaoPro32k], chat.History)
	if err != nil {
		return nil, err
	}
	return ch, nil
}
