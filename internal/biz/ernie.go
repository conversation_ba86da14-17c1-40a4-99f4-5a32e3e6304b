package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"llm_service/internal/biz/llm/ernie"
	"llm_service/internal/common"
	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"time"
)

type SSEErnie struct {
	confD *conf.Data
	log   *log.Helper
}

func NewSSEErnie(confD *conf.Data, logger log.Logger) *SSEErnie {
	return &SSEErnie{
		confD: confD,
		log:   log.NewHelper(logger),
	}
}

func (s *SSEErnie) SseCommonChat(ctx context.Context, biz request.LlmBiz, chat dto.SSECommonChat) (eventCh <-chan dto.Data, err error) {
	if chat.ApiKey == "" {
		return nil, common.NewErr(common.ECode110050, errors.New("请检查账号配置"))
	}
	s.log.WithContext(ctx).Infof("used sse_ernie biz:%v chat_param: %v", biz, chat)

	// 配置
	options := []ernie.ClientOption{ernie.WithModel(chat.Model)}
	if chat.Temperature != 0 {
		options = append(options, ernie.WithTemperature(chat.Temperature))
	}
	if chat.Prompt != "" {
		options = append(options, ernie.WithSystem(chat.Prompt))
	}
	client := ernie.NewClient(chat.URL, time.Second*300, chat.ApiKey, s.log, options...)
	ch, err := client.Send(ctx, ernie.AiServiceServer, chat.Message, chat.History)
	if err != nil {
		s.log.WithContext(ctx).Errorf("used sse_ernie biz:%v chat_param:%v,err:%v", biz, chat, err)
		return nil, err
	}
	return ch, nil
}
