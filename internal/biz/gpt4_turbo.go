package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"llm_service/internal/biz/llm/gpt4_turbo"
	"llm_service/internal/common"
	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"strings"
	"time"
)

type SSEGPT4Turbo struct {
	confD *conf.Data
	log   *log.Helper
}

func NewSSEGPT4Turbo(confD *conf.Data, logger log.Logger) *SSEGPT4Turbo {
	return &SSEGPT4Turbo{
		confD: confD,
		log:   log.<PERSON>elper(logger),
	}
}

func (s *SSEGPT4Turbo) SseCommonChat(ctx context.Context, biz request.LlmBiz, chat dto.SSECommonChat) (eventCh <-chan dto.Data, err error) {
	apiKeys := s.confD.Llm.Azure.Gpt4TurboApiKeys
	s.log.WithContext(ctx).Infof("used sse_gpt4_turbo biz:%v chat_param: %v", biz, chat)
	if len(apiKeys) <= 0 {
		return nil, common.NewErr(common.ECode110050, errors.New("请检查账号配置"))
	}
	accountKeys := strings.Split(apiKeys[0], ":")
	if len(accountKeys) < 2 {
		return nil, common.NewErr(common.ECode110050, errors.New("请检查账号配置"))
	}
	options := []gpt4_turbo.ClientOption{gpt4_turbo.WithModel()}
	if chat.Temperature != 0 {
		options = append(options, gpt4_turbo.WithTemperature(chat.Temperature))
	}
	// 配置
	openAiClient := gpt4_turbo.NewClient(s.confD.Llm.Azure.Gpt4TurboUrl, time.Second*300, apiKeys[0], s.log, options...)
	ch, err := openAiClient.Send(ctx, gpt4_turbo.OpenAiServer, chat.Prompt, chat.Message, chat.History)
	if err != nil {
		return nil, err
	}

	return ch, nil
}
