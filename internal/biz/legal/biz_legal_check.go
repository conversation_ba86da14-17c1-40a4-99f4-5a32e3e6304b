package legal

import (
	"context"
	"encoding/json"
	"fmt"
	"llm_service/internal/common"
	"llm_service/internal/conf"
	"llm_service/internal/dto/request"
	"strconv"
	"strings"
	"time"

	"git.100tal.com/znxx_xpp/go-libs/traces"
	"github.com/go-kratos/kratos/v2/log"
	"go.opentelemetry.io/otel/trace"
)

const (
	Input  = "input"
	Output = "output"

	BizLui         = 1
	BizZhZuowen    = 2
	BizEnZuowen    = 3
	BizEnKouYu     = 16
	BizSuperNotes  = 18
	BizCircle      = 19
	BizAIExplain   = 21
	ATYuWen        = 25
	XSBianCheng    = 29
	XSZYD          = 32 //写作引导
	BizLlm         = 100
	BizLlmDeepSeek = 1000000
)

var BizMap = map[request.LlmBiz]int{
	request.LlmBizLui:         BizLui,
	request.LlmBizKouYu:       BizEnKouYu,
	request.LlmBizENZuoWen:    BizEnZuowen,
	request.LlmBizZHZuoWen:    BizZh<PERSON>uowen,
	request.LlmBizSuperNotes:  BizSuperNotes,
	request.LlmBizCircle:      BizCircle,
	request.LlmBizAIExplain:   BizAIExplain,
	request.LlmBizATYuWen:     ATYuWen,
	request.LlmBizXSBianCheng: XSBianCheng,
	request.LlmBizXZYD:        XSZYD,
	request.LlmBizDeepSeek:    BizLlmDeepSeek,
}

type LegalBizUseCase struct {
	log  *log.Helper
	conf *conf.Data
}

func NewLegalBizUseCase(logger log.Logger, conf *conf.Data) *LegalBizUseCase {
	return &LegalBizUseCase{log: log.NewHelper(logger), conf: conf}
}

type CheckReq struct {
	Biz    request.LlmBiz `json:"biz"`
	Text   string         `json:"text"`
	Mode   string         `json:"mode"`
	ImgUrl string         `json:"img_url"`
	Sn     string         `json:"sn"` //sn
	TalId  string         `json:"talId"`
}

type CheckResp struct {
	ErrorCode int       `json:"error_code"`
	ErrorMsg  string    `json:"error_msg"`
	Data      CheckData `json:"data"`
	TraceId   string    `json:"trace_id"`
}

type CheckData struct {
	FilterText    string   `json:"filter_text"`
	RiskLabel     string   `json:"risk_label"`
	RiskLabelText string   `json:"risk_label_text"`
	RiskWords     []string `json:"risk_words"`
	RequestId     string   `json:"request_id"`
	TraceId       string   `json:"trace_id"`
}

func (nc *LegalBizUseCase) LegalCheck(ctx context.Context, req *CheckReq) (*CheckData, error) {
	nc.log.WithContext(ctx).Infof("LegalCheckInput, req:%+v", req)
	//llm biz 转化为 legal biz
	legalBiz := BizLlm
	if req.Biz >= request.LlmBizDeepSeek {
		legalBiz = request.LlmBizDeepSeek
	} else if val, ok := BizMap[req.Biz]; ok {
		legalBiz = val
	}

	legalConf, _ := nc.conf.ThirdParty.LegalCheck.BizMap[strconv.Itoa(legalBiz)]
	serviceId := legalConf.ServiceId
	mode := legalConf.Input
	if req.Mode == Output {
		mode = legalConf.Output
	}
	text := req.Text

	reqBody := map[string]interface{}{
		"biz":       legalBiz,
		"serviceId": serviceId,
		"mode":      mode,
		"text":      text,
		"sn":        req.Sn,
		"tal_id":    req.TalId,
	}

	if req.ImgUrl != "" {
		reqBody["type"] = "image"
		reqBody["text"] = req.ImgUrl
	}
	header := map[string]string{
		"Content-Type": "application/json",
		"trace_id":     ctx.Value(common.TraceId).(string),
		"Traceparent":  fmt.Sprintf("00-%s-%s-01", ctx.Value(common.TraceId).(string), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}

	jReq, _ := json.Marshal(reqBody)
	nc.log.WithContext(ctx).Infof("风控请求,url:%v,req: %v", nc.conf.ThirdParty.LegalCheck.Host, string(jReq))
	//主动清除空间占用
	jReq = []byte{}
	clt := traces.MakeHttpClient(nc.log).SetTimeout(10 * time.Second)
	resp, err := clt.R().SetHeaders(header).
		SetContext(ctx).
		SetBody(reqBody).
		Post(nc.conf.ThirdParty.LegalCheck.Host)
	if err != nil {
		if strings.Contains(err.Error(), common.ContextErr) {
			nc.log.WithContext(ctx).Warnf("调用LegalCheck 上下文关闭 err: %v", err.Error())
		} else {
			nc.log.WithContext(ctx).Errorf("调用LegalCheck失败, err: %v", err.Error())
		}
		// 接口失败，判定为通过
		return nil, nil
	}
	if resp.IsError() {
		nc.log.WithContext(ctx).Errorf("调用LegalCheck失败, err: %v", resp.Error())
		// 接口失败，判定为通过
		return nil, nil
	}

	var legalCheckResp CheckResp
	err = json.Unmarshal(resp.Body(), &legalCheckResp)
	if err != nil {
		nc.log.WithContext(ctx).Errorf("LegalCheck resp: %v，err: %v", resp, err)
		// 接口调用失败，判定为通过
		return nil, nil
	}

	startTime := ctx.Value(common.StartTime)
	cost := time.Since(startTime.(time.Time))
	nc.log.WithContext(ctx).Infof("LegalCheckOutput, resp:%+v cost:%v", legalCheckResp, cost.Milliseconds())

	// 风控，服务挂掉：120013，校验不通过：120012
	if legalCheckResp.ErrorCode != 0 {
		nc.log.WithContext(ctx).Infof("legalCheckRespIllegal resp: %+v", legalCheckResp)
		if legalCheckResp.ErrorCode == 120012 {
			return &legalCheckResp.Data, nil
		}
		return nil, nil
	}
	return nil, nil
}
