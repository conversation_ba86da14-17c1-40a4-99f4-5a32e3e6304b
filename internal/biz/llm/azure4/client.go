package azure4

import (
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pandodao/tokenizer-go"
	"github.com/pkg/errors"
	"llm_service/internal/biz/llm"
	"llm_service/internal/common"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"runtime/debug"
	"time"
)

type Server string

const (
	OpenAiServer Server = "openAi"
	AzureServer  Server = "azure"
)

type Model string

const (
	Gpt4 Model = "gpt-4"
)

func (m Model) String() string {
	return string(m)
}
func (s Server) GetHeader(apiKey string) map[string]string {
	switch s {
	case OpenAiServer:
		return map[string]string{
			"Authorization": "Bearer " + apiKey,
		}
	case AzureServer:
		return map[string]string{
			"api-key": apiKey,
		}
	default:
		return nil
	}
}

type Client struct {
	client    *llm.Client
	apiKey    string
	eventChan chan dto.Data
	option    *clientOption
	log       *log.Helper
}

type Result struct {
	Id      string   `json:"id"`
	Object  string   `json:"object"`
	Created int      `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
}

type Choice struct {
	Index        int    `json:"index"`
	FinishReason string `json:"finish_reason"`
	Delta        delta  `json:"delta"`
}

type delta struct {
	Content string `json:"content"`
}

func (r Result) GetIsEnd() bool {
	if len(r.Choices) <= 0 {
		return true
	}
	return len(r.Choices[0].FinishReason) > 0
}

func (r Result) GetContent() string {
	if len(r.Choices) <= 0 {
		return ""
	}
	return r.Choices[0].Delta.Content
}

func (r Result) GetTokens() int {
	if len(r.Choices) <= 0 {
		return 0
	}
	token, _ := tokenizer.CalToken(r.Choices[0].Delta.Content)
	return token
}

type clientOption struct {
	Model           Model             `json:"model"`
	Temperature     float32           `json:"temperature"`
	PresencePenalty float32           `json:"presence_penalty"`
	TopP            float32           `json:"top_p"`
	N               int               `json:"n"`
	Stream          bool              `json:"stream"`
	Messages        []request.Message `json:"messages"`
}

func (c *clientOption) GetJsonBody() (body map[string]interface{}, err error) {
	if c == nil {
		return nil, errors.New("*clientOption is nil")
	}
	marshal, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(marshal, &body)
	if err != nil {
		return nil, err
	}
	return body, err
}

type ClientOption func(o *clientOption)

func WithModel() ClientOption {
	return func(o *clientOption) {
		o.Model = Gpt4
	}
}

func WithPresencePenalty(presencePenalty float32) ClientOption {
	return func(o *clientOption) {
		o.PresencePenalty = presencePenalty
	}
}

func WithTemperature(temperature float32) ClientOption {
	return func(o *clientOption) {
		o.Temperature = temperature
	}
}

func WithTopP(p float32) ClientOption {
	return func(o *clientOption) {
		o.TopP = p
	}
}

func WithN(n int) ClientOption {
	return func(o *clientOption) {
		o.N = n
	}
}

func NewClient(url string, timeout time.Duration, apiKey string, log *log.Helper, options ...ClientOption) *Client {
	sseClient := llm.NewClient(url, timeout, log)
	var option = &clientOption{
		Stream:          true,
		Model:           Gpt4,
		Temperature:     1,
		TopP:            0.4,
		N:               1,
		PresencePenalty: 0,
	}
	for _, o := range options {
		o(option)
	}
	return &Client{
		client:    sseClient,
		apiKey:    apiKey,
		eventChan: make(chan dto.Data, 1024),
		option:    option,
		log:       log,
	}
}

func (c *Client) Send(ctx context.Context, server Server, prompt, message string, history []request.Message) (event <-chan dto.Data, err error) {
	if history == nil {
		c.log.WithContext(ctx).Infof("Azure4Gpt4: history 为空")
		history = make([]request.Message, 0)
	}
	if prompt != "" {
		newMessage := request.Message{
			Role:    request.SystemRole,
			Content: prompt,
		}
		// 将 newMessage 插入到 history 的第一个位置
		history = append([]request.Message{newMessage}, history...)
	} else {
		c.log.WithContext(ctx).Warnf("Azure4Gpt4: prompt 为空")
	}
	if message != "" {
		history = append(history, request.Message{
			Role:    request.UserRole,
			Content: message,
		})
	} else {
		c.log.WithContext(ctx).Warnf("Azure4Gpt4: message 为空")
	}
	c.option.Messages = history
	body, err := c.option.GetJsonBody()
	if err != nil {
		c.log.WithContext(ctx).Warnf("Azure4Gpt4: c.option.GetJsonBody() error:%v ", err)
		return nil, err
	}
	eventChan, err := c.client.Send(ctx, llm.PostMethod, server.GetHeader(c.apiKey), body)
	if err != nil {
		return nil, err
	}

	go func() {
		defer func() {
			close(c.eventChan)
			if err := recover(); err != nil {
				c.log.WithContext(ctx).Errorf("Azure4Gpt4: error, go routine panic错误：%v\n %s", err, debug.Stack())
				return
			}
		}()
		promptToken, _ := 0, 0 //tokenizer.CalToken(message)
		totalToken := 0
		for data := range eventChan {
			if data.Error != nil {
				// 存在异常，直接默认输出error兜底
				c.eventChan <- dto.Data{
					ErrorCode: common.OvertimeError,
					ErrorMsg:  common.OvertimeErrorMsg,
					IsEnd:     true,
				}
				return
			}

			var result Result
			err := json.Unmarshal([]byte(data.Data), &result)
			if err != nil {
				c.log.WithContext(ctx).Errorf("Azure4Gpt4: realData json marshal error：realData: %s, %s", data.Data, err.Error())
				continue
			}
			currentToken := 0 //result.GetTokens()
			totalToken += currentToken
			c.eventChan <- dto.Data{
				ErrorCode:  0,
				ErrorMsg:   "",
				Id:         result.Id,
				Object:     result.Object,
				Created:    result.Created,
				SentenceId: 0,
				IsEnd:      result.GetIsEnd(),
				Result:     result.GetContent(),
				Model:      result.Model,
				Usage: dto.Usage{
					PromptTokens:     promptToken,
					CompletionTokens: currentToken,
					TotalTokens:      totalToken,
				},
			}
			c.log.WithContext(ctx).Infof("Azure4Gpt4 real_data: %s", data.Data)

		}
	}()
	return c.eventChan, nil
}
