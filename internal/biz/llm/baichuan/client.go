package baichuan

import (
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"llm_service/internal/biz/llm"
	"llm_service/internal/common"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"runtime/debug"
	"time"
)

const (
	Baichuan2 = "Baichuan2-Turbo"
)

type Server string

const (
	BaiChuanServer  Server = "baichuan"
	AiServiceServer Server = "ai-service-baichuan"
)

func (s Server) GetHeader(apiKey string) map[string]string {
	switch s {
	case BaiChuanServer:
		return map[string]string{
			"Authorization": "Bearer " + apiKey,
		}
	case AiServiceServer:
		return map[string]string{
			"api-key": apiKey,
		}
	default:
		return nil
	}
}

type Client struct {
	client    *llm.Client
	apiKey    string
	eventChan chan dto.Data
	option    *clientOption
	log       *log.Helper
}

type Result struct {
	Id      string   `json:"id"`
	Object  string   `json:"object"`
	Created int      `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   Usage    `json:"usage"`
}

type Choice struct {
	Index        int    `json:"index"`
	FinishReason string `json:"finish_reason"`
	Delta        delta  `json:"delta"`
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

type delta struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

func (r Result) GetIsEnd() bool {
	if len(r.Choices) <= 0 {
		return true
	}
	return len(r.Choices[0].FinishReason) > 0
}

func (r Result) GetContent() string {
	if len(r.Choices) <= 0 {
		return ""
	}
	return r.Choices[0].Delta.Content
}

func (r Result) IsContentFilter() bool {
	if len(r.Choices) <= 0 {
		return false
	}
	return r.Choices[0].FinishReason == "content_filter"
}

type clientOption struct {
	Model             string            `json:"model"`
	Temperature       float32           `json:"temperature"`
	WithSearchEnhance bool              `json:"with_search_enhance"`
	Stream            bool              `json:"stream"`
	Messages          []request.Message `json:"messages"`
}

func (c *clientOption) GetJsonBody() (body map[string]interface{}, err error) {
	if c == nil {
		return nil, errors.New("*clientOption is nil")
	}
	marshal, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(marshal, &body)
	if err != nil {
		return nil, err
	}
	return body, err
}

type ClientOption func(o *clientOption)

func WithModel(model string) ClientOption {
	return func(o *clientOption) {
		o.Model = model
	}
}

func WithTemperature(temperature float32) ClientOption {
	return func(o *clientOption) {
		o.Temperature = temperature
	}
}

func WithSearchEnhance(e bool) ClientOption {
	return func(o *clientOption) {
		o.WithSearchEnhance = e
	}
}

func NewClient(url string, timeout time.Duration, apiKey string, log *log.Helper, options ...ClientOption) *Client {
	sseClient := llm.NewClient(url, timeout, log)
	var option = &clientOption{
		Stream:            true,
		Model:             Baichuan2,
		Temperature:       0.3,
		WithSearchEnhance: true,
	}
	for _, o := range options {
		o(option)
	}
	return &Client{
		client:    sseClient,
		apiKey:    apiKey,
		eventChan: make(chan dto.Data),
		option:    option,
		log:       log,
	}
}

func (c *Client) Send(ctx context.Context, server Server, message string, history []request.Message) (event <-chan dto.Data, err error) {
	if history == nil {
		c.log.WithContext(ctx).Infof("Baichuan2: history 为空")
		history = make([]request.Message, 0)
	}
	if message != "" {
		history = append(history, request.Message{
			Role:    request.UserRole,
			Content: message,
		})
	} else {
		c.log.WithContext(ctx).Warnf("Baichuan2: message 为空")
	}
	c.option.Messages = history
	body, err := c.option.GetJsonBody()
	if err != nil {
		c.log.WithContext(ctx).Warnf("Baichuan2: c.option.GetJsonBody() error:%v ", err)
		return nil, err
	}
	eventChan, err := c.client.Send(ctx, llm.PostMethod, server.GetHeader(c.apiKey), body)
	if err != nil {
		c.log.WithContext(ctx).Errorf("Baichuan2 error, send error: %v", err)
		return nil, err
	}

	go func() {
		defer func() {
			close(c.eventChan)
			if err := recover(); err != nil {
				c.log.WithContext(ctx).Errorf("Baichuan2: error, go routine panic错误：%v\n %s", err, debug.Stack())
				return
			}
		}()

		for data := range eventChan {
			c.log.WithContext(ctx).Infof("Baichuan2 real data: %v", data)
			if data.Error != nil {
				// 存在异常，直接默认输出error兜底
				c.eventChan <- dto.Data{
					ErrorCode: common.OvertimeError,
					ErrorMsg:  common.OvertimeErrorMsg,
					IsEnd:     true,
					Model:     Baichuan2,
				}
				return
			}

			var result Result
			err := json.Unmarshal([]byte(data.Data), &result)
			if err != nil {
				c.log.WithContext(ctx).Warnf("Baichuan2SSE: data json.Unmarshal error, realData: %s", data.Data)
				continue
			}

			errorCode := 0
			errorMsg := ""
			//判断百川是否命中内容过滤
			if result.IsContentFilter() {
				extError := common.New(common.ECode120015, "百川内容被过滤")
				errorCode = common.GetErrorCode(extError)
				errorMsg = common.GetErrorMessage(extError)
			}

			c.eventChan <- dto.Data{
				ErrorCode:  errorCode,
				ErrorMsg:   errorMsg,
				Id:         result.Id,
				Object:     result.Object,
				Created:    result.Created,
				SentenceId: 0,
				IsEnd:      result.GetIsEnd(),
				Result:     result.GetContent(),
				Model:      result.Model,
				Usage: dto.Usage{
					PromptTokens:     result.Usage.PromptTokens,
					CompletionTokens: result.Usage.CompletionTokens,
					TotalTokens:      result.Usage.TotalTokens,
				},
			}

		}
	}()
	return c.eventChan, nil
}
