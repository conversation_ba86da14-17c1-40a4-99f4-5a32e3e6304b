package baidu

import (
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"llm_service/internal/biz/llm"
	"llm_service/internal/common"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"runtime/debug"
	"strings"
	"time"
)

var versionForUrl = map[string]string{
	"baiduV1":       "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions",
	"baiduV2":       "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant",
	"baiduPrivate":  "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/https",
	"baiduPrivate2": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/oczsbp3t_noshot-composition",
}

type Client struct {
	client    *llm.Client
	eventChan chan dto.Data
	ApiKey    string
	option    *clientOption
	log       *log.Helper
}

type clientOption struct {
	Stream bool `json:"stream"`
	//System   string            `json:"system"`
	Messages []request.Message `json:"messages"`
}
type ClientOption func(o *clientOption)

func WithStream(stream bool) ClientOption {
	return func(o *clientOption) {
		o.Stream = stream
	}
}

//func WithSystem(system string) ClientOption {
//	return func(o *clientOption) {
//		o.System = system
//	}
//}

func (c *clientOption) GetJsonBody() (body map[string]interface{}) {
	if c == nil {
		return nil
	}
	marshal, _ := json.Marshal(c)
	json.Unmarshal(marshal, &body)
	return body
}

func NewClient(timeout time.Duration, version, apiKey string, log *log.Helper, options ...ClientOption) *Client {
	url, ok := versionForUrl[version]
	if !ok {
		url = versionForUrl["baiduV1"]
	}
	sseClient := llm.NewClient(url+"?access_token="+apiKey, timeout, log)
	var option = &clientOption{
		Stream: true,
	}
	for _, o := range options {
		o(option)
	}
	return &Client{
		client:    sseClient,
		eventChan: make(chan dto.Data),
		ApiKey:    apiKey,
		option:    option,
		log:       log,
	}
}

func countCharacters(jsonString string) (int, error) {
	// 解析JSON字符串为map[string]interface{}
	var jsonObj map[string]interface{}
	err := json.Unmarshal([]byte(jsonString), &jsonObj)
	if err != nil {
		return 0, err
	}

	// 将map[string]interface{}转换为JSON字符串
	jsonBytes, err := json.Marshal(jsonObj)
	if err != nil {
		return 0, err
	}

	// 返回JSON字符串的长度
	return len(jsonBytes), nil
}

func (c *Client) Send(ctx context.Context, message string, history []request.Message) (event <-chan dto.Data, err error) {
	if history == nil {
		c.log.WithContext(ctx).Infof("Baidu: history 为空")
		history = make([]request.Message, 0)
	}
	if message != "" {
		history = append(history, request.Message{
			Role:    request.UserRole,
			Content: message,
		})
	} else {
		c.log.WithContext(ctx).Warnf("Baidu: message 为空")
	}
	bh, err := json.Marshal(history)
	if err != nil {
		return nil, err
	}
	tokens := countRunes(string(bh))
	for {
		if tokens <= 6000 {
			break
		} else {
			history = history[1:]
			bh, err = json.Marshal(history)
			if err != nil {
				c.log.WithContext(ctx).Warnf("Baidu: error,tokens 数量超限,当前数量 %v ,当前限制数量:6000", tokens)
				return nil, err
			}
			tokens = countRunes(string(bh))
		}
	}
	c.option.Messages = history
	eventChan, err := c.client.Send(ctx, llm.PostMethod, nil, c.option.GetJsonBody())
	if err != nil {
		c.log.WithContext(ctx).Errorf("Ernie error, send error: %v", err)
		return nil, err
	}
	go func() {
		defer func() {
			close(c.eventChan)
			if err := recover(); err != nil {
				c.log.WithContext(ctx).Errorf("sse: error, go routine panic错误：%v\n %s", err, debug.Stack())
				return
			}
		}()
		for data := range eventChan {
			if data.Error != nil {
				// 存在异常，直接默认输出error兜底
				c.eventChan <- dto.Data{
					ErrorCode: common.OvertimeError,
					ErrorMsg:  common.OvertimeErrorMsg,
					IsEnd:     true,
					Model:     "baidu",
				}
				return
			}
			if strings.HasPrefix(data.Data, "data:") {
				realData := data.Data[5:]
				var result dto.Data
				err := json.Unmarshal([]byte(realData), &result)
				if err != nil {
					// 存在异常，忽略当前异常，直到输出完成
					c.log.WithContext(ctx).Warnf("baidu: data json.Unmarshal error, realData: %+v", data.Data)
					continue
				}
				c.eventChan <- result
			} else {
				// 兼容百度不带data: 的问题
				var result dto.Data
				err := json.Unmarshal([]byte(data.Data), &result)
				result.Model = "baidu"
				if err != nil {
					// 存在异常，忽略当前异常，直到输出完成
					c.log.WithContext(ctx).Warnf("baidu: data json.Unmarshal error, realData: %s", data.Data)
					continue
				}
				c.eventChan <- result
			}
		}
	}()
	return c.eventChan, nil
}

func countRunes(str string) int {
	// 将字符串转换为 rune 切片
	runes := []rune(str)

	// 返回 rune 切片的长度，即字符串中 Unicode 字符的数量
	return len(runes)
}
