package doubao_lite_32k

import (
	"context"
	"encoding/json"
	"llm_service/internal/biz/llm"
	"llm_service/internal/common"
	"llm_service/internal/data"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"runtime/debug"
	"time"

	"github.com/pkg/errors"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	DouBaoLite32k = "doubao-lite-32k"
)

type Server string

const (
	AiServiceServer Server = "llm-service-doubao-lite-32k"
)

func (s Server) GetHeader(apiKey string) map[string]string {
	switch s {
	case AiServiceServer:
		return map[string]string{
			"api-key":         apiKey,
			"X-DashScope-SSE": "enable",
		}
	default:
		return nil
	}
}

// Usage 定义 usage 结构体
type Usage struct {
	TotalTokens      int `json:"total_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	PromptTokens     int `json:"prompt_tokens"`
}

type Response struct {
	ID                string    `json:"id"`
	Object            string    `json:"object"`
	Model             string    `json:"model"`
	Created           int       `json:"created"`
	SystemFingerprint string    `json:"system_fingerprint"`
	Usage             Usage     `json:"usage"`
	Choices           []Choices `json:"choices"`
}

type Delta struct {
	Content string `json:"content"`
	Role    string `json:"role"`
}
type Choices struct {
	Delta        Delta  `json:"delta"`
	Index        int    `json:"index"`
	FinishReason string `json:"finish_reason"`
}

type StreamOptions struct {
	IncludeUsage bool `json:"include_usage"`
}

type clientOption struct {
	Model         string            `json:"model"`
	Messages      []request.Message `json:"messages"`
	Temperature   float32           `json:"temperature"`
	ExtraBody     ExtraBody         `json:"extra_body"`
	Stream        bool              `json:"stream"`
	StreamOptions StreamOptions     `json:"stream_options"`
}

type ExtraBody struct {
	EnableSearch bool `json:"enable_search"`
}

func (c *clientOption) GetJsonBody() (body map[string]interface{}, err error) {
	if c == nil {
		return nil, errors.New("*clientOption is nil")
	}
	marshal, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(marshal, &body)
	if err != nil {
		return nil, err
	}
	return body, err
}

type ClientOption func(o *clientOption)

func WithModel(model string) ClientOption {
	return func(o *clientOption) {
		o.Model = model
	}
}

func WithStream(stream bool) ClientOption {
	return func(o *clientOption) {
		o.Stream = stream
	}
}

func WithTemperature(temperature float32) ClientOption {
	return func(o *clientOption) {
		o.Temperature = temperature
	}
}

func WithStreamOptionsIncludeUsage(includeUsage bool) ClientOption {
	return func(o *clientOption) {
		o.StreamOptions.IncludeUsage = includeUsage
	}
}

func WithEnableSearch(enableSearch bool) ClientOption {
	return func(o *clientOption) {
		o.ExtraBody.EnableSearch = enableSearch
	}
}

type Client struct {
	client    *llm.Client
	apiKey    string
	eventChan chan dto.Data
	option    *clientOption
	log       *log.Helper
}

func NewClient(url string, timeout time.Duration, apiKey string, log *log.Helper, options ...ClientOption) *Client {
	sseClient := llm.NewClient(url, timeout, log)
	var option = &clientOption{
		Model: DouBaoLite32k,
	}
	for _, o := range options {
		o(option)
	}
	return &Client{
		client:    sseClient,
		apiKey:    apiKey,
		option:    option,
		eventChan: make(chan dto.Data),
		log:       log,
	}
}

// NewClientWithStats 创建带统计功能的客户端
func NewClientWithStats(url string, timeout time.Duration, apiKey string, log *log.Helper, chatDao *data.ChatDao, options ...ClientOption) *Client {
	sseClient := llm.NewClientWithStats(url, timeout, log, chatDao)
	var option = &clientOption{
		Model: DouBaoLite32k,
	}
	for _, o := range options {
		o(option)
	}
	return &Client{
		client:    sseClient,
		apiKey:    apiKey,
		option:    option,
		eventChan: make(chan dto.Data),
		log:       log,
	}
}

func (c *Client) Send(ctx context.Context, server Server, prompt, message string, history []request.Message) (event <-chan dto.Data, err error) {
	if history == nil {
		c.log.WithContext(ctx).Infof("doubao-lite-32k: history 为空")
		history = make([]request.Message, 0)
	}
	if prompt != "" {
		newMessage := request.Message{
			Role:    request.SystemRole,
			Content: prompt,
		}
		// 将 newMessage 插入到 history 的第一个位置
		history = append([]request.Message{newMessage}, history...)
	} else {
		c.log.WithContext(ctx).Warnf("doubao-lite-32k: prompt 为空")
	}
	if message != "" {
		history = append(history, request.Message{
			Role:    request.UserRole,
			Content: message,
		})
	} else {
		c.log.WithContext(ctx).Warnf("doubao-lite-32k: message 为空")
	}
	c.option.Messages = history
	body, err := c.option.GetJsonBody()
	if err != nil {
		c.log.WithContext(ctx).Warnf("doubao-lite-32k: c.option.GetJsonBody() error:%v ", err)
		return nil, err
	}
	eventChan, err := c.client.Send(ctx, llm.PostMethod, server.GetHeader(c.apiKey), body)
	if err != nil {
		c.log.WithContext(ctx).Errorf("doubao-lite-32k error, send error: %v", err)
		return nil, err
	}

	go func() {
		defer func() {
			close(c.eventChan)
			if err := recover(); err != nil {
				c.log.WithContext(ctx).Errorf("doubao-lite-32k error, go routine panic错误：%v\n %s", err, debug.Stack())
				return
			}
		}()
		isEnd := false
		for data := range eventChan {
			c.log.WithContext(ctx).Infof("doubao-lite-32k real data: %v", data)
			if data.Error != nil {
				// 存在异常，直接默认输出error兜底
				c.eventChan <- dto.Data{
					ErrorCode: common.OvertimeError,
					ErrorMsg:  common.OvertimeErrorMsg,
					IsEnd:     true,
					Model:     c.option.Model,
				}
				return
			}

			var d Response
			err := json.Unmarshal([]byte(data.Data), &d)
			if err != nil {
				// 存在异常，忽略当前异常，直到输出完成
				c.log.WithContext(ctx).Warnf("DouBaoLite32kSSE: data json.Unmarshal error, realData: %s", data.Data)
				continue
			}
			c.log.WithContext(ctx).Infof("输出大模型结果 doubao-lite-32k :%v", data.Data)
			msg := ""
			if len(d.Choices) > 0 {
				msg = d.Choices[0].Delta.Content
			}
			if d.ID == "" {
				isEnd = true
			}
			result := dto.Data{
				Id:     d.ID,
				IsEnd:  isEnd,
				Model:  DouBaoLite32k,
				Result: msg,
				Usage: dto.Usage{
					PromptTokens:     d.Usage.PromptTokens,
					CompletionTokens: d.Usage.CompletionTokens,
					TotalTokens:      d.Usage.TotalTokens,
				},
			}
			c.eventChan <- result
			if result.IsEnd {
				break
			}
			//【openai协议特殊】会在最后一个包后面追加一个返回token用量的包
			if d.Choices[0].FinishReason == "stop" {
				isEnd = true
			}
		}
	}()
	return c.eventChan, nil
}
