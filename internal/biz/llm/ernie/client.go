package ernie

import (
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"llm_service/internal/biz/llm"
	"llm_service/internal/common"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"runtime/debug"
	"strings"
	"time"
)

const (
	ErnieBot = "ernie-bot"
)

type Server string

const (
	AiServiceServer Server = "ai-service-ernie-bot"
)

func (s Server) GetHeader(apiKey string) map[string]string {
	switch s {
	case AiServiceServer:
		return map[string]string{
			"api-key": apiKey,
		}
	default:
		return nil
	}
}

type Client struct {
	client    *llm.Client
	apiKey    string
	eventChan chan dto.Data
	option    *clientOption
	log       *log.Helper
}

type Result struct {
	Id         string `json:"id"`
	Object     string `json:"object"`
	Created    int    `json:"created"`
	SentenceId int    `json:"sentence_id"`
	IsEnd      bool   `json:"is_end"`
	Result     string `json:"result"`
	Model      string `json:"model"`
	Usage      Usage  `json:"usage"`
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

type clientOption struct {
	Model       string            `json:"model"`
	Temperature float32           `json:"temperature"`
	Stream      bool              `json:"stream"`
	Messages    []request.Message `json:"messages"`
	System      string            `json:"system"`
}

func (c *clientOption) GetJsonBody() (body map[string]interface{}, err error) {
	if c == nil {
		return nil, errors.New("*clientOption is nil")
	}
	marshal, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(marshal, &body)
	if err != nil {
		return nil, err
	}
	return body, err
}

type ClientOption func(o *clientOption)

func WithModel(model string) ClientOption {
	return func(o *clientOption) {
		o.Model = model
	}
}

func WithSystem(system string) ClientOption {
	return func(o *clientOption) {
		o.System = system
	}
}

func WithStream(stream bool) ClientOption {
	return func(o *clientOption) {
		o.Stream = stream
	}
}

func WithTemperature(temperature float32) ClientOption {
	return func(o *clientOption) {
		o.Temperature = temperature
	}
}

func NewClient(url string, timeout time.Duration, apiKey string, log *log.Helper, options ...ClientOption) *Client {
	sseClient := llm.NewClient(url, timeout, log)
	var option = &clientOption{
		Stream: true,
		Model:  ErnieBot,
	}
	for _, o := range options {
		o(option)
	}
	return &Client{
		client:    sseClient,
		apiKey:    apiKey,
		eventChan: make(chan dto.Data),
		option:    option,
		log:       log,
	}
}

func (c *Client) Send(ctx context.Context, server Server, message string, history []request.Message) (event <-chan dto.Data, err error) {
	if history == nil {
		c.log.WithContext(ctx).Infof("Ernie: history 为空")
		history = make([]request.Message, 0)
	}
	if message != "" {
		history = append(history, request.Message{
			Role:    request.UserRole,
			Content: message,
		})
	} else {
		c.log.WithContext(ctx).Warnf("Ernie: message 为空")
	}
	c.option.Messages = history
	body, err := c.option.GetJsonBody()
	if err != nil {
		c.log.WithContext(ctx).Warnf("Ernie: c.option.GetJsonBody() error:%v ", err)
		return nil, err
	}
	eventChan, err := c.client.Send(ctx, llm.PostMethod, server.GetHeader(c.apiKey), body)
	if err != nil {
		c.log.WithContext(ctx).Errorf("Ernie error, send error: %v", err)
		return nil, err
	}

	go func() {
		defer func() {
			close(c.eventChan)
			if err := recover(); err != nil {
				c.log.WithContext(ctx).Errorf("Ernie error, go routine panic错误：%v\n %s", err, debug.Stack())
				return
			}
		}()

		for data := range eventChan {
			c.log.WithContext(ctx).Infof("Ernie real data: %v", data)
			if data.Error != nil {
				// 存在异常，直接默认输出error兜底
				c.eventChan <- dto.Data{
					ErrorCode: common.OvertimeError,
					ErrorMsg:  common.OvertimeErrorMsg,
					IsEnd:     true,
					Model:     c.option.Model,
				}
				return
			}
			if strings.HasPrefix(data.Data, "data:") {
				var result Result
				err := json.Unmarshal([]byte(data.Data), &result)
				if err != nil {
					c.log.WithContext(ctx).Warnf("ErnieSSE: data json.Unmarshal error, realData: %s", data.Data)
					continue
				}
				c.log.WithContext(ctx).Infof("输出大模型结果 ErnieSSE: %v", data.Data)
				c.eventChan <- dto.Data{
					ErrorCode:  0,
					ErrorMsg:   "",
					Id:         result.Id,
					Object:     result.Object,
					Created:    result.Created,
					SentenceId: result.SentenceId,
					IsEnd:      result.IsEnd,
					Result:     result.Result,
					Model:      c.option.Model,
					Usage: dto.Usage{
						PromptTokens:     result.Usage.PromptTokens,
						CompletionTokens: result.Usage.CompletionTokens,
						TotalTokens:      result.Usage.TotalTokens,
					},
				}
			} else {
				// 兼容百度error时，不带data: 的问题
				var result dto.Data
				err := json.Unmarshal([]byte(data.Data), &result)
				result.Model = "baidu"
				if err != nil {
					// 存在异常，忽略当前异常，直到输出完成
					c.log.WithContext(ctx).Warnf("ErnieSSE: data json.Unmarshal error, realData: %s", data.Data)
					continue
				}
				c.eventChan <- result
			}
		}
	}()
	return c.eventChan, nil
}

func countRunes(str string) int {
	// 将字符串转换为 rune 切片
	runes := []rune(str)

	// 返回 rune 切片的长度，即字符串中 Unicode 字符的数量
	return len(runes)
}
