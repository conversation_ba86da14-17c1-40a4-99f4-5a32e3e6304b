package openAICommon

import (
	"context"
	"encoding/json"
	"llm_service/internal/biz/llm"
	"llm_service/internal/common"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"runtime/debug"
	"strings"
	"time"

	"github.com/pkg/errors"

	"github.com/go-kratos/kratos/v2/log"
)

func getHeader(apiKey string) map[string]string {
	return map[string]string{
		"api-key":         apiKey,
		"X-DashScope-SSE": "enable",
	}
}

// Usage 定义 usage 结构体
type Usage struct {
	TotalTokens      int `json:"total_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	PromptTokens     int `json:"prompt_tokens"`
}

type Response struct {
	ID                string    `json:"id"`
	Object            string    `json:"object"`
	Model             string    `json:"model"`
	Created           int       `json:"created"`
	SystemFingerprint string    `json:"system_fingerprint"`
	Usage             Usage     `json:"usage"`
	Choices           []Choices `json:"choices"`
}

type Delta struct {
	Content          string `json:"content"`
	ReasoningContent string `json:"reasoning_content"`
	Role             string `json:"role"`
}
type Choices struct {
	Delta        Delta  `json:"delta"`
	Index        int    `json:"index"`
	FinishReason string `json:"finish_reason"`
}

type StreamOptions struct {
	IncludeUsage bool `json:"include_usage"`
}

type clientOption struct {
	Model         string        `json:"model"`
	Messages      interface{}   `json:"messages"`
	Temperature   float32       `json:"temperature"`
	MaxTokens     int           `json:"max_tokens,omitempty"`
	ExtraBody     ExtraBody     `json:"extra_body"`
	Stream        bool          `json:"stream"`
	StreamOptions StreamOptions `json:"stream_options"`
}

type ExtraBody struct {
	EnableSearch    bool   `json:"enable_search,omitempty"`
	SearchStrategy  string `json:"search_strategy,omitempty"`
	ReturnReasoning bool   `json:"return_reasoning,omitempty"`
}

func (c *clientOption) GetJsonBody() (body map[string]interface{}, err error) {
	if c == nil {
		return nil, errors.New("*clientOption is nil")
	}
	marshal, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(marshal, &body)
	if err != nil {
		return nil, err
	}
	return body, err
}

type ClientOption func(o *clientOption)

func WithModel(model string) ClientOption {
	return func(o *clientOption) {
		o.Model = model
	}
}

func WithStream(stream bool) ClientOption {
	return func(o *clientOption) {
		o.Stream = stream
	}
}

func WithTemperature(temperature float32) ClientOption {
	return func(o *clientOption) {
		o.Temperature = temperature
	}
}

func WithMaxTokens(maxTokens int) ClientOption {
	return func(o *clientOption) {
		o.MaxTokens = maxTokens
	}
}

func WithStreamOptionsIncludeUsage(includeUsage bool) ClientOption {
	return func(o *clientOption) {
		o.StreamOptions.IncludeUsage = includeUsage
	}
}

func WithEnableSearch(enableSearch bool) ClientOption {
	return func(o *clientOption) {
		o.ExtraBody.EnableSearch = enableSearch
	}
}

func WithSearchStrategy(searchStrategy string) ClientOption {
	return func(o *clientOption) {
		o.ExtraBody.SearchStrategy = searchStrategy
	}
}
func WithReturnReasoning(returnReasoning bool) ClientOption {
	return func(o *clientOption) {
		o.ExtraBody.ReturnReasoning = returnReasoning
	}
}

type OpenAIClient struct {
	client    *llm.Client
	apiKey    string
	eventChan chan dto.Data
	option    *clientOption
	log       *log.Helper
	logKey    string
}

func NewOpenAIClient(url, apiKey, logKey string, timeout time.Duration, log *log.Helper, options ...ClientOption) *OpenAIClient {
	sseClient := llm.NewClient(url, timeout, log)
	var option = &clientOption{}
	for _, o := range options {
		o(option)
	}
	return &OpenAIClient{
		client:    sseClient,
		apiKey:    apiKey,
		option:    option,
		eventChan: make(chan dto.Data),
		log:       log,
		logKey:    logKey,
	}
}

// processPrompt 处理 prompt 参数，返回 system 消息
func (c *OpenAIClient) processPrompt(ctx context.Context, prompt string) []request.MultiMessage {
	var systemMessages []request.MultiMessage

	if prompt != "" && strings.Trim(prompt, " ") != "" {
		newMessage := request.MultiMessage{
			Role:    request.SystemRole,
			Content: prompt,
		}
		systemMessages = append(systemMessages, newMessage)
	} else {
		c.log.WithContext(ctx).Warnf(c.logKey + ": prompt 为空")
	}

	return systemMessages
}

// processHistory 处理 history 参数，返回历史消息
func (c *OpenAIClient) processHistory(ctx context.Context, history interface{}) []request.MultiMessage {
	var historyMessages []request.MultiMessage

	if history == nil {
		// history 为空
		c.log.WithContext(ctx).Infof(c.logKey + ": history 为空")
		return historyMessages
	}

	// 尝试将 history 转换为 []request.Message
	if messages, ok := history.([]request.Message); ok {
		// 成功转换为 []request.Message
		for _, msg := range messages {
			historyMessages = append(historyMessages, request.MultiMessage{
				Role:    msg.Role,
				Content: msg.Content,
			})
		}
		c.log.WithContext(ctx).Infof(c.logKey+": 成功将 history 转换为 []request.Message，共 %d 条消息", len(messages))
		return historyMessages
	}

	// 尝试将 history 转换为 []request.MultiMessage
	if multiMessages, ok := history.([]request.MultiMessage); ok {
		// 成功转换为 []request.MultiMessage
		historyMessages = append(historyMessages, multiMessages...)
		c.log.WithContext(ctx).Infof(c.logKey+": 成功将 history 转换为 []request.MultiMessage，共 %d 条消息", len(multiMessages))
		return historyMessages
	}

	// 尝试将 history 转换为 JSON 字符串，然后再解析为所需的类型
	historyJSON, err := json.Marshal(history)
	if err != nil {
		c.log.WithContext(ctx).Warnf(c.logKey+": 无法将 history 转换为 JSON: %v", err)
		return historyMessages
	}

	// 先尝试转换为 []request.Message
	var messages []request.Message
	err = json.Unmarshal(historyJSON, &messages)
	if err == nil && len(messages) > 0 {
		// 成功转换为 []request.Message
		for _, msg := range messages {
			historyMessages = append(historyMessages, request.MultiMessage{
				Role:    msg.Role,
				Content: msg.Content,
			})
		}
		c.log.WithContext(ctx).Infof(c.logKey+": 成功将 history 转换为 []request.Message，共 %d 条消息", len(messages))
		return historyMessages
	}

	// 尝试转换为 []request.MultiMessage
	var multiMessages []request.MultiMessage
	err = json.Unmarshal(historyJSON, &multiMessages)
	if err == nil && len(multiMessages) > 0 {
		// 成功转换为 []request.MultiMessage
		historyMessages = append(historyMessages, multiMessages...)
		c.log.WithContext(ctx).Infof(c.logKey+": 成功将 history 转换为 []request.MultiMessage，共 %d 条消息", len(multiMessages))
		return historyMessages
	}

	// 尝试其他方式
	c.log.WithContext(ctx).Warnf(c.logKey+": 无法将 history 转换为已知类型，类型为 %T", history)
	return historyMessages
}

// processMessage 处理 message 参数，返回用户消息
func (c *OpenAIClient) processMessage(ctx context.Context, message interface{}) []request.MultiMessage {
	var userMessages []request.MultiMessage

	if message == nil {
		c.log.WithContext(ctx).Warnf(c.logKey + ": message 为 nil")
		return userMessages
	}

	switch m := message.(type) {
	case string:
		// 文本消息
		if m != "" {
			userMessages = append(userMessages, request.MultiMessage{
				Role:    request.UserRole,
				Content: m,
			})
		} else {
			c.log.WithContext(ctx).Warnf(c.logKey + ": message 为空")
		}
	case request.MultiMessage:
		// 已经是 MultiMessage，直接添加
		userMessages = append(userMessages, m)
	case request.MultiModalMessage:
		// 单个多模态消息，作为内容添加
		userMessages = append(userMessages, request.MultiMessage{
			Role:    request.UserRole,
			Content: m,
		})
	case []request.MultiModalMessage:
		// 多个多模态消息，创建一个包含这些消息的 MultiMessage
		userMessages = append(userMessages, request.MultiMessage{
			Role:    request.UserRole,
			Content: m,
		})
	default:
		// 其他类型，尝试直接作为内容使用
		c.log.WithContext(ctx).Infof(c.logKey+": 将消息类型 %T 直接作为内容使用", message)
		userMessages = append(userMessages, request.MultiMessage{
			Role:    request.UserRole,
			Content: m,
		})
	}

	return userMessages
}

// combineMessages 按照 system -> history -> user 的顺序组合消息
func (c *OpenAIClient) combineMessages(systemMessages, historyMessages, userMessages []request.MultiMessage) []request.MultiMessage {
	var multiHistory []request.MultiMessage

	// 先添加 system 消息
	multiHistory = append(multiHistory, systemMessages...)

	// 如果历史消息不为空，添加历史消息
	if len(historyMessages) > 0 {
		multiHistory = append(multiHistory, historyMessages...)
	}

	// 添加用户消息
	multiHistory = append(multiHistory, userMessages...)

	return multiHistory
}

// Result 定义响应结果结构体
type Result struct {
	Id      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Delta struct {
			Content string `json:"content"`
			Role    string `json:"role"`
		} `json:"delta"`
		Index        int    `json:"index"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
}

// GetContent 获取响应内容
func (r *Result) GetContent() string {
	if len(r.Choices) > 0 {
		return r.Choices[0].Delta.Content
	}
	return ""
}

// GetIsEnd 判断是否结束
func (r *Result) GetIsEnd() bool {
	if len(r.Choices) > 0 && r.Choices[0].FinishReason != "" {
		return true
	}
	return false
}

func (c *OpenAIClient) Send(ctx context.Context, prompt string, message interface{}, model string, history interface{}) (event <-chan dto.Data, err error) {
	// 处理 prompt，获取 system 消息
	systemMessages := c.processPrompt(ctx, prompt)

	// 处理 history，获取历史消息
	historyMessages := c.processHistory(ctx, history)

	// 处理 message，获取用户消息
	userMessages := c.processMessage(ctx, message)

	// 按照 system -> history -> user 的顺序组合消息
	multiHistory := c.combineMessages(systemMessages, historyMessages, userMessages)

	// 设置消息
	c.option.Messages = multiHistory

	body, err := c.option.GetJsonBody()
	if err != nil {
		c.log.WithContext(ctx).Warnf(c.logKey+": c.option.GetJsonBody() error:%v ", err)
		return nil, err
	}

	eventChan, err := c.client.Send(ctx, llm.PostMethod, getHeader(c.apiKey), body)
	if err != nil {
		c.log.WithContext(ctx).Errorf(c.logKey+" error, send error: %v", err)
		return nil, err
	}

	// 创建一个新的 channel，避免复用
	c.eventChan = make(chan dto.Data, 1024)

	go func(model string) {
		defer func() {
			close(c.eventChan)
			if err := recover(); err != nil {
				c.log.WithContext(ctx).Errorf(c.logKey+" error, go routine panic错误：%v\n %s", err, debug.Stack())
				return
			}
		}()
		isEnd := false
		for data := range eventChan {
			c.log.WithContext(ctx).Infof(c.logKey+" real data: %v", data)
			if data.Error != nil {
				// 存在异常，直接默认输出error兜底
				c.eventChan <- dto.Data{
					ErrorCode: common.OvertimeError,
					ErrorMsg:  common.OvertimeErrorMsg,
					IsEnd:     true,
					Model:     c.option.Model,
				}
				return
			}

			var d Response
			err := json.Unmarshal([]byte(data.Data), &d)
			if err != nil {
				// 存在异常，忽略当前异常，直到输出完成
				c.log.WithContext(ctx).Warnf(c.logKey+"SSE: data json.Unmarshal error, realData: %s", data.Data)
				continue
			}
			c.log.WithContext(ctx).Infof("输出大模型结果 %v :%v", c.logKey, data.Data)
			msg := ""
			isDeepSeek := false
			if d.Choices != nil {
				if len(d.Choices) > 0 {
					msg = d.Choices[0].Delta.Content
					if (model == common.GptTypeMap[common.GptTypeDeepSeekReasoner] ||
						model == common.GptTypeMap[common.GptTypeDeepSeekR1DistillQwen32B]) && (d.Choices[0].Delta.Content == "" && d.Choices[0].Delta.ReasoningContent != "") {
						isDeepSeek = true
						msg = d.Choices[0].Delta.ReasoningContent
					}
				}
			} else {
				isEnd = true
			}

			if d.ID == "" {
				isEnd = true
			}
			result := dto.Data{
				Id:         d.ID,
				IsEnd:      isEnd,
				Model:      model,
				IsDeepSeek: isDeepSeek,
				Result:     msg,
				Usage: dto.Usage{
					PromptTokens:     d.Usage.PromptTokens,
					CompletionTokens: d.Usage.CompletionTokens,
					TotalTokens:      d.Usage.TotalTokens,
				},
			}
			c.eventChan <- result
			if result.IsEnd {
				break
			}
			//【openai协议特殊】会在最后一个包后面追加一个返回token用量的包
			if len(d.Choices) == 0 {
				c.log.WithContext(ctx).Warnf("中台返回包异常 len(d.Choices) == 0 ")
				isEnd = true
			} else if d.Choices[0].FinishReason == "stop" {
				isEnd = true
			}
		}
	}(model)
	return c.eventChan, nil
}
