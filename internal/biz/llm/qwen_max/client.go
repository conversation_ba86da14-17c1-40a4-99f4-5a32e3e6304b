package qwen_max

import (
	"context"
	"encoding/json"
	"github.com/pkg/errors"
	"llm_service/internal/biz/llm"
	"llm_service/internal/common"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"runtime/debug"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	QwenMax = "qwen-max"
)

type Server string

const (
	AiServiceServer Server = "llm-service-qwen-max"
)

func (s Server) GetHeader(apiKey string) map[string]string {
	switch s {
	case AiServiceServer:
		return map[string]string{
			"api-key":         apiKey,
			"X-DashScope-SSE": "enable",
			//"X-APX-Inspection": "enable",
		}
	default:
		return nil
	}
}

type Message struct {
	Content string `json:"content"`
	Role    string `json:"role"`
}

// Choice 定义 choices 结构体
type Choice struct {
	Message      Message `json:"message"`
	FinishReason string  `json:"finish_reason"`
}

// Output 定义 output 结构体
type Output struct {
	Choices []Choice `json:"choices"`
}

// Usage 定义 usage 结构体
type Usage struct {
	TotalTokens  int `json:"total_tokens"`
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
}

// Response 定义整个 JSON 的根结构体
type Response struct {
	Output    Output `json:"output"`
	Usage     Usage  `json:"usage"`
	RequestID string `json:"request_id"`
}

type Input struct {
	Messages []request.Message `json:"messages"`
}

type Parameters struct {
	Temperature       float32 `json:"temperature"`
	ResultFormat      string  `json:"result_format"`
	IncrementalOutput bool    `json:"incremental_output"`
	EnableSearch      bool    `json:"enable_search"`
	SearchStrategy    string  `json:"search_strategy"`
}

type clientOption struct {
	Model      string     `json:"model"`
	Input      Input      `json:"input"`
	Parameters Parameters `json:"parameters"`
	Stream     bool       `json:"stream"`
}

func (c *clientOption) GetJsonBody() (body map[string]interface{}, err error) {
	if c == nil {
		return nil, errors.New("*clientOption is nil")
	}
	marshal, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(marshal, &body)
	if err != nil {
		return nil, err
	}
	return body, err
}

type ClientOption func(o *clientOption)

func WithModel(model string) ClientOption {
	return func(o *clientOption) {
		o.Model = model
	}
}

func WithStream(stream bool) ClientOption {
	return func(o *clientOption) {
		o.Stream = stream
	}
}

func WithTemperature(temperature float32) ClientOption {
	return func(o *clientOption) {
		o.Parameters.Temperature = temperature
	}
}

func WithResultFormat(resultFormat string) ClientOption {
	return func(o *clientOption) {
		o.Parameters.ResultFormat = resultFormat
	}
}

func WithIncrementalOutput(incrementalOutput bool) ClientOption {
	return func(o *clientOption) {
		o.Parameters.IncrementalOutput = incrementalOutput
	}
}
func WithSearchStrategy(searchStrategy string) ClientOption {
	return func(o *clientOption) {
		o.Parameters.SearchStrategy = searchStrategy
	}
}

func WithEnableSearch(enableSearch bool) ClientOption {
	return func(o *clientOption) {
		o.Parameters.EnableSearch = enableSearch
	}
}

type Client struct {
	client    *llm.Client
	apiKey    string
	eventChan chan dto.Data
	option    *clientOption
	log       *log.Helper
}

func NewClient(url string, timeout time.Duration, apiKey string, log *log.Helper, options ...ClientOption) *Client {
	sseClient := llm.NewClient(url, timeout, log)
	var option = &clientOption{
		Model: QwenMax,
	}
	for _, o := range options {
		o(option)
	}
	return &Client{
		client:    sseClient,
		apiKey:    apiKey,
		option:    option,
		eventChan: make(chan dto.Data),
		log:       log,
	}
}

func (c *Client) Send(ctx context.Context, server Server, prompt, message string, history []request.Message) (event <-chan dto.Data, err error) {
	if history == nil {
		c.log.WithContext(ctx).Infof("QwenMax: history 为空")
		history = make([]request.Message, 0)
	}
	if prompt != "" {
		newMessage := request.Message{
			Role:    request.SystemRole,
			Content: prompt,
		}
		// 将 newMessage 插入到 history 的第一个位置
		history = append([]request.Message{newMessage}, history...)
	} else {
		c.log.WithContext(ctx).Warnf("QwenMax: prompt 为空")
	}
	if message != "" {
		history = append(history, request.Message{
			Role:    request.UserRole,
			Content: message,
		})
	} else {
		c.log.WithContext(ctx).Warnf("QwenMax: message 为空")
	}
	c.option.Input.Messages = history
	body, err := c.option.GetJsonBody()
	if err != nil {
		c.log.WithContext(ctx).Warnf("QwenMax: c.option.GetJsonBody() error:%v ", err)
		return nil, err
	}
	eventChan, err := c.client.Send(ctx, llm.PostMethod, server.GetHeader(c.apiKey), body)
	if err != nil {
		c.log.WithContext(ctx).Errorf("QwenMax error, send error: %v", err)
		return nil, err
	}

	go func() {
		defer func() {
			close(c.eventChan)
			if err := recover(); err != nil {
				c.log.WithContext(ctx).Errorf("QwenMax error, go routine panic错误：%v\n %s", err, debug.Stack())
				return
			}
		}()

		for data := range eventChan {
			c.log.WithContext(ctx).Infof("QwenMax real data: %v", data)
			if data.Error != nil {
				// 存在异常，直接默认输出error兜底
				c.eventChan <- dto.Data{
					ErrorCode: common.OvertimeError,
					ErrorMsg:  common.OvertimeErrorMsg,
					IsEnd:     true,
					Model:     c.option.Model,
				}
				return
			}

			var d Response
			err := json.Unmarshal([]byte(data.Data), &d)
			if err != nil {
				// 存在异常，忽略当前异常，直到输出完成
				c.log.WithContext(ctx).Warnf("QwenMaxSSE: data json.Unmarshal error, realData: %s", data.Data)
				continue
			}
			c.log.WithContext(ctx).Infof("输出大模型结果 qwen-max :%v", data.Data)
			isEnd := false
			if d.Output.Choices[0].FinishReason == "stop" {
				isEnd = true
			}
			result := dto.Data{
				Id:     d.RequestID,
				IsEnd:  isEnd,
				Model:  QwenMax,
				Result: d.Output.Choices[0].Message.Content,
				Usage: dto.Usage{
					PromptTokens:     d.Usage.InputTokens,
					CompletionTokens: d.Usage.OutputTokens,
					TotalTokens:      d.Usage.TotalTokens,
				},
			}
			c.eventChan <- result
			if result.IsEnd {
				break
			}
		}
	}()
	return c.eventChan, nil
}

func countRunes(str string) int {
	// 将字符串转换为 rune 切片
	runes := []rune(str)

	// 返回 rune 切片的长度，即字符串中 Unicode 字符的数量
	return len(runes)
}
