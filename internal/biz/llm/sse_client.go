package llm

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"llm_service/internal/common"
	"net/http"
	"strings"
	"time"

	"git.100tal.com/znxx_xpp/go-libs/traces"
	"github.com/gin-contrib/sse"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
)

type Method string

func (m Method) String() string {
	return string(m)
}

const (
	OptionsMethod Method = "OPTIONS"
	GetMethod     Method = "GET"
	HeadMethod    Method = "HEAD"
	PostMethod    Method = "POST"
	PutMethod     Method = "PUT"
	DeleteMethod  Method = "DELETE"
)

type Client struct {
	client   *resty.Client
	url      string
	event    chan *Event
	done     chan bool
	timeout  time.Duration
	closeCh  chan struct{}
	log      *log.Helper
	ProxyUrl string
}

type Event struct {
	Error error
	Data  string
}

func NewClient(url string, timeout time.Duration, log *log.Helper) *Client {
	transport := &http.Transport{
		ResponseHeaderTimeout: 60 * time.Second, // 服务器响应超时
	}
	return &Client{
		url:     url,
		client:  traces.MakeHttpClient(log).SetTransport(transport).SetTimeout(timeout).SetBaseURL(url),
		event:   make(chan *Event, 1024),
		done:    make(chan bool),
		timeout: timeout,
		closeCh: make(chan struct{}),
		log:     log,
	}
}

func (c *Client) Close() {
	c.closeCh <- struct{}{}
}

func (c *Client) WithProxy(proxyUrl string) *Client {
	c.ProxyUrl = proxyUrl
	return c
}

func (c *Client) Send(ctx context.Context, method Method, header map[string]string, body map[string]interface{}) (event <-chan *Event, err error) {
	marshal, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	if val, ok := header["X-APX-Inspection"]; ok && val == "enable" {
		bBody, _ := json.Marshal(body)
		c.log.WithContext(ctx).Infof("qwenplus-lvwang:%v", string(bBody))
	}
	c.log.WithContext(ctx).Infof("sse_send_start url:%s,method:%s,body:%s", c.url, method.String(), string(marshal))
	client := c.client.R().SetHeader("Content-Type", "application/json")
	for k, v := range header {
		client.Header.Add(k, v)
	}
	resp, err := client.
		SetDoNotParseResponse(true).
		SetContentLength(true).
		SetContext(ctx).
		SetBody(marshal).Post(c.url)

	if err != nil {
		// 安全获取模型名称
		modelName := ""
		if val, ok := body["model"]; ok {
			if strVal, ok := val.(string); ok {
				modelName = strVal
			}
		}
		// 根据错误类型选择日志级别
		logFunc := c.log.WithContext(ctx).Errorf
		if strings.Contains(err.Error(), "context") {
			logFunc = c.log.WithContext(ctx).Warnf
		}
		bodyStr := string(marshal)
		logFunc("request 模型名称:%v , error: %v , llm body %s ", modelName, err, bodyStr)

		return nil, err
	}
	if resp.StatusCode() != http.StatusOK {
		if val, ok := header["X-APX-Inspection"]; ok && val == "enable" {
			bBody, _ := json.Marshal(body)
			c.log.WithContext(ctx).Infof("qwenplus-lvwangfirstpkgerr:%v", string(bBody))
		}
		c.log.WithContext(ctx).Errorf("request llm is error: status:%v,reqbody:%v,respbody:%s", resp.Status(), string(marshal), resp.Body())
		return nil, errors.New(fmt.Sprintf("request llm error: %v %s", resp.Status(), resp.Body()))
	}
	go c.RequestWithSSE(ctx, body, resp, c.event, func(event *sse.Event, err error) (string, error) {
		if cast.ToString(event.Data) == common.EofText {
			return "", nil
		}
		return cast.ToString(event.Data), nil
	})
	return c.event, nil
}

func (c *Client) RequestWithSSE(ctx context.Context, body map[string]interface{}, resp *resty.Response, ch chan *Event, callback func(event *sse.Event, err error) (string, error)) {
	defer func() {
		close(ch)
		_ = resp.RawResponse.Body.Close()
	}()
	tStart := time.Now().UnixMilli()
	rd := bufio.NewScanner(resp.RawResponse.Body)
	dataBuffer := new(bytes.Buffer) // 复用这个数据缓冲区
	i := 0
	for rd.Scan() {
		dataBuffer.Reset() // 清空缓冲区，而不是每次都重新创建
		data := rd.Bytes()
		i++
		if rd.Text() == common.LLMEventErr {
			rd.Scan()
			rd.Scan()
			c.log.WithContext(ctx).Errorf("大模型输出错误信息:%v", rd.Text())
			ch <- &Event{
				Error: errors.New(rd.Text()),
				Data:  rd.Text(),
			}
			if strings.Contains(rd.Text(), "DataInspectionFailed") {
				bBody, _ := json.Marshal(body)
				if i <= 2 {
					c.log.WithContext(ctx).Infof("qwenplus-lvwangfirstpkgerr:%v", string(bBody))
				} else {
					c.log.WithContext(ctx).Infof("qwenplus-lvwangmiddlepkgerr:%v", string(bBody))
				}
			}
			return
		}
		if len(data) > 0 {
			if !strings.HasPrefix(rd.Text(), "data:") {
				continue
			}
			dataBuffer.Write(data)
			dataBuffer.WriteString("\n")

			events, err := sse.Decode(dataBuffer)
			if err != nil {
				ch <- &Event{
					Error: err,
					Data:  "Error decoding SSE data",
				}
				return
			}

			for _, event := range events {
				msg, err := callback(&event, nil)
				if err != nil {
					ch <- &Event{
						Error: err,
						Data:  "Error in callback",
					}
					continue
				}
				startTime := ctx.Value(common.StartTime)
				tmpId := ctx.Value(common.TmpId)
				cost := time.Since(startTime.(time.Time))
				tEnd := time.Now().UnixMilli()
				fmt.Println(msg)
				c.log.WithContext(ctx).Infof("输出大模型msg:%v tmpid:%v,cost:%v,interval:%v", msg, tmpId, cost.Milliseconds(), tEnd-tStart)
				tStart = time.Now().UnixMilli()
				if msg != "" {
					ch <- &Event{
						Error: nil,
						Data:  msg,
					}
				} else {
					bEvent, _ := json.Marshal(event)
					ch <- &Event{
						Error: nil,
						Data:  string(bEvent),
					}
				}
			}
		}
	}

	if err := rd.Err(); err != nil {
		ch <- &Event{
			Error: err,
			Data:  "Error reading from SSE",
		}
	}
}
