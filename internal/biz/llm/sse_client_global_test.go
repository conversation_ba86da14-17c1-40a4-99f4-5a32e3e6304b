package llm

import (
	"context"
	"testing"
	"time"

	"llm_service/internal/data"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
)

func TestSSEClientWithoutGlobalChatDao(t *testing.T) {
	// 清除全局ChatDao
	SetGlobalChatDao(nil)

	// 创建SSE客户端
	client := NewClient("http://test.com", 30*time.Second, log.NewHelper(log.DefaultLogger))

	// 测试getStatsInfo方法
	ctx := context.Background()
	statsInfo := client.getStatsInfo(ctx, "test-model")

	// 没有ChatDao时应该返回空字符串
	assert.Equal(t, "", statsInfo)
}

func TestSetGlobalChatDao(t *testing.T) {
	// 测试设置nil
	SetGlobalChatDao(nil)
	assert.Nil(t, globalChatDao)

	// 测试设置有效的ChatDao
	chatDao := &data.ChatDao{}
	SetGlobalChatDao(chatDao)
	assert.Equal(t, chatDao, globalChatDao)
}
