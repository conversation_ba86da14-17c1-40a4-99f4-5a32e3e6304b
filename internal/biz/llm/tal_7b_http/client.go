package tal_7b_http

import (
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"llm_service/internal/biz/llm"
	"llm_service/internal/common"
	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"runtime/debug"
	"time"
)

type Server string

func GetHeader() map[string]string {
	return map[string]string{
		"Transfer-Encoding": "chunked",
	}
}

const (
	AiServiceServer Server = "llm-service-tal-7b"
)

type Client struct {
	client    *llm.Client
	conf      *conf.TalConfig
	eventChan chan dto.Data
	option    *clientOption
	log       *log.Helper
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

type clientOption struct {
	N        int               `json:"n"`
	Stream   bool              `json:"stream"`
	Messages []request.Message `json:"messages"`
}

type Data struct {
	IsEnd            int    `json:"is_end"`
	Result           string `json:"result"`
	PromptTokens     int    `json:"prompt_tokens"`
	TotalTokens      int    `json:"total_tokens"`
	CompletionTokens int    `json:"completion_tokens"`
}

// Response 结构体对应于 JSON 数据的根结构
type Response struct {
	Code      int    `json:"code"`
	Msg       string `json:"msg"`
	RequestID string `json:"requestId"`
	Data      Data   `json:"data"`
	Mod       string `json:"mod"`
}

func (c *clientOption) GetJsonBody() (body map[string]interface{}, err error) {
	if c == nil {
		return nil, errors.New("*clientOption is nil")
	}
	marshal, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(marshal, &body)
	if err != nil {
		return nil, err
	}
	return body, err
}

type ClientOption func(o *clientOption)

func WithMessages(messages []request.Message) ClientOption {
	return func(o *clientOption) {
		o.Messages = messages
	}
}

func WithStream(stream bool) ClientOption {
	return func(o *clientOption) {
		o.Stream = stream
	}
}

func WithN(n int) ClientOption {
	return func(o *clientOption) {
		o.N = n
	}
}

func NewClient(url string, timeout time.Duration, conf *conf.TalConfig, log *log.Helper, options ...ClientOption) *Client {
	sseClient := llm.NewClient(url, timeout, log)
	var option = &clientOption{}
	for _, o := range options {
		o(option)
	}
	return &Client{
		client:    sseClient,
		conf:      conf,
		eventChan: make(chan dto.Data),
		option:    option,
		log:       log,
	}
}

func (c *Client) Send(ctx context.Context) (event <-chan dto.Data, err error) {
	body, err := c.option.GetJsonBody()
	if err != nil {
		c.log.WithContext(ctx).Warnf("tal_7b: c.option.GetJsonBody() error:%v ", err)
		return nil, err
	}
	eventChan, err := c.client.Send(ctx, llm.PostMethod, GetHeader(), body)
	if err != nil {
		c.log.WithContext(ctx).Errorf("tal_7b error, send error: %v", err)
		return nil, err
	}
	c.log.WithContext(ctx).Infof("tal_7b util.SendRequest body :%v", body)
	go func() {
		defer func() {
			close(c.eventChan)
			if err := recover(); err != nil {
				c.log.WithContext(ctx).Errorf("tal_7b error, go routine panic错误：%v\n %s", err, debug.Stack())
				return
			}
		}()

		for data := range eventChan {
			c.log.WithContext(ctx).Infof("tal_7b real data: %v", data)
			if data.Error != nil {
				// 存在异常，直接默认输出error兜底
				c.eventChan <- dto.Data{
					ErrorCode: common.OvertimeError,
					ErrorMsg:  common.OvertimeErrorMsg,
					IsEnd:     true,
					Model:     "tal_7b",
				}
				return
			}

			var d Response
			err := json.Unmarshal([]byte(data.Data), &d)
			if err != nil {
				// 存在异常，忽略当前异常，直到输出完成
				c.log.WithContext(ctx).Warnf("tal_7b: data json.Unmarshal error, realData: %s", data.Data)
				continue
			}
			c.log.WithContext(ctx).Infof("输出大模型结果 tal_7b %v", data.Data)
			if d.Data.IsEnd != 0 {
				c.eventChan <- dto.Data{IsEnd: true, Result: ""}
				break
			}
			result := dto.Data{
				Id:     d.RequestID,
				Model:  d.Mod,
				Result: d.Data.Result,
				Usage: dto.Usage{
					PromptTokens:     d.Data.PromptTokens,
					CompletionTokens: d.Data.CompletionTokens,
					TotalTokens:      d.Data.TotalTokens,
				},
			}
			c.eventChan <- result
			if result.IsEnd {
				break
			}
		}
	}()
	return c.eventChan, nil
}
