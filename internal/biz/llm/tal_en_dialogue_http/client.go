package tal_en_dialogue_http

import (
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"llm_service/internal/biz/llm"
	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
)

type Server string

func GetHeader() map[string]string {
	return map[string]string{
		"Transfer-Encoding": "chunked",
	}
}

type Client struct {
	client    *llm.Client
	conf      *conf.TalConfig
	eventChan chan dto.Data
	option    *clientOption
	log       *log.Helper
}

type Result struct {
	Id         string `json:"id"`
	Object     string `json:"object"`
	Created    int    `json:"created"`
	SentenceId int    `json:"sentence_id"`
	IsEnd      bool   `json:"is_end"`
	Result     string `json:"result"`
	Model      string `json:"model"`
	Usage      Usage  `json:"usage"`
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

type clientOption struct {
	Stream   bool              `json:"is_stream"`
	Messages []request.Message `json:"messages"`
}

type Response struct {
	Code      int            `json:"code"`
	Msg       string         `json:"msg"`
	RequestID string         `json:"request_id"`
	Data      EnDialogueData `json:"data"`
}
type EnDialogueData struct {
	Result           string `json:"result"`
	IsEnd            int    `json:"is_end"`
	Mod              string `json:"mod"`
	IsOverwrite      int    `json:"is_overwrite"`
	PromptTokens     int    `json:"prompt_tokens"`
	TotalTokens      int    `json:"total_tokens"`
	CompletionTokens int    `json:"completion_tokens"`
}

func (c *clientOption) GetJsonBody() (body map[string]interface{}, err error) {
	if c == nil {
		return nil, errors.New("*clientOption is nil")
	}
	marshal, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(marshal, &body)
	if err != nil {
		return nil, err
	}
	return body, err
}

type ClientOption func(o *clientOption)
