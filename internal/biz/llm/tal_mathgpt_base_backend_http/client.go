package tal_mathgpt_base_backend_http

import (
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"llm_service/internal/biz/llm"
	"llm_service/internal/common"
	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"runtime/debug"
	"time"
)

type Server string

func GetHeader() map[string]string {
	return map[string]string{
		"Transfer-Encoding": "chunked",
	}
}

type Client struct {
	client    *llm.Client
	conf      *conf.TalConfig
	eventChan chan dto.Data
	option    *clientOption
	log       *log.Helper
}

type Result struct {
	Id         string `json:"id"`
	Object     string `json:"object"`
	Created    int    `json:"created"`
	SentenceId int    `json:"sentence_id"`
	IsEnd      bool   `json:"is_end"`
	Result     string `json:"result"`
	Model      string `json:"model"`
	Usage      Usage  `json:"usage"`
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

type clientOption struct {
	Stream   bool              `json:"is_stream"`
	Messages []request.Message `json:"messages"`
}

type Response struct {
	Code      int                    `json:"code"`
	Msg       string                 `json:"msg"`
	RequestID string                 `json:"request_id"`
	Data      MathGptBaseBackendData `json:"data"`
	Action    string                 `json:"action"`
}
type MathGptBaseBackendData struct {
	Result      string `json:"result"`
	IsEnd       int    `json:"is_end"`
	UserInfo    string `json:"user_info"`
	Mod         string `json:"mod"`
	IsOverwrite int    `json:"is_overwrite"`
	ClassType   string `json:"class_type"`
}

func (c *clientOption) GetJsonBody() (body map[string]interface{}, err error) {
	if c == nil {
		return nil, errors.New("*clientOption is nil")
	}
	marshal, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(marshal, &body)
	if err != nil {
		return nil, err
	}
	return body, err
}

type ClientOption func(o *clientOption)

func WithMessages(messages []request.Message) ClientOption {
	return func(o *clientOption) {
		o.Messages = messages
	}
}

func WithStream(stream bool) ClientOption {
	return func(o *clientOption) {
		o.Stream = stream
	}
}

func NewClient(url string, timeout time.Duration, conf *conf.TalConfig, log *log.Helper, options ...ClientOption) *Client {
	sseClient := llm.NewClient(url, timeout, log)
	var option = &clientOption{}
	for _, o := range options {
		o(option)
	}
	return &Client{
		client:    sseClient,
		conf:      conf,
		eventChan: make(chan dto.Data),
		option:    option,
		log:       log,
	}
}

func (c *Client) Send(ctx context.Context) (event <-chan dto.Data, err error) {
	body, err := c.option.GetJsonBody()
	if err != nil {
		c.log.WithContext(ctx).Warnf("TalMathgpt_base_backend: c.option.GetJsonBody() error:%v ", err)
		return nil, err
	}
	eventChan, err := c.client.Send(ctx, llm.PostMethod, GetHeader(), body)

	if err != nil {
		c.log.WithContext(ctx).Errorf("TalMathgpt_base_backend error, send error: %v", err)
		return nil, err
	}
	c.log.WithContext(ctx).Infof("TalMathgpt_base_backend util.SendRequest body :%v", body)
	go func() {
		defer func() {
			close(c.eventChan)
			if err := recover(); err != nil {
				c.log.WithContext(ctx).Errorf("TalMathgpt_base_backend error, go routine panic错误：%v\n %s", err, debug.Stack())
				return
			}
		}()

		for data := range eventChan {
			c.log.WithContext(ctx).Infof("TalMathgpt_base_backend real data: %v", data)
			if data.Error != nil {
				// 存在异常，直接默认输出error兜底
				c.eventChan <- dto.Data{
					ErrorCode: common.OvertimeError,
					ErrorMsg:  common.OvertimeErrorMsg,
					IsEnd:     true,
					Model:     "tal_7b",
				}
				return
			}

			var d Response
			err := json.Unmarshal([]byte(data.Data), &d)
			if err != nil {
				// 存在异常，忽略当前异常，直到输出完成
				c.log.WithContext(ctx).Warnf("TalMathgpt_base_backend: data json.Unmarshal error, realData: %s", data.Data)
				continue
			}
			c.log.WithContext(ctx).Infof("输出大模型结果 TalMathgpt_base_backend: %v", data.Data)
			if d.Data.IsEnd == 3 {
				c.eventChan <- dto.Data{IsEnd: true, Result: ""}
				break
			}
			result := dto.Data{
				Id:     d.RequestID,
				Model:  d.Data.Mod,
				Result: d.Data.Result,
			}
			c.eventChan <- result
			if result.IsEnd {
				break
			}
		}
	}()
	return c.eventChan, nil
}
