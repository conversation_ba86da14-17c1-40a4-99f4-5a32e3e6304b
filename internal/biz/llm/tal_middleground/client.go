package tal_middleground

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"llm_service/internal/dto/request"
	"runtime/debug"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/websocket"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	"llm_service/internal/biz/llm"
	"llm_service/internal/common"
	"llm_service/internal/conf"
	"llm_service/internal/data/ailab/util"

	"llm_service/internal/data/base"
	"llm_service/internal/dto/tal"
	"llm_service/internal/dto/ws"
)

type TalMiddleGroundUseCase struct {
	*llm.BaseUseCase
	log *log.Helper
}

func NewTalMiddleGroundUseCase(base *llm.BaseUseCase, logger log.Logger) *TalMiddleGroundUseCase {
	return &TalMiddleGroundUseCase{
		BaseUseCase: base,
		log:         log.NewHelper(logger),
	}
}

// SendMessageByTal 这个中台接口不支持prompt
func (t *TalMiddleGroundUseCase) SendMessageByTal(c context.Context, chatMessage, sessionId string, ch chan string, history []request.Message) (res string, err error) {
	aiWs, err := t.newDuiSocketConnect(c, t.Conf)
	if err != nil {
		t.log.WithContext(c).Warn("newDuiSocketConnect err", err)
		return "", err
	}

	var msgs []tal.GptTalMessage
	var hisMsg []tal.GptTalMessage
	if chatMessage != "" {
		msgs = append(msgs, tal.GptTalMessage{
			Role:    "user",
			Content: chatMessage,
		})
	}
	if history == nil {
		t.log.WithContext(c).Infof("SendMessageByTal: history 为空")
		history = make([]request.Message, 0)
	}
	for _, h := range history {
		hisMsg = append(hisMsg, tal.GptTalMessage{
			Role:    string(h.Role),
			Content: h.Content,
		})
	}
	msgs = append(hisMsg, msgs...)
	req := tal.GptTalRequest{
		Messages:  msgs,
		SessionID: cast.ToString(time.Now().UnixMilli()),
		UserID:    cast.ToString(time.Now().UnixMilli()),
		AskID:     cast.ToString(time.Now().UnixMilli()),
		PromptID:  time.Now().Unix(),
	}

	reqB, _ := json.Marshal(req)
	if err != nil {
		t.log.WithContext(c).Warnf("SendMessageByTal json.Marshal err %s", err.Error())
		return "", err
	}
	err = aiWs.SendText(c, reqB)
	if err != nil {
		t.log.WithContext(c).Warnf("SendMessageByTal err %s", err.Error())
		return "", err
	}
	t.log.WithContext(c).Infof("SendMessageByTal req %s", string(reqB))

	if ch == nil {
		ch = make(chan string)
		defer func() {
			for {
				var text string
				select {
				case text = <-ch:
					if text == common.EofText {
						t.log.WithContext(c).Infof("tal_returen_eof")
						return
					}
					pText := gjson.Parse(text)
					res += pText.Get("result").String()
					t.log.WithContext(c).Infof("SendMessageByTal res %s", res)
				case <-time.After(time.Second * 60):
					t.log.WithContext(c).Errorf("GptChan err %s", "返回内容超时")
					return
				}
			}
		}()
	}
	go func() {
		for {
			select {
			case <-c.Done(): // 监听超时返回
				e2 := aiWs.WsSocket.Close()
				if e2 != nil {
					t.log.WithContext(c).Warnf("tal_ws_close,err %s", e2.Error())
				} else {
					t.log.WithContext(c).Infof("tal_ws_close,success")
				}
				return
			default:
				time.Sleep(time.Second * 1)
			}
		}
	}()
	//copyCtx := c.Copy()
	go func() {
		defer func() {
			if err := recover(); err != nil {
				t.log.WithContext(c).Errorf("debugInfo :%v, [AIws-PANIC] PanicError: %v", string(debug.Stack()), err)
			}
		}()
		for {
			msgType, data, err := aiWs.WsSocket.ReadMessage()
			t.log.WithContext(c).Infof("[ReadFromAIws] ASR respMsg: %s; type: %d", string(data), msgType)
			if err != nil {
				if err == io.EOF {
					t.log.WithContext(c).Infof("[ReadFromAIws] ReadMessage err: %v", err)
					continue
				}
				t.log.WithContext(c).Errorf("[ReadFromAIws] ReadMessage err: %v", err)
				return
			}
			t.log.WithContext(c).Infof("tal_ws_result:%s,msgType:%d", string(data), msgType)
			var res tal.GptTalResponse
			err = json.Unmarshal(data, &res)
			if err != nil {
				t.log.WithContext(c).Errorf("[ReadFromAIws] json.Unmarshal err: %v", err)
				return
			}
			msg := base.ChatResult{}
			msg.Id = res.RequestID
			if res.Code != 20000 || res.Data.IsEnd == 1 {
				ch <- common.EofText
				return
			} else {
				msg.Result = res.Data.Result
				msg.Model = res.Data.Mod
				msg.Id = res.RequestID
			}
			msgB, err := json.Marshal(msg)
			if err != nil {
				fmt.Println("json.Marshal err:", err)
			}
			ch <- string(msgB)
		}
	}()
	return res, nil
}

func (s *TalMiddleGroundUseCase) newDuiSocketConnect(c context.Context, conf *conf.Data) (*ws.DuiConnect, error) {
	var urlParams = make(map[string]string)
	urlParams["mod"] = "com6b"
	appId := c.Value(common.AppId).(string)
	language := c.Value(common.Language).(string)
	if _, ok := conf.Llm.Tal.Zh.Mod[appId]; ok {
		urlParams["mod"] = conf.Llm.Tal.Zh.Mod[appId]
	}
	if language == "en" {
		if _, ok := conf.Llm.Tal.En.Mod[appId]; ok {
			urlParams["mod"] = conf.Llm.Tal.En.Mod[appId]
		}
	}
	s.log.WithContext(c).Infof("tal_gpt_socket_connect urlParams: %v, language: %v", urlParams, language)

	var requestUrl string
	var err error
	if language == "en" {
		requestUrl, err = util.GetWsSign(
			conf.Llm.Tal.En.Key,
			conf.Llm.Tal.En.Secret,
			util.GetCurrentDate(),
			conf.Llm.Tal.En.Url,
			urlParams,
		)
		if err != nil {
			s.log.WithContext(c).Error("SpeechProcessLoop.WsReadIn.Error@", err)
			return nil, err
		}
	} else {
		requestUrl, err = util.GetWsSign(
			conf.Llm.Tal.Zh.Key,
			conf.Llm.Tal.Zh.Secret,
			util.GetCurrentDate(),
			conf.Llm.Tal.Zh.Url,
			urlParams,
		)
		if err != nil {
			s.log.WithContext(c).Error("SpeechProcessLoop.WsReadIn.Error@", err)
			return nil, err
		}
	}
	s.log.WithContext(c).Infof("tal_gpt_socket_connect requestUrl: %v", requestUrl)

	aiWs, response, err := websocket.DefaultDialer.Dial(requestUrl, nil)
	if err != nil {
		s.log.WithContext(c).Warn("DefaultDialer err", err, response, requestUrl)
		return nil, err
	}

	return &ws.DuiConnect{
		WsSocket: aiWs,
		Logger:   s.log,
	}, nil
}
