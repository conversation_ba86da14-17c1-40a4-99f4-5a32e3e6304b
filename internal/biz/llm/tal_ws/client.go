package tal_ws

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"runtime/debug"
	"time"

	"llm_service/internal/biz/llm"
	"llm_service/internal/common"
	"llm_service/internal/conf"
	"llm_service/internal/data/ailab/util"
	"llm_service/internal/dto/request"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/websocket"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"

	"llm_service/internal/data/base"
	"llm_service/internal/dto/tal"
	"llm_service/internal/dto/ws"
)

type TalUseCase struct {
	*llm.BaseUseCase
	log *log.Helper
}

func NewTalUseCase(base *llm.BaseUseCase, logger log.Logger) *TalUseCase {
	return &TalUseCase{
		BaseUseCase: base,
		log:         log.NewHelper(logger),
	}
}

func (t *TalUseCase) SendMessageByTal(c context.Context, chatMessage, sessionId string, ch chan string, history []request.Message) (res string, err error) {
	aiWs, err := newDuiSocketConnect(c, t.Conf, t.log)
	if err != nil {
		t.log.WithContext(c).Warn("newDuiSocketConnect err", err)
		return "", err
	}

	var msgs []tal.GptTalMessage
	var hisMsg []tal.GptTalMessage
	if chatMessage != "" {
		msgs = append(msgs, tal.GptTalMessage{
			Role:    "user",
			Content: chatMessage,
		})
	}
	if history == nil {
		t.log.WithContext(c).Infof("SendMessageByTal: history 为空")
		history = make([]request.Message, 0)
	}
	for _, h := range history {
		hisMsg = append(hisMsg, tal.GptTalMessage{
			Role:    string(h.Role),
			Content: h.Content,
		})
	}
	msgs = append(hisMsg, msgs...)

	req := tal.GptTalRequest{
		Messages:  msgs,
		SessionID: sessionId,
		UserID:    sessionId,
		AskID:     cast.ToString(time.Now().UnixMilli()),
		PromptID:  time.Now().Unix(),
	}

	reqB, _ := json.Marshal(req)
	if err != nil {
		t.log.WithContext(c).Warnf("SendMessageByTal json.Marshal err %s", err.Error())
		return "", err
	}
	err = aiWs.SendText(c, reqB)
	if err != nil {
		t.log.WithContext(c).Warnf("SendMessageByTal err %s", err.Error())
		return "", err
	}
	t.log.WithContext(c).Infof("SendMessageByTal req %s", string(reqB))

	if ch == nil {
		ch = make(chan string)
		defer func() {
			for {
				var text string
				select {
				case text = <-ch:
					if text == common.EofText {
						t.log.WithContext(c).Infof("tal_returen_eof")
						return
					}
					pText := gjson.Parse(text)
					res += pText.Get("result").String()
					t.log.WithContext(c).Infof("SendMessageByTal res %s", res)
				case <-time.After(time.Second * 60):
					t.log.WithContext(c).Errorf("GptChan err %s", "返回内容超时")
					return
				}
			}
		}()
	}
	go func() {
		for {
			select {
			case <-c.Done(): // 监听超时返回
				e2 := aiWs.WsSocket.Close()
				if e2 != nil {
					t.log.WithContext(c).Warnf("tal_ws_close,err %s", e2.Error())
				} else {
					t.log.WithContext(c).Infof("tal_ws_close,success")
				}
				return
			default:
				time.Sleep(time.Second * 1)
			}
		}
	}()
	//copyCtx := c.Copy()
	go func() {
		defer func() {
			if err := recover(); err != nil {
				t.log.WithContext(c).Errorf("debugInfo :%v, [AIws-PANIC] PanicError: %v", string(debug.Stack()), err)
			}
		}()
		for {
			msgType, data, err := aiWs.WsSocket.ReadMessage()
			t.log.WithContext(c).Infof("[ReadFromAIws] ASR respMsg: %s; type: %d", string(data), msgType)
			if err != nil {
				if err == io.EOF {
					t.log.WithContext(c).Infof("[ReadFromAIws] ReadMessage err: %v", err)
					continue
				}
				t.log.WithContext(c).Errorf("[ReadFromAIws] ReadMessage err: %v", err)
				return
			}
			t.log.WithContext(c).Infof("tal_ws_result:%s,msgType:%d", string(data), msgType)
			var res tal.GptTalResponse
			err = json.Unmarshal(data, &res)
			if err != nil {
				t.log.WithContext(c).Errorf("[ReadFromAIws] json.Unmarshal err: %v", err)
				return
			}

			msg := base.ChatResult{}
			msg.Id = res.RequestID
			if res.Code != 20000 || res.Data.IsEnd == 1 {
				ch <- common.EofText
				return
			} else {
				msg.Result = res.Data.Result
				msg.Model = res.Data.Mod
				msg.Id = res.RequestID
			}
			msgB, err := json.Marshal(msg)
			if err != nil {
				fmt.Println("json.Marshal err:", err)
			}
			ch <- string(msgB)
		}
	}()
	return res, nil
}

func newDuiSocketConnect(c context.Context, conf *conf.Data, log *log.Helper) (*ws.DuiConnect, error) {
	var urlParams = make(map[string]string)
	//urlParams["mod"] = "com6b"
	log.WithContext(c).Infof("tal_gpt_socket_connect urlParams: %v", urlParams)

	var requestUrl string
	var err error
	requestUrl, err = util.GetWsSign(
		conf.Llm.Tal.Tal70B.Key,
		conf.Llm.Tal.Tal70B.Secret,
		util.GetCurrentDate(),
		conf.Llm.Tal.Tal70B.Url,
		urlParams,
	)
	if err != nil {
		log.WithContext(c).Error("SpeechProcessLoop.WsReadIn.Error@", err)
		return nil, err
	}
	log.WithContext(c).Infof("tal_gpt_socket_connect requestUrl: %v", requestUrl)

	aiWs, response, err := websocket.DefaultDialer.Dial(requestUrl, nil)
	if err != nil {
		log.WithContext(c).Warn("DefaultDialer err", err, response, requestUrl)
		return nil, err
	}

	return &ws.DuiConnect{
		WsSocket: aiWs,
		Logger:   log,
	}, nil
}
