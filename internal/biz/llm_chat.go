package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"llm_service/internal/biz/legal"
	"llm_service/internal/biz/llm/tal_ws"
	"llm_service/internal/biz/prompt"
	"llm_service/internal/common"
	"llm_service/internal/conf"
	"llm_service/internal/data"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"llm_service/internal/dto/response"
	"runtime/debug"
	"strconv"
	"strings"
	"time"
	"unicode"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/pkoukk/tiktoken-go"
)

// 错误类型常量
const (
	errInvalidRequest      = "请求参数错误"
	errContentViolation    = "提问内容不合规"
	errImageViolation      = "图片内容不合规"
	errOutputViolation     = "输出内容不合规"
	errRequestTooLong      = "请求内容过长"
	errPromptFailed        = "获取prompt失败"
	errLlmRequestFailed    = "请求GPT失败"
	errBusinessTypeInvalid = "不存在的业务类型"
	errStrategyInvalid     = "策略配置错误"
	errEmptyLlmResponse    = "llm 返回结果为空"
)

type SseUseCase struct {
	conf                            *conf.Data
	db                              *data.Data
	log                             *log.Helper
	talWs                           *tal_ws.TalUseCase
	prom                            *prompt.Prompt
	legalBiz                        *legal.LegalBizUseCase
	sseBaiChuan                     *SSEBaiChuan
	sseAzure4                       *SSEAzure4
	sseTal70B                       *SSETal70B
	sseTalMiddleGround              *SSETalMiddleGround
	sseErnie                        *SSEErnie
	sseBaidu                        *SSEBaidu
	sseTalComposition               *SSETalComposition
	sseTalMathGPTBaseBackend        *SSETalMathGPTBaseBackend
	sseTongYiQwenPlus               *SSETongYiQwenPlusOpenAi
	sseTongYiQwenMax                *SSETongYiQwenMaxOpenAi
	sseTongYiQwen72B                *SSETongYiQwen72BOpenAi
	sseGPT4Turbo                    *SSEGPT4Turbo
	sseGPT4Exclusive                *SSEGPT4Exclusive
	sseTal7B                        *SSETal7B
	sseLlama370b                    *SseLlama370b
	sseTalEnDialogue                *SSETalEnDialogue
	sseLlama370bModelSquare         *SSELlama370bModelSquare
	sseDouBaoLite32k                *SSEDouBaoLite32kOpenAi
	sseDouBaoPro32k                 *SSEDouBaoPro32kOpenAi
	sseDeepSeekReasoner             *SSEDeepSeekReasonerOpenAi
	sseDeepSeekV3                   *SSEDeepSeekV3OpenAi
	sseDeepSeekDistillQwen32BOpenAi *SSEDeepSeekDistillQwen32BOpenAi
	MultiModalClient                *MultiModalClient
	claude37SonnetOpenAi            *SSEClaude37SonnetOpenAi
}

// prepareRequest 处理请求前置逻辑，包括请求头处理、内容检查、历史记录处理和prompt获取
func (s *SseUseCase) prepareRequest(ctx context.Context, appId string, req *request.LlmCommonRequest) (gptReqs []*request.GptReq, err error) {
	// 处理请求头信息
	s.processRequestHeaders(ctx, req)
	s.statisticsSnTalId(ctx, req)
	// 内容安全检查
	if !req.MsgNoCheck {
		if err = s.checkRequestContent(ctx, req); err != nil {
			return nil, err
		}
	}

	// 处理历史记录，避免超长
	if err = s.processHistory(ctx, req); err != nil {
		return nil, err
	}

	// 获取prompt模板
	if req.TmpId != "" {
		gptReqs, err = s.getPromptTemplates(ctx, appId, req)
		if err != nil {
			return nil, err
		}
	}

	return gptReqs, nil
}

// processRequestHeaders 处理请求头信息
func (s *SseUseCase) processRequestHeaders(ctx context.Context, req *request.LlmCommonRequest) {
	headers := s.GetRequestHeaders(ctx)
	if headers == nil {
		return
	}

	userAgent := s.GetRequestHeader(ctx, common.HeaderUserAgent)
	contentType := s.GetRequestHeader(ctx, common.HeaderContentType)
	deviceId := s.GetRequestHeader(ctx, common.HeaderXGenieDeviceId)
	s.log.WithContext(ctx).Infof("获取到的请求头信息: UA=%s, ContentType=%s, DeviceId=%s", userAgent, contentType, deviceId)

	// 如果请求中没有设置Sn，且请求头中有设备ID，则使用设备ID作为Sn
	if deviceId != "" && req.Sn == "" {
		s.log.WithContext(ctx).Infof("从请求头获取设备ID作为Sn: %s", deviceId)
		req.Sn = deviceId
	}
}

// checkRequestContent 检查请求内容是否合规
func (s *SseUseCase) checkRequestContent(ctx context.Context, req *request.LlmCommonRequest) error {
	// 处理多模态消息检查
	if req.MultiMessage != nil {
		if err := s.checkMultiModalContent(ctx, req); err != nil {
			return err
		}
	}

	// 检查文本内容
	legalRes, err := s.legalBiz.LegalCheck(ctx, &legal.CheckReq{
		Biz:   req.Biz,
		Mode:  legal.Input,
		Text:  req.Message,
		Sn:    req.Sn,
		TalId: req.TalId,
	})

	if err != nil {
		s.log.WithContext(ctx).Errorf("check find sensitive words:%+v is err:%v", legalRes.RiskWords, err)
		return common.New(common.ECode120014, "提问内容不合规")
	}

	if legalRes != nil && legalRes.RiskLabelText != "" {
		s.log.WithContext(ctx).Warnf("check find sensitive words:%+v", legalRes.RiskWords)
		return common.New(common.ECode120014, "提问内容不合规")
	}

	return nil
}

// checkMultiModalContent 检查多模态内容是否合规
func (s *SseUseCase) checkMultiModalContent(ctx context.Context, req *request.LlmCommonRequest) error {
	// 将 interface{} 转换为 []byte
	s.log.WithContext(ctx).Infof("收到多模态消息: %+v", req.MultiMessage)

	jsonBytes, err := json.Marshal(req.MultiMessage)
	if err != nil {
		s.log.WithContext(ctx).Errorf("marshal multi message error: %v, data: %+v", err, req.MultiMessage)
		return common.New(common.ECode120014, "请求参数错误")
	}

	// 创建临时数组接收数据
	var messages []request.MultiModalMessage
	if err := json.Unmarshal(jsonBytes, &messages); err != nil {
		s.log.WithContext(ctx).Errorf("unmarshal multi message error: %v, json: %s", err, string(jsonBytes))
		return common.New(common.ECode120014, "请求参数错误")
	}

	// 验证解析后的数据
	for _, msg := range messages {
		switch msg.Type {
		case "text":
			if msg.Text == "" {
				s.log.WithContext(ctx).Errorf("text message is empty")
				return common.New(common.ECode120014, "文本消息不能为空")
			}

			// 对文本内容进行合规检查
			legalRes, err := s.legalBiz.LegalCheck(ctx, &legal.CheckReq{
				Biz:   req.Biz,
				Mode:  legal.Input,
				Text:  msg.Text,
				TalId: req.TalId,
				Sn:    req.Sn,
			})

			if err != nil {
				s.log.WithContext(ctx).Errorf("text content check failed: %+v is err:%v", legalRes.RiskWords, err)
				return common.New(common.ECode120014, "提问内容不合规")
			}

			if legalRes != nil && legalRes.RiskLabelText != "" {
				s.log.WithContext(ctx).Warnf("text content check found sensitive words:%+v", legalRes.RiskWords)
				return common.New(common.ECode120014, "提问内容不合规")
			}

		case "image_url":
			if msg.ImageUrl == nil || msg.ImageUrl.Url == "" {
				s.log.WithContext(ctx).Errorf("image url is empty")
				return common.New(common.ECode120014, "图片URL不能为空")
			}

			// 对图片URL进行合规检查
			legalRes, err := s.legalBiz.LegalCheck(ctx, &legal.CheckReq{
				Biz:    req.Biz,
				Mode:   legal.Input,
				ImgUrl: msg.ImageUrl.Url,
				Sn:     req.Sn,
				TalId:  req.TalId,
			})

			if err != nil {
				s.log.WithContext(ctx).Errorf("image content check failed: %+v is err:%v", legalRes.RiskWords, err)
				return common.New(common.ECode120014, "图片内容不合规")
			}

			if legalRes != nil && legalRes.RiskLabelText != "" {
				s.log.WithContext(ctx).Warnf("image content check found sensitive content:%+v", legalRes.RiskWords)
				return common.New(common.ECode120014, "图片内容不合规")
			}

		default:
			s.log.WithContext(ctx).Errorf("unsupported message type: %s", msg.Type)
			return common.New(common.ECode120014, "不支持的消息类型")
		}
	}

	return nil
}

// processHistory 处理历史记录，避免超长
func (s *SseUseCase) processHistory(ctx context.Context, req *request.LlmCommonRequest) error {
	if len(req.History) == 0 {
		return nil
	}

	var tokenCheckHis []request.Record
	lh := len(req.History)
	i := 0

	for i < lh {
		if req.History[i].Role == request.UserRole {
			if req.History[i].Content == "" || (i+1 < lh && req.History[i+1].Content == "") {
				i += 2
				continue
			}
		}

		tokenCheckHis = append(tokenCheckHis, request.Record{
			Type: request.RoleTypeMap[req.History[i].Role],
			Msg:  req.History[i].Content,
		})
		i++
	}

	reqData := request.Chat{
		History: tokenCheckHis,
		Message: req.Message,
	}

	check, checkedHis := s.DescDealHistoryMsg(ctx, reqData)
	if !check {
		s.log.WithContext(ctx).Infof("DescDealHistoryMsg check is false: %v", "请求内容过长")
		return common.New(common.ECode160000, "请求内容过长")
	}

	// 重新填入历史，避免超长
	req.History = []request.Message{}
	for _, r := range checkedHis {
		req.History = append(req.History, request.Message{
			Role:    request.RoleMap[r.Type],
			Content: r.Msg,
		})
	}

	return nil
}

// getPromptTemplates 获取prompt模板
func (s *SseUseCase) getPromptTemplates(ctx context.Context, appId string, req *request.LlmCommonRequest) ([]*request.GptReq, error) {
	var gptReqs []*request.GptReq

	prompts, err := s.prom.GetPrompt(ctx, appId, req.TmpId, req.Args)
	if err != nil {
		s.log.WithContext(ctx).Warnf("GetPrompt err %s", err.Error())
		return nil, common.New(common.ECode190000, errPromptFailed)
	}

	for _, p := range prompts {
		tmp := request.GptReq{
			Msg:      p.Msg,
			Strategy: fmt.Sprintf("%d:%d", p.GptType, 1),
		}
		gptReqs = append(gptReqs, &tmp)
	}

	return gptReqs, nil
}

// parseStrategy 解析策略配置
func (s *SseUseCase) parseStrategy(strategy string) (int, error) {
	strategySplit := strings.Split(strategy, ":")
	if len(strategySplit) < 2 {
		return 0, common.New(common.ECode110040, errBusinessTypeInvalid)
	}

	businessType, err := strconv.Atoi(strategySplit[0])
	if err != nil {
		return 0, common.New(common.ECode110050, errStrategyInvalid)
	}

	_, err = strconv.Atoi(strategySplit[1])
	if err != nil {
		return 0, common.New(common.ECode110050, errStrategyInvalid)
	}

	return businessType, nil
}

// logModelRequest 记录模型请求日志
func (s *SseUseCase) logModelRequest(ctx context.Context, req *request.LlmCommonRequest, businessType, reqTimes, totalReqs int) {
	s.log.WithContext(ctx).Infof("sse common req: %+v, type:%v, desc:%v, req_times_%d",
		req, businessType, common.GptTypeMap[businessType], reqTimes)
	s.log.WithContext(ctx).Infof("sse_use_retry, gpt_type_%s, biz is %v, 正在请求模版Id %v 中第 %d 个大模型",
		common.GptTypeMap[businessType], req.Biz, req.TmpId, reqTimes)

	if reqTimes == 1 {
		s.log.WithContext(ctx).Infow("template_id", req.TmpId, "use_model", reqTimes, "is_last", totalReqs == reqTimes)
	} else if totalReqs == reqTimes {
		s.log.WithContext(ctx).Warnw("template_id", req.TmpId, "use_model", reqTimes, "is_last", totalReqs == reqTimes)
	}
}

// createChatRequest 为不同的模型类型创建对应的聊天请求
func (s *SseUseCase) createChatRequest(req *request.LlmCommonRequest, businessType int, promptMsg string) dto.SSECommonChat {
	// 创建基础聊天请求
	chat := dto.SSECommonChat{
		History:           req.History,
		MultiModalHistory: req.MultiModalHistory, // 使用多模态历史记录
		Message:           req.Message,
		Prompt:            promptMsg,
		Temperature:       req.Temperature,
		EnableSearch:      req.EnableSearch,
		MultiMessage:      req.MultiMessage,
	}

	// 根据不同模型类型设置特殊参数
	switch businessType {
	case common.GptTypeAzure4:
		// Azure GPT-4使用基础配置即可

	case common.GptTypeGPT4Turbo, common.GptTypeGPTExclusive:
		// GPT-4 Turbo和Exclusive使用基础配置即可

	case common.GptTypeTal70B:
		isCheck := int(s.conf.Llm.Tal.Tal70B.IsCheck)
		chat.Message = promptMsg // 注意：这里覆盖了默认消息
		chat.IsCheck = &isCheck

	case common.GptTypeLlama3_70b:
		talConf := s.conf.Llm.Tal.Llama3_70B
		isCheck := int(talConf.IsCheck)
		chat.IsCheck = &isCheck
		chat.ApiKey = talConf.Key
		chat.URL = talConf.Url

	case common.GptTypeTal7B:
		talConf := s.conf.Llm.Tal.Tal7B
		isCheck := int(talConf.IsCheck)
		chat.IsCheck = &isCheck
		chat.ApiKey = talConf.Key
		chat.URL = talConf.Url

	case common.GptTypeBaiChuan:
		baiChuanConf := s.conf.Llm.Baichuan
		isCheck := int(baiChuanConf.IsCheck)
		chat.Model = baiChuanConf.Model
		chat.Message = promptMsg // 覆盖默认消息
		chat.Temperature = baiChuanConf.Temperature
		chat.WithSearchEnhance = &baiChuanConf.WithSearchEnhance
		chat.IsCheck = &isCheck

	case common.GptTypeErnieBot:
		ernieConf := s.conf.Llm.Ernie.ErnieBot
		isCheck := int(ernieConf.IsCheck)
		chat.Model = ernieConf.Model
		chat.IsCheck = &isCheck
		chat.ApiKey = ernieConf.ApiKey
		chat.URL = ernieConf.Url
		if req.Temperature != 0 {
			chat.Temperature = req.Temperature
		} else {
			chat.Temperature = ernieConf.Temperature
		}

	case common.GptTypeTal:
		chat.Message = promptMsg // 覆盖默认消息

	case common.GptTypeBaiduV1, common.GptTypeBaiduV2, common.GptTypeBaiduPrivate, common.GptTypeBaiduPrivate2:
		baiduConf := s.conf.Llm.Baidu
		chat.Version = common.GptTypeMap[businessType]
		chat.URL = baiduConf.Url

	case common.GptTypeTalEnComposition, common.GptTypeTalChComposition:
		talConf := s.conf.Llm.Tal.EnComposition
		if businessType == common.GptTypeTalChComposition {
			talConf = s.conf.Llm.Tal.ChComposition
		}
		isCheck := int(talConf.IsCheck)
		chat.Version = fmt.Sprintf("%v", businessType)
		chat.IsCheck = &isCheck
		chat.ApiKey = talConf.Key
		chat.URL = talConf.Url

	case common.GptTypeMathGPTBaseBackend:
		talConf := s.conf.Llm.Tal.MathGptBaseBackend
		chat.ApiKey = talConf.Key
		chat.URL = talConf.Url

	case common.GptTypeTalEnglishDialogue:
		talConf := s.conf.Llm.Tal.EnDialogue
		chat.ApiKey = talConf.Key
		chat.URL = talConf.Url

	case common.GptTypeTongyiQwenMax:
		qwenConf := s.conf.Llm.Tongyi.QwenMax
		isCheck := int(qwenConf.IsCheck)
		chat.Model = qwenConf.Model
		chat.IsCheck = &isCheck
		chat.ApiKey = qwenConf.ApiKey
		chat.URL = qwenConf.Url

	case common.GptTypeTongyiQwenPlus:
		qwenConf := s.conf.Llm.Tongyi.QwenPlus
		chat.Model = qwenConf.Model
		chat.ApiKey = qwenConf.ApiKey
		chat.URL = qwenConf.Url

	case common.GptTypeQwen72B:
		qwenConf := s.conf.Llm.Tongyi.Qwen72B
		isCheck := int(qwenConf.IsCheck)
		chat.Model = qwenConf.Model
		chat.IsCheck = &isCheck
		chat.ApiKey = qwenConf.ApiKey
		chat.URL = qwenConf.Url

	case common.GptTypeLlama370bModelSquare:
		llama3Conf := s.conf.Llm.Tal.Llama3_70BModelSquare
		chat.ApiKey = llama3Conf.ApiKey
		chat.URL = llama3Conf.Url
		chat.Temperature = 0.3
		chat.TopP = 0.8

	case common.GptTypeDouBaoPro32k:
		douBaoPro32kConf := s.conf.Llm.Doubao.DouBaoPro32K
		chat.Model = douBaoPro32kConf.Model
		chat.ApiKey = douBaoPro32kConf.ApiKey
		chat.URL = douBaoPro32kConf.Url

	case common.GptTypeDouBaoLite32k:
		douBaoLite32kConf := s.conf.Llm.Doubao.DouBaoLite32K
		chat.Model = douBaoLite32kConf.Model
		chat.ApiKey = douBaoLite32kConf.ApiKey
		chat.URL = douBaoLite32kConf.Url

	case common.GptTypeDeepSeekReasoner:
		deepSeekR1Conf := s.conf.Llm.DeepSeek.DeepSeekR1
		chat.Model = deepSeekR1Conf.Model
		chat.ApiKey = deepSeekR1Conf.ApiKey
		chat.URL = deepSeekR1Conf.Url

	case common.GptTypeDeepSeekV3:
		deepSeekV3Conf := s.conf.Llm.DeepSeek.DeepSeekV3
		chat.Model = deepSeekV3Conf.Model
		chat.ApiKey = deepSeekV3Conf.ApiKey
		chat.URL = deepSeekV3Conf.Url

	case common.GptTypeDeepSeekR1DistillQwen32B:
		deepSeekR1DistillQwen32B := s.conf.Llm.DeepSeek.DeepSeekR1DistillQwen32B
		chat.Model = deepSeekR1DistillQwen32B.Model
		chat.ApiKey = deepSeekR1DistillQwen32B.ApiKey
		chat.URL = deepSeekR1DistillQwen32B.Url

	case common.GptTypeQwenVlMax:
		qwenVlMaxConf := s.conf.Llm.MultiModal.QwenVlMax
		chat.Model = qwenVlMaxConf.Model
		chat.ApiKey = qwenVlMaxConf.ApiKey
		chat.URL = qwenVlMaxConf.Url

	case common.GptTypeGlm4vflash:
		glm4vflashConf := s.conf.Llm.MultiModal.Glm4Vflash
		chat.Model = glm4vflashConf.Model
		chat.ApiKey = glm4vflashConf.ApiKey
		chat.URL = glm4vflashConf.Url

	case common.GptTypeDoubaoProVision32k:
		doubaoProVision32kConf := s.conf.Llm.MultiModal.DoubaoProVision32K
		chat.Model = doubaoProVision32kConf.Model
		chat.ApiKey = doubaoProVision32kConf.ApiKey
		chat.URL = doubaoProVision32kConf.Url
	case common.GptTypeClaude37Sonnet:
		claude37SonnetConf := s.conf.Llm.Anthropic.Claude37Sonnet
		chat.Model = claude37SonnetConf.Model
		chat.ApiKey = claude37SonnetConf.ApiKey
		chat.URL = claude37SonnetConf.Url
	}

	return chat
}

// callModelAPI 根据模型类型调用相应的API
func (s *SseUseCase) callModelAPI(ctx context.Context, req *request.LlmCommonRequest, businessType int, promptMsg string) (<-chan dto.Data, error) {
	// 创建聊天请求
	chat := s.createChatRequest(req, businessType, promptMsg)

	// 根据不同模型类型调用对应的API
	switch businessType {
	case common.GptTypeAzure4:
		return s.sseAzure4.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeGPT4Turbo:
		return s.sseGPT4Turbo.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeGPTExclusive:
		return s.sseGPT4Exclusive.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeTal70B:
		return s.sseTal70B.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeLlama3_70b:
		return s.sseLlama370b.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeTal7B:
		return s.sseTal7B.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeBaiChuan:
		return s.sseBaiChuan.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeErnieBot:
		return s.sseErnie.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeTal:
		return s.sseTalMiddleGround.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeBaiduV1, common.GptTypeBaiduV2, common.GptTypeBaiduPrivate, common.GptTypeBaiduPrivate2:
		return s.sseBaidu.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeTalEnComposition, common.GptTypeTalChComposition:
		return s.sseTalComposition.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeMathGPTBaseBackend:
		return s.sseTalMathGPTBaseBackend.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeTalEnglishDialogue:
		return s.sseTalEnDialogue.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeTongyiQwenMax:
		return s.sseTongYiQwenMax.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeTongyiQwenPlus:
		return s.sseTongYiQwenPlus.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeQwen72B:
		return s.sseTongYiQwen72B.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeLlama370bModelSquare:
		return s.sseLlama370bModelSquare.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeDouBaoPro32k:
		return s.sseDouBaoPro32k.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeDouBaoLite32k:
		return s.sseDouBaoLite32k.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeDeepSeekReasoner:
		return s.sseDeepSeekReasoner.SseCommonChat(ctx, req.Biz, chat)
	case common.GptTypeDeepSeekV3:
		return s.sseDeepSeekV3.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeDeepSeekR1DistillQwen32B:
		return s.sseDeepSeekDistillQwen32BOpenAi.SseCommonChat(ctx, req.Biz, chat)

	case common.GptTypeQwenVlMax:
		return s.MultiModalClient.SseCommonChat(ctx, req.Biz, chat, common.GptTypeQwenVlMax)

	case common.GptTypeGlm4vflash:
		return s.MultiModalClient.SseCommonChat(ctx, req.Biz, chat, common.GptTypeGlm4vflash)

	case common.GptTypeDoubaoProVision32k:
		return s.MultiModalClient.SseCommonChat(ctx, req.Biz, chat, common.GptTypeDoubaoProVision32k)
	case common.GptTypeClaude37Sonnet:
		return s.claude37SonnetOpenAi.SseCommonChat(ctx, req.Biz, chat)

	default:
		return nil, common.New(common.ECode110030, errBusinessTypeInvalid)
	}
}

func NewSseUseCase(conf *conf.Data, data *data.Data,
	tal *tal_ws.TalUseCase,
	prom *prompt.Prompt,
	sseBaiChuan *SSEBaiChuan,
	sseAzure4 *SSEAzure4,
	sseGPT4Turbo *SSEGPT4Turbo,
	sseGPT4Exclusive *SSEGPT4Exclusive,
	sseTal70B *SSETal70B,
	sseTalMiddleGround *SSETalMiddleGround,
	sseTal7B *SSETal7B,
	sseErnie *SSEErnie,
	sseBaidu *SSEBaidu,
	sseTalComposition *SSETalComposition,
	sseTalMathGPTBaseBackend *SSETalMathGPTBaseBackend,
	legalBiz *legal.LegalBizUseCase,
	sseTongYiQwenMax *SSETongYiQwenMaxOpenAi,
	sseTongYiQwenPlus *SSETongYiQwenPlusOpenAi,
	sseTongYiQwen72B *SSETongYiQwen72BOpenAi,
	sseLlama370b *SseLlama370b,
	sseTalEnDialogue *SSETalEnDialogue,
	sseLlama370bModelSquare *SSELlama370bModelSquare,
	sseDouBaoLite32k *SSEDouBaoLite32kOpenAi,
	sseDouBaoPro32k *SSEDouBaoPro32kOpenAi,
	sseDeepSeekReasoner *SSEDeepSeekReasonerOpenAi,
	sseDeepSeekV3 *SSEDeepSeekV3OpenAi,
	sseDeepSeekDistillQwen32BOpenAi *SSEDeepSeekDistillQwen32BOpenAi,
	MultiModalClient *MultiModalClient,
	claude37SonnetOpenAi *SSEClaude37SonnetOpenAi,
	logger log.Logger) *SseUseCase {
	return &SseUseCase{
		conf:                            conf,
		db:                              data,
		log:                             log.NewHelper(logger),
		talWs:                           tal,
		prom:                            prom,
		sseBaiChuan:                     sseBaiChuan,
		sseAzure4:                       sseAzure4,
		sseTal70B:                       sseTal70B,
		sseTalMiddleGround:              sseTalMiddleGround,
		sseErnie:                        sseErnie,
		sseBaidu:                        sseBaidu,
		sseTalComposition:               sseTalComposition,
		sseTalMathGPTBaseBackend:        sseTalMathGPTBaseBackend,
		legalBiz:                        legalBiz,
		sseTal7B:                        sseTal7B,
		sseTongYiQwenMax:                sseTongYiQwenMax,
		sseTongYiQwenPlus:               sseTongYiQwenPlus,
		sseGPT4Turbo:                    sseGPT4Turbo,
		sseGPT4Exclusive:                sseGPT4Exclusive,
		sseTongYiQwen72B:                sseTongYiQwen72B,
		sseLlama370b:                    sseLlama370b,
		sseTalEnDialogue:                sseTalEnDialogue,
		sseLlama370bModelSquare:         sseLlama370bModelSquare,
		sseDouBaoLite32k:                sseDouBaoLite32k,
		sseDouBaoPro32k:                 sseDouBaoPro32k,
		sseDeepSeekReasoner:             sseDeepSeekReasoner,
		sseDeepSeekV3:                   sseDeepSeekV3,
		sseDeepSeekDistillQwen32BOpenAi: sseDeepSeekDistillQwen32BOpenAi,
		MultiModalClient:                MultiModalClient,
		claude37SonnetOpenAi:            claude37SonnetOpenAi,
	}
}

func (s *SseUseCase) SSECommon(ctx context.Context, appId string, req *request.LlmCommonRequest) (event <-chan dto.Data, err error) {
	defer func() {
		if r := recover(); r != nil {
			s.log.WithContext(ctx).Errorf("SseCommon.Panic,err(%+v)", r)
		}
	}()

	// 前置处理：请求头处理、内容检查、历史记录处理和prompt获取
	gptReqs, err := s.prepareRequest(ctx, appId, req)
	if err != nil {
		return nil, err
	}

	reqTimes := 0
	// message单次请求，模板分多次发起请求
	for _, gptReq := range gptReqs {
		if gptReq.Msg == "" {
			s.log.WithContext(ctx).Warnf("请求模版Id %v 中第 %d 个大模型时 prompt 为空，大模型将不会设置system", req.TmpId, reqTimes)
		}
		reqTimes++
		promptMsg := gptReq.Msg
		strategy := gptReq.Strategy

		// 解析策略
		businessType, err := s.parseStrategy(strategy)
		if err != nil {
			return nil, err
		}

		// 日志记录
		s.logModelRequest(ctx, req, businessType, reqTimes, len(gptReqs))

		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			s.log.WithContext(ctx).Warn("sse common ctx is done 停止继续降级并退出")
			return nil, nil
		default:
		}

		// 使用辅助函数调用模型API
		event, err = s.callModelAPI(ctx, req, businessType, promptMsg)
		if err != nil {
			s.log.WithContext(ctx).Errorf("SseCommonChat 调用出错 strategy=%s req=%s err=%v", strategy, req.Message, err)
			continue
		}
		s.log.WithContext(ctx).Infof("请求GPT,strategy=%s req=%s ", strategy, req.Message)

		event, err = s.SSECheck(ctx, event, req, common.GptTypeMap[businessType], req.OutputNoCheck)
		if err != nil {
			s.log.WithContext(ctx).Errorf("SSECheck 调用出错 strategy=%s req=%s err=%v", strategy, req.Message, err)
			continue
		}
		if err == nil {
			break // 模板查询到一个之后, 跳出
		}
	}

	if err != nil {
		return nil, common.New(common.ECode150000, "请求GPT失败")
	}

	return event, nil
}

func (s *SseUseCase) Common(ctx context.Context, appId string, req *request.LlmCommonRequest) (resp *response.LlmCommonResp, err error) {
	defer func() {
		if err := recover(); err != nil {
			s.log.WithContext(ctx).Errorf("Common.Panic,err(%+v)", err)
		}
	}()

	// 前置处理：请求头处理、内容检查、历史记录处理和prompt获取
	gptReqs, err := s.prepareRequest(ctx, appId, req)
	if err != nil {
		return nil, err
	}

	reqTimes := 0
	// message单次请求，模板分多次发起请求
	for _, gptReq := range gptReqs {
		if gptReq.Msg == "" {
			s.log.WithContext(ctx).Warnf("请求模版Id %v 中第 %d 个大模型时 prompt 为空，大模型将不会设置system", req.TmpId, reqTimes)
		}
		reqTimes++
		promptMsg := gptReq.Msg
		strategy := gptReq.Strategy

		// 解析策略
		businessType, err := s.parseStrategy(strategy)
		if err != nil {
			return nil, err
		}

		// 验证是否支持非流式返回
		if _, ok := common.GptNotStreamTypeMap[businessType]; !ok {
			return nil, common.New(common.ECode110050, fmt.Sprintf("暂未支持此模型:模型id:%v,名称:%v", businessType, common.GptTypeMap[businessType]))
		}

		// 日志记录
		s.logModelRequest(ctx, req, businessType, reqTimes, len(gptReqs))

		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			s.log.WithContext(ctx).Warn("common ctx is done 停止继续降级并退出")
			return nil, nil
		default:
		}

		// 设置超时
		ctxWithTimeout, _ := context.WithTimeout(ctx, time.Minute*3)

		// 目前只支持中文作文模型的非流式调用
		switch businessType {
		case common.GptTypeTalChComposition:
			chat := s.createChatRequest(req, businessType, promptMsg)
			resp, err = s.sseTalComposition.CommonChat(ctxWithTimeout, req.Biz, chat)
		default:
			return nil, common.New(common.ECode110030, errBusinessTypeInvalid)
		}

		if err != nil {
			s.log.WithContext(ctx).Errorf("CommonChat 调用出错 strategy=%s req=%s err=%v", strategy, req.Message, err)
			continue
		}
		s.log.WithContext(ctx).Infof("请求GPT,strategy=%s req=%s", strategy, req.Message)

		// 输出内容检查
		if !req.OutputNoCheck {
			legalRes, _ := s.legalBiz.LegalCheck(ctxWithTimeout, &legal.CheckReq{
				Biz:   req.Biz,
				Mode:  legal.Output,
				Text:  resp.Result,
				Sn:    req.Sn,
				TalId: req.TalId,
			})
			if legalRes != nil && legalRes.RiskLabelText != "" {
				s.log.WithContext(ctx).Errorf("输出内容检查发现敏感词:%+v", legalRes.RiskWords)
				return nil, common.New(common.ECode120014, errOutputViolation)
			}
		}

		// 成功获取到一个响应，退出循环
		if err == nil {
			break
		}
	}

	if err != nil {
		return nil, common.New(common.ECode150000, errLlmRequestFailed)
	}

	return resp, nil
}

func (s *SseUseCase) statisticsSnTalId(ctx context.Context, body *request.LlmCommonRequest) {
	if body.Sn != "" {
		s.log.WithContext(ctx).Infof("Sn统计:%v,biz:%v", body.Sn, body.Biz)
	}
	if body.TalId != "" {
		s.log.WithContext(ctx).Infof("TalId统计:%v,biz:%v", body.TalId, body.Biz)
	}
}

// DescDealHistoryMsg 大模型统一接口调用
func (s *SseUseCase) DescDealHistoryMsg(ctx context.Context, chatMessage request.Chat) (bool, []request.Record) {
	if len(chatMessage.History) == 0 {
		return true, nil
	}

	//历史消息token
	result := make([]request.Record, 0)
	histories := chatMessage.History
	//反转histories切片
	for i, j := 0, len(histories)-1; i < j; i, j = i+1, j-1 {
		histories[i], histories[j] = histories[j], histories[i]
	}
	historyMaxToken := 6000
	tokens := 0
	for _, h := range histories {
		if h.Type == 0 {
			continue
		}
		if tokens > historyMaxToken {
			break
		}
		result = append(result, h)
		tokens = tokens + CalcTokens(h.Msg, "")
	}
	s.log.WithContext(ctx).Infof("llm_history, tokens:%d,chatMessage:%v", tokens, chatMessage)
	// 百度要求历史记录必须成对，这里考虑把无法组成问答组的历史记录剔除
	if len(result)%2 != 0 {
		result = result[:len(result)-1]
	}
	//反转result切片
	for i, j := 0, len(result)-1; i < j; i, j = i+1, j-1 {
		result[i], result[j] = result[j], result[i]
	}

	return true, result
}

func CalcTokens(msg, model string) int {

	// 统计其他模型消耗的 token 数
	chineseCount := 0
	englishCount := 0
	for _, v := range msg {
		if unicode.Is(unicode.Han, v) {
			chineseCount++
		} else if unicode.Is(unicode.Latin, v) {
			englishCount++
		}
	}
	tokenCount := chineseCount*2 + englishCount

	// 统计 open-ai gpt 消耗的 token 数
	if model == "gpt-4" || model == "gpt-3.5-turbo" {
		tkm, err := tiktoken.EncodingForModel(model)
		if err != nil {
			return CalcTokens(msg, "")
		}
		token := tkm.Encode(msg, nil, nil)
		tokenCount = len(token)
	}
	return tokenCount
}

// sseProcessState 用于管理SSECheck函数中的状态变量
type sseProcessState struct {
	firstResponse  bool
	messageCount   int
	tmpRes         string
	lastRes        string
	allRes         string
	lastIsDeepSeek bool
}

// 处理错误响应并发送到输出通道
func (s *SseUseCase) sendErrorResponse(
	defaultData dto.Data,
	extError *common.ExtError,
	state *sseProcessState,
	d dto.Data,
	outCh chan<- dto.Data) {

	defaultData.ErrorCode = common.GetErrorCode(extError)
	defaultData.ErrorMsg = extError.Message
	defaultData.Sensitive = d.Sensitive
	defaultData.Result = state.tmpRes
	outCh <- defaultData
}

// 发送正常响应到输出通道
func (s *SseUseCase) sendNormalResponse(
	d dto.Data,
	state *sseProcessState,
	source string,
	outCh chan<- dto.Data) {
	outCh <- dto.Data{
		ErrorCode:  0,
		ErrorMsg:   "",
		Id:         d.Id,
		Object:     d.Object,
		Created:    d.Created,
		IsDeepSeek: d.IsDeepSeek,
		SentenceId: 0,
		IsEnd:      d.IsEnd,
		Result:     state.tmpRes,
		Model:      d.Model,
		Source:     source,
		Usage: dto.Usage{
			PromptTokens:     d.Usage.PromptTokens,
			CompletionTokens: d.Usage.CompletionTokens,
			TotalTokens:      d.Usage.TotalTokens,
		},
	}
}

// 处理文本分段
func (s *SseUseCase) processText(source string, d dto.Data, state *sseProcessState) bool {
	// 判断是否包含标点符号
	havePunct := state.messageCount == 0 // 第一条消息总是处理

	punctuation := ""
	codes := []rune(d.Result)
	for _, code := range codes {
		if source != common.GptTypeMap[common.GptTypeTal] {
			if unicode.IsPunct(code) && code != '\'' && code != '-' {
				havePunct = true
				punctuation = fmt.Sprintf("%c", code)
				break
			}
		} else {
			if unicode.IsPunct(code) {
				havePunct = true
				punctuation = fmt.Sprintf("%c", code)
				break
			}
		}
	}

	// 处理文本
	state.tmpRes = state.lastRes + state.tmpRes
	state.lastRes = ""
	splitList := splitAtFirstCharacter(d.Result, punctuation)
	if len(splitList) == 1 {
		state.tmpRes = state.tmpRes + splitList[0]
	} else if len(splitList) == 2 {
		state.tmpRes = state.tmpRes + splitList[0]
		state.lastRes = splitList[1]
	}

	return havePunct
}

// 处理响应中的错误
func (s *SseUseCase) processErrorInResponse(
	ctx context.Context,
	d dto.Data,
	defaultData dto.Data,
	outCh chan<- dto.Data,
	extError **common.ExtError) bool {

	if d.ErrorCode > 0 {
		if d.ErrorCode == common.OvertimeError {
			s.log.WithContext(ctx).Warnf("sse_resp_error, 请求超时 d:%v", d)
		} else {
			s.log.WithContext(ctx).Errorf("sse_resp_error, d:%v", d)
		}
		*extError = common.New(common.ECode150000, d.ErrorMsg)
		defaultData.ErrorCode = common.GetErrorCode(*extError)
		defaultData.ErrorMsg = (*extError).Message
		outCh <- defaultData
		return true
	}
	return false
}

// 处理特殊模型
func (s *SseUseCase) handleSpecialModels(
	ctx context.Context,
	req *request.LlmCommonRequest,
	source string,
	state *sseProcessState,
	d dto.Data) {

	if state.lastIsDeepSeek != d.IsDeepSeek {
		//存在深度思考切换的时候需要重置风控内容
		state.allRes = state.tmpRes
	}
	if source == common.GptTypeMap[common.GptTypeDeepSeekR1DistillQwen32B] ||
		source == common.GptTypeMap[common.GptTypeDeepSeekReasoner] ||
		source == common.GptTypeMap[common.GptTypeDeepSeekV3] {
		s.log.WithContext(ctx).Infof("deepseek 模型需要替换biz +1000000, 原biz:%v, 修改后biz:%v",
			req.Biz, req.Biz+1000000)
		req.Biz += 1000000
	}
}

// 执行内容合规检查
func (s *SseUseCase) performContentCheck(
	ctx context.Context,
	req *request.LlmCommonRequest,
	state *sseProcessState,
	d dto.Data,
	extError **common.ExtError) int64 {

	legalRes, _ := s.legalBiz.LegalCheck(ctx, &legal.CheckReq{
		Biz:   req.Biz,
		Mode:  legal.Output,
		Text:  state.allRes,
		Sn:    req.Sn,
		TalId: req.TalId,
	})

	end := time.Now().UnixMilli()

	if legalRes != nil && legalRes.RiskLabelText != "" {
		s.log.WithContext(ctx).Warnf("sse check find sensitive words:%+v", legalRes.RiskWords)
		d.Sensitive = legalRes.RiskWords
		*extError = common.New(common.ECode120014, errOutputViolation)
	}
	if d.IsEnd && state.allRes == "" {
		s.log.WithContext(ctx).Infof("sse_gpt_res_null")
		*extError = common.New(common.ECode150000, errEmptyLlmResponse)
	}

	return end
}

// 等待处理准备就绪或超时
func (s *SseUseCase) waitForReadiness(
	ctx context.Context,
	canReturn bool,
	err error,
	outCh chan dto.Data) (<-chan dto.Data, error) {

	for {
		select {
		case <-ctx.Done(): // 监听超时返回
			return nil, errors.New("sse timeout")
		default:
			if !canReturn {
				time.Sleep(10 * time.Millisecond)
				continue
			}
			if err != nil {
				return nil, err
			}
			return outCh, nil
		}
	}
}

// SSECheck 检查SSE流式内容并进行内容合规检查
func (s *SseUseCase) SSECheck(ctx context.Context, ch <-chan dto.Data, req *request.LlmCommonRequest, source string, outputNoCheck bool) (event <-chan dto.Data, err error) {
	tmpId := ctx.Value(common.TmpId)
	var (
		tEnd   int64
		tStart int64
	)

	// 防止panic导致服务崩溃
	defer func() {
		if err := recover(); err != nil {
			s.log.WithContext(ctx).Errorf("SSECheck.Panic, debugInfo: %s, err: %+v", string(debug.Stack()), err)
		}
	}()

	// 创建输出通道
	outCh := make(chan dto.Data)

	// 错误处理相关
	var extError *common.ExtError
	defaultErrorData := dto.Data{
		Id:         "",
		Object:     "",
		Created:    0,
		SentenceId: 0,
		IsEnd:      true,
		Result:     "",
		Model:      "",
		Usage: dto.Usage{
			PromptTokens:     0,
			CompletionTokens: 0,
			TotalTokens:      0,
		},
	}

	// 百度模型特殊处理
	canReturn := !strings.Contains(source, "baidu")

	// 启动goroutine处理流式数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				s.log.WithContext(ctx).Errorf("debugInfo:%v \n ,SSECheck.Panic,err(%+v)", string(debug.Stack()), err)
			}
		}()

		// 初始化处理状态
		state := &sseProcessState{
			firstResponse:  true,
			messageCount:   0,
			tmpRes:         "",
			lastRes:        "",
			allRes:         "",
			lastIsDeepSeek: true,
		}
		// 处理流数据
		for d := range ch {
			// 处理首次响应
			if state.firstResponse {
				state.firstResponse = false
				startTime := ctx.Value(common.StartTime)
				if startTime != nil {
					cost := time.Since(startTime.(time.Time))
					s.log.WithContext(ctx).Infof("sse_first_response_cost,source:%s,TmpId:%v,cost:%v",
						source, tmpId, cost.Milliseconds())
				}

				if !canReturn {
					err = s.catchError(ctx, d)
					canReturn = true
				}
			}

			// 检查响应错误
			if s.processErrorInResponse(ctx, d, defaultErrorData, outCh, &extError) {
				break
			}

			// 增加消息计数并处理文本
			state.messageCount++
			havePunct := s.processText(source, d, state)

			// 当需要发送检查或是最后一条消息时
			if havePunct || d.IsEnd || state.lastIsDeepSeek != d.IsDeepSeek {
				// 记录日志
				startTime := ctx.Value(common.StartTime)
				if startTime != nil {
					cost := time.Since(startTime.(time.Time))
					s.log.WithContext(ctx).Infof("sse_every_llm_response_cost,source:%s,TmpId:%v,cost:%v",
						source, tmpId, cost.Milliseconds())
				}

				// 处理消息内容
				if d.Result != "" {
					tStart = time.Now().UnixMilli()
					state.allRes = state.allRes + state.tmpRes
					s.log.WithContext(ctx).Infof("sse check send %s", state.allRes)

					// 处理特殊模型情况
					s.handleSpecialModels(ctx, req, source, state, d)

					// 内容检查
					if !outputNoCheck {
						tEnd = s.performContentCheck(ctx, req, state, d, &extError)
					}
				}

				// 如果有错误,发送错误响应
				if extError != nil && extError.Code > 0 {
					s.sendErrorResponse(defaultErrorData, extError, state, d, outCh)
					break
				}

				// 发送正常响应
				s.sendNormalResponse(d, state, source, outCh)

				// 记录指标
				if state.messageCount == 1 {
					startTime := ctx.Value(common.StartTime)
					if startTime != nil {
						cost := time.Since(startTime.(time.Time))
						s.log.WithContext(ctx).Infof("sse_first_response_withshumei_cost,source:%s,TmpId:%v,cost:%v",
							source, tmpId, cost.Milliseconds())
					}
				}
				s.log.WithContext(ctx).Infof("sse_every_response_withshumei_interval,source:%s,TmpId:%v,interval:%v",
					source, tmpId, tEnd-tStart)

				// 重置状态
				state.tmpRes = ""
				state.lastIsDeepSeek = d.IsDeepSeek

				// 如果是结束消息,退出循环
				if d.IsEnd {
					break
				}
			}
		}
		close(outCh) // 最后关闭通道
	}()

	// 使用waitForReadiness辅助函数等待处理就绪或超时
	return s.waitForReadiness(ctx, canReturn, err, outCh)
}

// GetRequestHeaders 获取所有请求头
func (s *SseUseCase) GetRequestHeaders(ctx context.Context) map[string][]string {
	// 从context中获取请求头
	headersVal := ctx.Value(common.RequestHeaders)
	if headersVal == nil {
		return nil
	}

	// 类型断言转换为map[string][]string
	headers, ok := headersVal.(map[string][]string)
	if !ok {
		s.log.WithContext(ctx).Warnf("无法获取请求头信息，类型断言失败")
		return nil
	}

	return headers
}

// GetRequestHeader 获取指定的请求头值
func (s *SseUseCase) GetRequestHeader(ctx context.Context, headerName string) string {
	headers := s.GetRequestHeaders(ctx)
	if headers == nil {
		return ""
	}

	values, exists := headers[headerName]
	if !exists || len(values) == 0 {
		return ""
	}

	return values[0]
}

// GetRequestHeaderValues 获取指定请求头的所有值
func (s *SseUseCase) GetRequestHeaderValues(ctx context.Context, headerName string) []string {
	headers := s.GetRequestHeaders(ctx)
	if headers == nil {
		return nil
	}

	values, exists := headers[headerName]
	if !exists {
		return nil
	}

	return values
}

func (s *SseUseCase) catchError(c context.Context, data dto.Data) error {
	if data.Model == "baidu" && data.ErrorCode == 18 {
		return errors.Errorf("baidu data invalid,msg:%s", data.ErrorMsg)
	}
	return nil
}

func (s *SseUseCase) jsonLog(tmpId string, useNum int, isLast bool) string {
	type l struct {
		TemplateId string `json:"template_id"`
		UseModel   int    `json:"use_model"`
		IsLast     bool   `json:"is_last"`
	}
	j := &l{
		TemplateId: tmpId,
		UseModel:   useNum,
		IsLast:     isLast,
	}
	bjson, _ := json.Marshal(j)
	return string(bjson)

}

func splitAtFirstCharacter(s, char string) []string {
	var result []string
	runes := []rune(s)
	charRune := []rune(char)

	if len(charRune) != 1 {
		// If the specified character is not a single rune, return the whole string as a single element array
		return []string{s}
	}

	for idx, v := range runes {
		if v == charRune[0] {
			result = append(result, string(runes[:idx+1]))
			result = append(result, string(runes[idx+1:]))
			return result
		}
	}

	// If no character is found, return the whole string as a single element array
	return []string{s}
}
