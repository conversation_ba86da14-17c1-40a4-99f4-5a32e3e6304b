package biz

import (
	"context"
	"llm_service/internal/biz/llm/openAICommon"
	"llm_service/internal/common"
	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
)

type MultiModalClient struct {
	confD *conf.Data
	log   *log.Helper
}

func NewMultiModalClient(confD *conf.Data, logger log.Logger) *MultiModalClient {
	return &MultiModalClient{
		confD: confD,
		log:   log.NewHelper(logger),
	}
}

func (s *MultiModalClient) SseCommonChat(ctx context.Context, biz request.LlmBiz, chat dto.SSECommonChat, gptType int) (eventCh <-chan dto.Data, err error) {
	if chat.ApiKey == "" {
		return nil, common.NewErr(common.ECode110050, errors.New("请检查账号配置"))
	}
	s.log.WithContext(ctx).Infof("used SseCommonChat biz:%v chat_param: %v", biz, chat)
	options := []openAICommon.ClientOption{openAICommon.WithModel(chat.Model)}
	if chat.Temperature != 0 {
		options = append(options, openAICommon.WithTemperature(chat.Temperature))
	}

	options = append(options, openAICommon.WithStreamOptionsIncludeUsage(true))
	options = append(options, openAICommon.WithStream(true))
	if chat.EnableSearch {
		options = append(options, openAICommon.WithEnableSearch(true))
	}

	client := openAICommon.NewOpenAIClient(chat.URL, chat.ApiKey, common.GptTypeMap[gptType], time.Second*300, s.log, options...)

	// 判断是否使用多模态历史记录
	var history interface{}
	if chat.MultiModalHistory != nil {
		// 如果有多模态历史记录，优先使用
		s.log.WithContext(ctx).Infof("using multi_modal_history for model: %s", chat.Model)
		history = chat.MultiModalHistory
	} else {
		// 否则使用普通历史记录
		history = chat.History
	}

	// 调用 Send 方法，传递适当的历史记录
	ch, err := client.Send(ctx, chat.Prompt, chat.MultiMessage, common.GptTypeMap[gptType], history)
	if err != nil {
		return nil, err
	}
	return ch, nil
}
