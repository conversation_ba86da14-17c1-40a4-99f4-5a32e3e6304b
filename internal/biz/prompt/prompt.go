package prompt

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"llm_service/internal/data"
	"llm_service/internal/data/mysql_model"
)

type Prompt struct {
	repo *data.ChatDao
	log  *log.Helper
}

func NewPrompt(repo *data.ChatDao, logger log.Logger) *Prompt {
	return &Prompt{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

func (p *Prompt) GetPrompt(ctx context.Context, appId, tempId string, args []interface{}) (tempContent []mysql_model.TemplateContent, err error) {
	tempContent, err = p.GetTemp(ctx, appId, tempId)
	if err != nil {
		return
	}

	cnt := 0
	for i, content := range tempContent {
		//判断temp字符串中占位符的数量是否与args中的数量一致
		if countOfPlaceHolder(content.Msg) != len(args) {
			err = errors.New("args not match")
			p.log.WithContext(ctx).Warnf("第%v个大模型配置中,temp字符串中占位符的数量与args中的数量不一致", i+1)
			cnt++
			continue
		}

		tempContent[i].Msg = fmt.Sprintf(content.Msg, args...)
	}
	if cnt == len(tempContent) {
		err = errors.New("args not match")
	}
	return
}

func countOfPlaceHolder(temp string) (count int) {
	for i, v := range temp {
		if v == '%' && i+1 < len(temp) && temp[i+1] == 's' {
			count++
		}
	}
	return
}

func (p *Prompt) GetTemp(ctx context.Context, appId, tempId string) (tempContent []mysql_model.TemplateContent, err error) {
	template, err := p.repo.GetTemplate(ctx, appId, tempId)
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(template.Content), &tempContent)
	return
}
