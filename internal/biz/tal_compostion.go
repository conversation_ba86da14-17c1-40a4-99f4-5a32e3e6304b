package biz

import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-contrib/sse"
	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"io/ioutil"
	"llm_service/internal/biz/ailab/util"
	"llm_service/internal/biz/llm/tal_en_compostion_http"
	"llm_service/internal/common"
	"llm_service/internal/dto/response"
	"net/http"
	"time"

	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
)

type SSETalComposition struct {
	confD *conf.Data
	log   *log.Helper
}

func NewSSETalComposition(confD *conf.Data, logger log.Logger) *SSETalComposition {
	return &SSETalComposition{
		confD: confD,
		log:   log.NewHelper(logger),
	}
}

func (s *SSETalComposition) SseCommonChat(ctx context.Context, biz request.LlmBiz, chat dto.SSECommonChat) (eventCh <-chan dto.Data, err error) {
	s.log.WithContext(ctx).Infof("used sse_tal biz:%v chat_param: %v", biz, chat)
	if chat.ApiKey == "" {
		return nil, common.NewErr(common.ECode110050, errors.New("请检查账号配置"))
	}
	s.log.WithContext(ctx).Infof("used sse_tal biz:%v chat_param: %v", biz, chat)
	// 配置
	bodyParam := map[string]interface{}{}
	modConfig := s.confD.Llm.Tal.EnComposition
	if chat.Version == fmt.Sprintf("%v", common.GptTypeTalChComposition) {
		modConfig = s.confD.Llm.Tal.ChComposition
	}
	history := chat.History
	message := chat.Message
	prompt := chat.Prompt
	if history == nil {
		history = make([]request.Message, 0)
	}
	if prompt != "" {
		history = append(history, request.Message{
			Role:    request.UserRole,
			Content: prompt,
		})
	}
	if message != "" {
		if prompt != "" {
			history = append(history, []request.Message{
				{
					Role:    request.AssistantRole,
					Content: "好的,我会按照模板进行输出",
				},
			}...)
		}
		history = append(history, request.Message{
			Role:    request.UserRole,
			Content: message,
		})
	}

	bodyParam["is_stream"] = true
	bodyParam["messages"] = history
	body, err := jsoniter.Marshal(bodyParam)
	if err != nil {
		s.log.WithContext(ctx).Errorf("jsoniter.Marshal err :%v", err)
		return nil, err
	}
	eventChan := make(chan dto.Data)
	s.log.WithContext(ctx).Infof("tal_compostion:url:%v,body:%v", modConfig.Url, string(body))
	resp, err := util.TalRequest(modConfig.Url, body, modConfig.Key, modConfig.Secret)
	if err != nil {
		return nil, err
	}
	go s.RequestWithSSE(ctx, resp, eventChan, func(event *sse.Event, err error) (string, error) {
		if cast.ToString(event.Data) == common.EofText {
			return "", nil
		}
		return cast.ToString(event.Data), nil
	})
	return eventChan, nil
}

func (s *SSETalComposition) CommonChat(ctx context.Context, biz request.LlmBiz, chat dto.SSECommonChat) (resp *response.LlmCommonResp, err error) {
	s.log.WithContext(ctx).Infof("used sse_tal biz:%v chat_param: %v", biz, chat)
	if chat.ApiKey == "" {
		return nil, common.NewErr(common.ECode110050, errors.New("请检查账号配置"))
	}
	s.log.WithContext(ctx).Infof("used sse_tal biz:%v chat_param: %v", biz, chat)
	// 配置
	bodyParam := map[string]interface{}{}
	modConfig := s.confD.Llm.Tal.EnComposition
	if chat.Version == fmt.Sprintf("%v", common.GptTypeTalChComposition) {
		modConfig = s.confD.Llm.Tal.ChComposition
	}
	history := chat.History
	message := chat.Message
	prompt := chat.Prompt
	if history == nil {
		history = make([]request.Message, 0)
	}
	if prompt != "" {
		history = append(history, request.Message{
			Role:    request.UserRole,
			Content: prompt,
		})
	}
	if message != "" {
		if prompt != "" {
			history = append(history, []request.Message{
				{
					Role:    request.AssistantRole,
					Content: "好的,我会按照模板进行输出",
				},
			}...)
		}
		history = append(history, request.Message{
			Role:    request.UserRole,
			Content: message,
		})
	}

	bodyParam["is_stream"] = false
	bodyParam["messages"] = history
	body, err := jsoniter.Marshal(bodyParam)
	if err != nil {
		s.log.WithContext(ctx).Errorf("jsoniter.Marshal err :%v", err)
		return nil, err
	}
	s.log.WithContext(ctx).Infof("tal_compostion:url:%v,body:%v", modConfig.Url, string(body))
	res, err := util.TalRequest(modConfig.Url, body, modConfig.Key, modConfig.Secret)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		s.log.WithContext(ctx).Errorf("tal_compostion:url:%v,body:%v find err:%v", modConfig.Url, string(body), err)
		return nil, err
	}

	respBody, err := ioutil.ReadAll(res.Body)
	if err != nil {
		s.log.WithContext(ctx).Errorf("tal_compostion:url:%v, Error reading response body: %v\n", modConfig.Url, err)
		return nil, err
	}
	var d tal_en_compostion_http.Response
	err = json.Unmarshal(respBody, &d)
	if err != nil {
		s.log.WithContext(ctx).Errorf("tal : data json.Unmarshal error %v, data: %s", err, string(respBody))
		return nil, err
	}
	resp = &response.LlmCommonResp{
		TraceID: ctx.Value(common.TraceId).(string),
		ID:      d.RequestId,
		Result:  d.Data.Result,
		Model:   common.GptTypeMap[common.GptTypeTalChComposition],
		Source:  d.Data.Mod,
	}
	return resp, nil
}

func (s *SSETalComposition) RequestWithSSE(ctx context.Context, resp *http.Response, eventCh chan dto.Data, callback func(event *sse.Event, err error) (string, error)) {
	defer func() {
		close(eventCh)
		fmt.Println("done")
		_ = resp.Body.Close()
	}()

	tStart := time.Now().UnixMilli()
	rd := bufio.NewScanner(resp.Body)
	for rd.Scan() {
		row := rd.Text()
		startTime := ctx.Value(common.StartTime)
		tmpId := ctx.Value(common.TmpId)
		cost := time.Since(startTime.(time.Time))
		tEnd := time.Now().UnixMilli()
		s.log.WithContext(ctx).Infof("输出大模型msg:%v tmpid:%v,cost:%v,interval:%v", row, tmpId, cost.Milliseconds(), tEnd-tStart)
		tStart = time.Now().UnixMilli()
		var d tal_en_compostion_http.Response
		err := json.Unmarshal([]byte(row), &d)
		if err != nil {
			// 存在异常，忽略当前异常，直到输出完成
			s.log.WithContext(ctx).Warnf("tal SSE: data json.Unmarshal error %v, realData: %s", err, row)
			continue
		}
		if d.Code != common.TalSuccess {
			s.log.WithContext(ctx).Errorf("tal SSE: 九章作文模型状态异常 error, code:%v ,row: %v", d.Code, row)
			eventCh <- dto.Data{IsEnd: true, Result: ""}
			break
		}
		if d.Data.IsEnd == 3 {
			eventCh <- dto.Data{IsEnd: true, Result: ""}
			break
		}
		result := dto.Data{
			Id:     d.RequestId,
			Model:  d.Data.Mod,
			Result: d.Data.Result,
			Usage: dto.Usage{
				PromptTokens:     d.Data.PromptTokens,
				CompletionTokens: d.Data.CompletionTokens,
				TotalTokens:      d.Data.TotalTokens,
			},
		}
		eventCh <- result
		if result.IsEnd {
			break
		}
	}
}
