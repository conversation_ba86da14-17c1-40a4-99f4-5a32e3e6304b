package biz

import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-contrib/sse"
	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"llm_service/internal/biz/ailab/util"
	"llm_service/internal/biz/llm/tal_en_dialogue_http"
	"llm_service/internal/common"
	"net/http"
	"time"

	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
)

type SSETalEnDialogue struct {
	confD *conf.Data
	log   *log.Helper
}

func NewSSETalEnDialogue(confD *conf.Data, logger log.Logger) *SSETalEnDialogue {
	return &SSETalEnDialogue{
		confD: confD,
		log:   log.NewHelper(logger),
	}
}

func (s *SSETalEnDialogue) SseCommonChat(ctx context.Context, biz request.LlmBiz, chat dto.SSECommonChat) (eventCh <-chan dto.Data, err error) {
	s.log.WithContext(ctx).Infof("used sse_tal biz:%v chat_param: %v", biz, chat)
	if chat.ApiKey == "" {
		return nil, common.NewErr(common.ECode110050, errors.New("请检查账号配置"))
	}
	s.log.WithContext(ctx).Infof("used sse_tal biz:%v chat_param: %v", biz, chat)
	// 配置
	bodyParam := map[string]interface{}{}
	modConfig := s.confD.Llm.Tal.EnDialogue
	history := chat.History
	prompt := chat.Prompt
	plist := make([]request.Message, 0)
	if history == nil {
		history = make([]request.Message, 0)
	}
	if prompt != "" {
		plist = append(plist, request.Message{
			Role:    request.UserRole,
			Content: prompt,
		})
	}
	//没有历史消息就忽略message和历史消息 有历史消息就拼接历史消息再拼接message
	if len(chat.History) > 0 {
		plist = append(plist, history...)
		//如果历史消息最后一个角色是user就删除最后一个元素
		if len(plist) > 0 && plist[len(plist)-1].Role == request.UserRole {
			plist = plist[:len(plist)-1]
		}
		// 遍历数组，删除连续的第二个user角色
		for i := 1; i < len(plist); i++ {
			if plist[i].Role == request.UserRole && plist[i-1].Role == request.UserRole {
				// 删除第i个元素
				plist = append(plist[:i], plist[i+1:]...)
				i-- // 调整索引以避免跳过下一个元素
			}
		}
		plist = append(plist, request.Message{
			Role:    request.UserRole,
			Content: chat.Message,
		})
		history = plist
	} else {
		history = plist
	}
	bodyParam["stream"] = true
	bodyParam["messages"] = history
	body, err := jsoniter.Marshal(bodyParam)
	if err != nil {
		s.log.WithContext(ctx).Errorf("jsoniter.Marshal err :%v", err)
		return nil, err
	}
	eventChan := make(chan dto.Data)
	s.log.WithContext(ctx).Infof("tal_en_dialogue:url:%v,body:%v", modConfig.Url, string(body))
	resp, err := util.TalRequest(modConfig.Url, body, modConfig.Key, modConfig.Secret)
	if err != nil {
		return nil, err
	}
	go s.RequestWithSSE(ctx, resp, eventChan, func(event *sse.Event, err error) (string, error) {
		if cast.ToString(event.Data) == common.EofText {
			return "", nil
		}
		return cast.ToString(event.Data), nil
	})
	return eventChan, nil
}

func (s *SSETalEnDialogue) RequestWithSSE(ctx context.Context, resp *http.Response, eventCh chan dto.Data, callback func(event *sse.Event, err error) (string, error)) {
	defer func() {
		close(eventCh)
		fmt.Println("done")
		_ = resp.Body.Close()
	}()

	tStart := time.Now().UnixMilli()
	rd := bufio.NewScanner(resp.Body)
	for rd.Scan() {
		row := rd.Text()
		startTime := ctx.Value(common.StartTime)
		tmpId := ctx.Value(common.TmpId)
		cost := time.Since(startTime.(time.Time))
		tEnd := time.Now().UnixMilli()
		s.log.WithContext(ctx).Infof("输出大模型msg:%v tmpid:%v,cost:%v,interval:%v", row, tmpId, cost.Milliseconds(), tEnd-tStart)
		tStart = time.Now().UnixMilli()
		var d tal_en_dialogue_http.Response
		err := json.Unmarshal([]byte(row), &d)
		if err != nil {
			// 存在异常，忽略当前异常，直到输出完成
			s.log.WithContext(ctx).Warnf("tal SSE: data json.Unmarshal error %v, realData: %s", err, row)
			continue
		}
		if d.Code != common.TalSuccess {
			s.log.WithContext(ctx).Errorf("tal SSE: 九章英语对话模型状态异常 error, code:%v ,row: %v", d.Code, row)
			eventCh <- dto.Data{IsEnd: true, Result: ""}
			break
		}
		if d.Data.IsEnd == 1 {
			eventCh <- dto.Data{IsEnd: true, Result: ""}
			break
		}

		result := dto.Data{
			Id:     d.RequestID,
			Model:  d.Data.Mod,
			Result: d.Data.Result,
			Usage: dto.Usage{
				PromptTokens:     d.Data.PromptTokens,
				CompletionTokens: d.Data.CompletionTokens,
				TotalTokens:      d.Data.TotalTokens,
			},
		}
		eventCh <- result
		if result.IsEnd {
			break
		}
	}
}
