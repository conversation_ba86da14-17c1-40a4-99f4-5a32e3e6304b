package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"llm_service/internal/biz/llm/tal_llama3_70b_model_square"
	"llm_service/internal/common"
	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
	"time"
)

type SSELlama370bModelSquare struct {
	confD *conf.Data
	log   *log.Helper
}

func NewSSELlama370bModelSquare(confD *conf.Data, logger log.Logger) *SSELlama370bModelSquare {
	return &SSELlama370bModelSquare{
		confD: confD,
		log:   log.NewHelper(logger),
	}
}

func (s *SSELlama370bModelSquare) SseCommonChat(ctx context.Context, biz request.LlmBiz, chat dto.SSECommonChat) (eventCh <-chan dto.Data, err error) {
	if chat.ApiKey == "" {
		return nil, common.NewErr(common.ECode110050, errors.New("请检查账号配置"))
	}
	s.log.WithContext(ctx).Infof("used sse_tal_llama3_70b_model_square biz:%v chat_param: %v", biz, chat)
	options := []tal_llama3_70b_model_square.ClientOption{tal_llama3_70b_model_square.WithModel(chat.Model)}
	if chat.Temperature != 0 {
		options = append(options, tal_llama3_70b_model_square.WithTemperature(chat.Temperature))
	} else if s.confD.Llm.Tongyi.Qwen72B.Temperature != 0 {
		options = append(options, tal_llama3_70b_model_square.WithTemperature(s.confD.Llm.Tongyi.Qwen72B.Temperature))
	}
	if chat.TopP != 0 {
		options = append(options, tal_llama3_70b_model_square.WithTopP(chat.TopP))
	}
	options = append(options, tal_llama3_70b_model_square.WithModel(tal_llama3_70b_model_square.Llama370b))
	options = append(options, tal_llama3_70b_model_square.WithStream(true))

	client := tal_llama3_70b_model_square.NewClient(chat.URL, time.Second*300, chat.ApiKey, s.log, options...)
	ch, err := client.Send(ctx, tal_llama3_70b_model_square.AiServiceServer, chat.Prompt, chat.Message, chat.History)
	if err != nil {
		return nil, err
	}
	return ch, nil
}
