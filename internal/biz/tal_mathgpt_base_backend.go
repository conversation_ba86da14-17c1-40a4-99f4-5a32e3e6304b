package biz

import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-contrib/sse"
	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"llm_service/internal/biz/ailab/util"
	"llm_service/internal/biz/llm/tal_en_compostion_http"
	"llm_service/internal/common"
	"net/http"
	"time"

	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
)

type SSETalMathGPTBaseBackend struct {
	confD *conf.Data
	log   *log.Helper
}

func NewSSETalMathGPTBaseBackend(confD *conf.Data, logger log.Logger) *SSETalMathGPTBaseBackend {
	return &SSETalMathGPTBaseBackend{
		confD: confD,
		log:   log.NewHelper(logger),
	}
}

func (s *SSETalMathGPTBaseBackend) SseCommonChat(ctx context.Context, biz request.LlmBiz, chat dto.SSECommonChat) (eventCh <-chan dto.Data, err error) {
	s.log.WithContext(ctx).Infof("used sse_mathgpt_base_backend biz:%v chat_param: %v", biz, chat)
	if chat.ApiKey == "" {
		return nil, common.NewErr(common.ECode110050, errors.New("请检查账号配置"))
	}
	s.log.WithContext(ctx).Infof("used sse_mathgpt_base_backend biz:%v chat_param: %v", biz, chat)
	// 配置
	bodyParam := map[string]interface{}{}
	modConfig := s.confD.Llm.Tal.MathGptBaseBackend
	history := chat.History
	message := chat.Message
	prompt := chat.Prompt
	pList := make([]request.Message, 0)
	if history == nil {
		history = make([]request.Message, 0)
	}
	if prompt != "" {
		history = append(history, request.Message{
			Role:    request.UserRole,
			Content: prompt,
		})
		history = append(pList, history...)
	} else if message != "" {
		history = append(history, request.Message{
			Role:    request.UserRole,
			Content: message,
		})
	}
	bodyParam["messages"] = history
	bodyParam["n"] = 1
	body, err := jsoniter.Marshal(bodyParam)
	if err != nil {
		s.log.WithContext(ctx).Errorf("jsoniter.Marshal err :%v", err)
		return nil, err
	}
	s.log.WithContext(ctx).Infof("url:%v,req body is %v", modConfig.Url, string(body))
	eventChan := make(chan dto.Data)
	resp, err := util.TalRequest(modConfig.Url, body, modConfig.Key, modConfig.Secret)
	if err != nil {
		return nil, err
	}
	go s.RequestWithSSE(ctx, resp, eventChan, func(event *sse.Event, err error) (string, error) {
		if cast.ToString(event.Data) == common.EofText {
			return "", nil
		}
		return cast.ToString(event.Data), nil
	})
	return eventChan, nil
}

func (s *SSETalMathGPTBaseBackend) RequestWithSSE(ctx context.Context, resp *http.Response, eventCh chan dto.Data, callback func(event *sse.Event, err error) (string, error)) {
	defer func() {
		close(eventCh)
		fmt.Println("done")
		_ = resp.Body.Close()
	}()

	tStart := time.Now().UnixMilli()
	rd := bufio.NewScanner(resp.Body)
	for rd.Scan() {
		row := rd.Text()
		startTime := ctx.Value(common.StartTime)
		tmpId := ctx.Value(common.TmpId)
		cost := time.Since(startTime.(time.Time))
		tEnd := time.Now().UnixMilli()
		s.log.WithContext(ctx).Infof("输出大模型msg:%v tmpid:%v,cost:%v,interval:%v", row, tmpId, cost.Milliseconds(), tEnd-tStart)
		tStart = time.Now().UnixMilli()
		var d tal_en_compostion_http.Response
		err := json.Unmarshal([]byte(row), &d)
		if err != nil {
			// 存在异常，忽略当前异常，直到输出完成
			s.log.WithContext(ctx).Warnf("tal SSE: data json.Unmarshal error %v, realData: %s", err, row)
			continue
		}
		if d.Data.IsEnd == 1 {
			eventCh <- dto.Data{IsEnd: true, Result: ""}
			break
		}
		result := dto.Data{
			Id:     d.RequestId,
			Model:  d.Data.Mod,
			Result: d.Data.Result,
			Usage: dto.Usage{
				PromptTokens:     d.Data.PromptTokens,
				CompletionTokens: d.Data.CompletionTokens,
				TotalTokens:      d.Data.TotalTokens,
			},
		}
		eventCh <- result
		if result.IsEnd {
			break
		}
	}
}
