package biz

import (
	"context"
	"llm_service/internal/biz/llm/tal_middleground"
	"llm_service/internal/common"
	"runtime/debug"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/tidwall/gjson"
	"llm_service/internal/conf"
	"llm_service/internal/dto"
	"llm_service/internal/dto/request"
)

type SSETalMiddleGround struct {
	confD *conf.Data
	log   *log.Helper
	talWs *tal_middleground.TalMiddleGroundUseCase
}

func NewSSETalMiddleGround(confD *conf.Data, talWs *tal_middleground.TalMiddleGroundUseCase, logger log.Logger) *SSETalMiddleGround {
	return &SSETalMiddleGround{
		confD: confD,
		talWs: talWs,
		log:   log.NewHelper(logger),
	}
}

func (s *SSETalMiddleGround) SseCommonChat(ctx context.Context, biz request.LlmBiz, chat dto.SSECommonChat) (eventCh <-chan dto.Data, err error) {
	s.log.WithContext(ctx).Infof("used sse_tal_middleground biz:%v chat_param: %v", biz, chat)
	eventChan := make(chan dto.Data)
	GptChan := make(chan string)
	_, err = s.talWs.SendMessageByTal(ctx, chat.Message, chat.SessionId, GptChan, chat.History)
	if err != nil {
		s.log.WithContext(ctx).Warn("SendMessageByTal err", err)
		return nil, err
	}
	go func() {
		defer func() {
			close(eventChan)
			if err := recover(); err != nil {
				s.log.WithContext(ctx).Errorf("go routine panic错误：%v\n %s", err, debug.Stack())
				return
			}
		}()
		for {
			var text string
			select {
			case text = <-GptChan:
				if text == "" {
					s.log.WithContext(ctx).Warnf("tal_sse_channel_err err:%s", "返回内容为空")
				}
			case <-time.After(time.Minute * 3):
				s.log.WithContext(ctx).Warnf("tal_sse_channel_timeout")
				return
			}

			var result dto.Data
			if text == common.EofText {
				result.IsEnd = true
				eventChan <- result
				break
			} else {
				res := gjson.Parse(text)
				msg := res.Get("result").String()
				id := res.Get("id").String()
				model := res.Get("model").String()
				if msg == "" {
					continue
				}
				result.Result = msg
				result.Id = id
				result.Model = model
			}
			eventChan <- result
		}
	}()
	return eventChan, nil
}
