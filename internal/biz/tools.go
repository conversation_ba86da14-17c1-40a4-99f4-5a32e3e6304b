package biz

import (
	"github.com/gin-contrib/sse"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"io"
	"llm_service/internal/common"
	"llm_service/internal/dto"
	"llm_service/internal/pkg/kgin"
)

func Render(c *kgin.Context, log *log.Helper, err error, errMsg string) {
	data := dto.Data{
		ErrorCode: common.GetErrorCode(err),
		ErrorMsg:  common.GetErrorMessage(err),
		TraceId:   c.Request.Context().Value(common.TraceId).(string),
		IsEnd:     true,
	}
	log.WithContext(c.Request.Context()).Errorf(errMsg+" %v", data)
	c.Stream(func(w io.Writer) bool {
		_ = sse.Encode(w, sse.Event{
			Event: "llm",
			Id:    uuid.New().String(),
			Data:  data,
		})
		return false
	})
	return
}
