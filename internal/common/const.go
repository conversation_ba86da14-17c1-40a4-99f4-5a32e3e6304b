package common

import (
	"sync"

	"github.com/pkg/errors"
)

const (
	AppNameApi = "llm_service"
)

const (
	TraceId        = "trace-id"
	XTraceId       = "X-Genie-TraceId"
	StartTime      = "startTime"
	TmpId          = "tmpId"
	RequestTime    = "requestTime"
	EofText        = "[DONE]"
	LLMEventErr    = "event:error"
	WSConnTime     = "connectTime"
	AppId          = "X-Genie-AppId"
	Language       = "X-Genie-Language"
	ContextErr     = "context"
	DeviceId       = "X-Genie-Deviceid"
	RequestHeaders = "request-headers"

	// HTTP 标准Header常量定义
	HeaderContentType      = "Content-Type"
	HeaderAuthorization    = "Authorization"
	HeaderUserAgent        = "User-Agent"
	HeaderAccept           = "Accept"
	HeaderAcceptEncoding   = "Accept-Encoding"
	HeaderAcceptLanguage   = "Accept-Language"
	HeaderCacheControl     = "Cache-Control"
	HeaderConnection       = "Connection"
	HeaderOrigin           = "Origin"
	HeaderReferer          = "Referer"
	HeaderHost             = "Host"
	HeaderContentLength    = "Content-Length"
	HeaderCookie           = "Cookie"
	HeaderXForwardedFor    = "X-Forwarded-For"
	HeaderXRealIP          = "X-Real-IP"
	HeaderXRequestID       = "X-Request-ID"
	HeaderXRequestedWith   = "X-Requested-With"
	HeaderIfModifiedSince  = "If-Modified-Since"
	HeaderIfNoneMatch      = "If-None-Match"
	HeaderTransferEncoding = "Transfer-Encoding"

	// 自定义Header常量定义 (已有的和扩展的)
	HeaderXGenieAppId      = "X-Genie-AppId"    // 等同于AppId常量
	HeaderXGenieLanguage   = "X-Genie-Language" // 等同于Language常量
	HeaderXGenieTraceId    = "X-Genie-TraceId"  // 等同于XTraceId常量
	HeaderXGenieDeviceId   = "X-Genie-Deviceid" // 等同于DeviceId常量
	HeaderXGenieUserId     = "X-Genie-UserId"
	HeaderXGenieToken      = "X-Genie-Token"
	HeaderXGenieTimestamp  = "X-Genie-Timestamp"
	HeaderXGenieSignature  = "X-Genie-Signature"
	HeaderXGenieVersion    = "X-Genie-Version"
	HeaderXGenieClientType = "X-Genie-ClientType"
	HeaderXGenieRequestId  = "X-Genie-RequestId"
)

const DefaultLayout = "2006-01-02 15:04:05"

const (
	ECodeSuccessMsg = "success"
	ECodeSuccess    = 0

	GptTypeOpenAi35                 = 1
	GptTypeAzure35                  = 2
	GptTypeOpenAi4                  = 3
	GptTypeBaiduV1                  = 4
	GptTypeTal                      = 5 //中台作文批改
	GptTypeBaiduV2                  = 6
	GptTypeBaiduPrivate             = 7
	GptTypeAzure4                   = 8
	GptTypeBaiduPrivate2            = 9
	GptTypeGoogleGemini             = 10
	GptTypeMathGpt                  = 11
	GptTypeBaiChuan                 = 12
	GptTypeErnieBot                 = 13
	GptTypeTalEnComposition         = 14
	GptTypeTongyiQwenMax            = 15
	GptTypeTal70B                   = 16
	GptTypeTal7B                    = 17
	GptTypeGPT4Turbo                = 18
	GptTypeQwen72B                  = 19
	GptTypeGPTExclusive             = 20
	GptTypeLlama3_70b               = 21
	GptTypeTongyiQwenPlus           = 22
	GptTypeMathGPTBaseBackend       = 23
	GptTypeTalChComposition         = 24
	GptTypeTalEnglishDialogue       = 25
	GptTypeLlama370bModelSquare     = 26
	GptTypeDouBaoLite32k            = 27
	GptTypeDouBaoPro32k             = 28
	GptTypeQwenVlMax                = 29
	GptTypeGlm4vflash               = 30
	GptTypeDoubaoProVision32k       = 31
	GptTypeDeepSeekReasoner         = 32
	GptTypeDeepSeekR1DistillQwen32B = 33
	GptTypeClaude37Sonnet           = 34
	GptTypeDeepSeekV3               = 35

	ECode110000 = 110000 //参数错误
	ECode110010 = 110010 //Token校验失败
	ECode110020 = 110020 //未进行appId配置
	ECode110030 = 110030 //不存在的业务类型
	ECode110040 = 110040 //未配置业务策略
	ECode110050 = 110050 //策略配置错误
	ECode120000 = 120000 //数据异常
	ECode120010 = 120010 //业务数据转换失败
	ECode120011 = 120011 //业务数据校验错误
	ECode120012 = 120012 //输入触发敏感信息
	ECode120013 = 120013 //输入敏感词验证失败
	ECode120014 = 120014 //输出敏感词验证失败
	ECode120015 = 120015 //触发百川敏感词，内容被过滤
	ECode120020 = 120020 //缓存数据错误
	ECode120030 = 120030 //数据库数据错误
	ECode120040 = 120040 //第三方数据错误
	ECode130000 = 130000 //服务异常
	ECode140000 = 140000 //未知错误
	ECode150000 = 150000 //gpt服务异常
	ECode160000 = 160000 //请求内容过长
	Ecode170000 = 170000 //分享会话不存在或已删除
	ECode180000 = 180000 //风控禁止访问
	ECode190000 = 190000 //模板ID不存在
)

var (
	GptTypeMap = map[int]string{
		GptTypeOpenAi35:                 "openai35",
		GptTypeAzure35:                  "azure35",
		GptTypeOpenAi4:                  "openai4",
		GptTypeBaiduV1:                  "baiduV1",
		GptTypeTal:                      "tal",
		GptTypeBaiduV2:                  "baiduV2",
		GptTypeBaiduPrivate:             "baiduPrivate",
		GptTypeAzure4:                   "azure4",
		GptTypeBaiduPrivate2:            "baiduPrivate2",
		GptTypeGoogleGemini:             "googleGemini",
		GptTypeMathGpt:                  "mathGpt",
		GptTypeBaiChuan:                 "baichuan2Turbo",
		GptTypeErnieBot:                 "ernieBot",
		GptTypeTalEnComposition:         "talEnCompostion",
		GptTypeTalChComposition:         "talChCompostion",
		GptTypeTongyiQwenMax:            "qwenMax",
		GptTypeTal70B:                   "talMulti70b",
		GptTypeTal7B:                    "tal7b",
		GptTypeGPT4Turbo:                "gPT4Turbo",
		GptTypeQwen72B:                  "qwen72b",
		GptTypeTongyiQwenPlus:           "qwenPlus",
		GptTypeGPTExclusive:             "gPT4Exclusive",
		GptTypeMathGPTBaseBackend:       "mathGPTBaseBackend",
		GptTypeLlama3_70b:               "llama3_70b",
		GptTypeTalEnglishDialogue:       "englishDialogue",
		GptTypeLlama370bModelSquare:     "llama3_70b_model_square",
		GptTypeDouBaoLite32k:            "DouBaoLite32k",
		GptTypeDouBaoPro32k:             "DouBaoPro32k",
		GptTypeQwenVlMax:                "qwenVlMax",
		GptTypeGlm4vflash:               "glm4vflash",
		GptTypeDoubaoProVision32k:       "doubaoProVision32k",
		GptTypeDeepSeekReasoner:         "deepseekReasoner",
		GptTypeDeepSeekR1DistillQwen32B: "deepseek-r1-distill-qwen-32b",
		GptTypeClaude37Sonnet:           "Claude37Sonnet",
		GptTypeDeepSeekV3:               "deepSeekV3",
	}
	// ErrMap Error Map 只定义输出信息,  模块自定义输出错误信息在 application 层自己写,只需要定义上面的 code 能够跟踪即可
	ErrMap = map[int]string{
		ECode110000: "参数错误",
		ECode110010: "验签错误",
		ECode110020: "未进行appId配置",
		ECode110030: "不存在的业务类型",
		ECode110040: "未配置业务策略",
		ECode110050: "策略配置错误",
		ECode120000: "数据错误",
		ECode120010: "校验异常",
		ECode130000: "服务异常",
		ECode140000: "请求错误",
		ECode150000: "chat服务异常",
		ECode160000: "请求内容过长",
		Ecode170000: "当前分享不存在或已被删除",
		ECode180000: "风控禁止访问",
		ECode190000: "模板ID不存在",
	}

	GptNotStreamTypeMap = map[int]struct {
	}{
		GptTypeTalChComposition: struct {
		}{},
	}
)

const TalSuccess = 20000

type WsProcStageType int

const (
	// DuiWsProcStageStart 标识当前处于开始发送流程
	DuiWsProcStageStart WsProcStageType = 1
	// DuiWsProcStageFeed 标识当前处于分析处理流程
	DuiWsProcStageFeed WsProcStageType = 2
	// DuiWsProcStageStop 标识当前处于截停或停止流程
	DuiWsProcStageStop WsProcStageType = 3
)

var WSChannelMap = sync.Map{}

type ExtError struct {
	Code    int
	Message string
	Err     error
}

func NewErr(code int, err error) *ExtError {
	return &ExtError{Code: code, Err: err, Message: err.Error()}
}
func Warp(msg string, err error) *ExtError {
	return &ExtError{Code: ECode140000, Err: errors.Wrap(err, msg)}
}

func NewError(code int, msg, info string) *ExtError {
	return &ExtError{Code: code, Message: msg, Err: errors.New(info)}
}

func New(code int, msg string) *ExtError {
	return &ExtError{Code: code, Message: msg}
}
func (e *ExtError) Error() string {
	if e != nil && e.Err != nil {
		return e.Err.Error()
	}
	return ECodeSuccessMsg
}

func (e *ExtError) GetMessage() string {
	if ErrMap[e.Code] != "" {
		return ErrMap[e.Code]
	}
	if e.Message != "" {
		return e.Message
	}
	return ErrMap[ECode140000]
}

func (e *ExtError) GetCode() int {
	return e.Code
}
