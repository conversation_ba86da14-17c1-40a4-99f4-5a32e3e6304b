package common

import (
	"time"

	"github.com/nacos-group/nacos-sdk-go/common/constant"
	"gopkg.in/natefinch/lumberjack.v2"
)

type Env string

var CurrentEnv Env

// 新增变量类型，需要扩展下面所有方法，填充具体参数
const (
	LocalEnv   Env = "local"   // 本地环境（使用本地配置）
	DevelopEnv Env = "develop" // 开发环境
	TestEnv    Env = "test"    // 测试环境
	GrayEnv    Env = "gray"    // 灰度环境
	OnlineEnv  Env = "online"  // 生产环境
)

type DiscoveryConfig struct {
	Address     string `json:"address"`
	Port        uint64 `json:"port"`
	NamespaceId string `json:"namespaceId"`
	ServerGroup string `json:"serverGroup"`
}

// ======== TCM 配置中心 =========

// TcmServerConfig TCM server config
func (e Env) TcmServerConfig() constant.ServerConfig {
	switch e {
	case DevelopEnv:
		return constant.ServerConfig{
			IpAddr:      "http://cloud-test.tal.com",
			Port:        80,
			ContextPath: "/tcm-api/nacos",
		}
	case TestEnv:
		return constant.ServerConfig{
			IpAddr:      "http://cloud-test.tal.com",
			Port:        80,
			ContextPath: "/tcm-api/nacos",
		}
	case GrayEnv:
		return constant.ServerConfig{
			IpAddr:      "http://cloud.tal.com",
			Port:        80,
			ContextPath: "/tcm-api/nacos",
		}
	case OnlineEnv:
		return constant.ServerConfig{
			IpAddr:      "http://cloud.tal.com",
			Port:        80,
			ContextPath: "/tcm-api/nacos",
		}
	default:
		panic("不支持的环境变量")
	}
}

// tcmClientOption TCM ClientOptions
func (e Env) tcmClientOption() []constant.ClientOption {
	switch e {
	case DevelopEnv:
		return []constant.ClientOption{
			constant.WithNamespaceId("895d57ea-c2d0-4280-a895-e77a9068e75a"),
			constant.WithTimeoutMs(8000),
			constant.WithAccessKey("VEFMX1ROU184OTVkNTdlYS1jMmQwLTQyODAtYTg5NS1lNzdhOTA2OGU3NWE="),
			constant.WithSecretKey("VEFMX1ROU19f5YyX5LqsX+mYv+mHjOS6kV9kZXY4OTVkNTdlYS1jMmQwLTQyODAtYTg5NS1lNzdhOTA2OGU3NWFfNzI="),
			constant.WithLogLevel("info"),
		}
	case TestEnv:
		return []constant.ClientOption{
			constant.WithNamespaceId("47e7d4a9-8a7b-4b1f-b3af-31b702020f27"),
			constant.WithTimeoutMs(8000),
			constant.WithAccessKey("VEFMX1ROU180N2U3ZDRhOS04YTdiLTRiMWYtYjNhZi0zMWI3MDIwMjBmMjc="),
			constant.WithSecretKey("VEFMX1ROU19f5YyX5LqsX+mYv+mHjOS6kV90ZXN0NDdlN2Q0YTktOGE3Yi00YjFmLWIzYWYtMzFiNzAyMDIwZjI3XzQ3"),
			constant.WithLogLevel("info"),
		}
	case GrayEnv:
		return []constant.ClientOption{
			constant.WithNamespaceId("368d36a8-8490-4b8b-99d6-839a45bba99f"),
			constant.WithTimeoutMs(5000),
			constant.WithAccessKey("VEFMX1ROU18zNjhkMzZhOC04NDkwLTRiOGItOTlkNi04MzlhNDViYmE5OWY="),
			constant.WithSecretKey("VEFMX1ROU19f5YyX5LqsX+mYv+mHjOS6kV9iZXRhMzY4ZDM2YTgtODQ5MC00YjhiLTk5ZDYtODM5YTQ1YmJhOTlmXzUw"),
			constant.WithLogLevel("error"),
		}
	case OnlineEnv:
		return []constant.ClientOption{
			constant.WithNamespaceId("4480c3f0-aa42-4ba5-93e1-7a0f99addfa5"),
			constant.WithTimeoutMs(5000),
			constant.WithAccessKey("VEFMX1ROU180NDgwYzNmMC1hYTQyLTRiYTUtOTNlMS03YTBmOTlhZGRmYTU="),
			constant.WithSecretKey("VEFMX1ROU19f5YyX5LqsX+mYv+mHjOS6kV9wcm9kNDQ4MGMzZjAtYWE0Mi00YmE1LTkzZTEtN2EwZjk5YWRkZmE1XzUw"),
			constant.WithLogLevel("error"),
		}
	default:
		panic("不支持的环境变量")
	}
}

func (e Env) TcmClientConfig() *constant.ClientConfig {
	opts := []constant.ClientOption{
		constant.WithNotLoadCacheAtStart(true),
		constant.WithLogDir("/tmp/nacos/log"),
		constant.WithCacheDir("/tmp/nacos/cache"),
		constant.WithLogRollingConfig(&lumberjack.Logger{
			MaxSize:   1,
			MaxAge:    5,
			LocalTime: true,
			Compress:  true,
		}),
		constant.WithLogSampling(time.Second*10, 100, 50),
	}
	opts = append(opts, e.tcmClientOption()...)
	return constant.NewClientConfig(opts...)
}

// Discovery 服务发现配置
func (e Env) Discovery() DiscoveryConfig {
	switch e {
	case LocalEnv:
		return DiscoveryConfig{
			Address:     "**************",
			Port:        8848,
			NamespaceId: "genie-service-local",
			ServerGroup: "genie-server",
		}
	case DevelopEnv:
		return DiscoveryConfig{
			Address:     "**************",
			Port:        8848,
			NamespaceId: "genie-service-dev",
			ServerGroup: "genie-server",
		}
	case TestEnv:
		return DiscoveryConfig{
			Address:     "**************",
			Port:        8848,
			NamespaceId: "genie-service-test",
			ServerGroup: "genie-server",
		}
	case GrayEnv:
		return DiscoveryConfig{
			Address:     "*************",
			Port:        8848,
			NamespaceId: "genie-service-gray",
			ServerGroup: "genie-server",
		}
	case OnlineEnv:
		return DiscoveryConfig{
			Address:     "*************",
			Port:        8848,
			NamespaceId: "genie-service",
			ServerGroup: "genie-server",
		}
	default:
		panic("不支持的环境变量")
	}
}

// EmbeddingTableSuffix 对应服务的向量数据库后缀
func (e Env) EmbeddingTableSuffix() string {
	switch e {
	case LocalEnv:
		fallthrough
	case DevelopEnv:
		fallthrough
	case TestEnv:
		return ""
	case GrayEnv:
		return "_gray"
	case OnlineEnv:
		return "_prod"
	default:
		panic("不支持的环境变量")
	}
}
