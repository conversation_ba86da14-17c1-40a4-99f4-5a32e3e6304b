package common

import (
	"errors"
)

const defaultError = 100000
const OvertimeError = 504
const OvertimeErrorMsg = "请求超时"

func GetErrorCode(err error) int {
	var defineError *ExtError
	if errors.As(err, &defineError) {
		return err.(*ExtError).GetCode()
	}
	return defaultError
}

func GetErrorMessage(err error) string {
	var defineError *ExtError
	if errors.As(err, &defineError) {
		return err.(*ExtError).GetMessage()
	}
	return err.Error()
}
