// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v3.20.3
// source: internal/conf/conf.proto

package conf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Bootstrap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Server   *Server   `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	Data     *Data     `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Log      *Log      `protobuf:"bytes,3,opt,name=log,proto3" json:"log,omitempty"`
	Business *Business `protobuf:"bytes,4,opt,name=business,proto3" json:"business,omitempty"`
}

func (x *Bootstrap) Reset() {
	*x = Bootstrap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bootstrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bootstrap) ProtoMessage() {}

func (x *Bootstrap) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bootstrap.ProtoReflect.Descriptor instead.
func (*Bootstrap) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Bootstrap) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Bootstrap) GetData() *Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Bootstrap) GetLog() *Log {
	if x != nil {
		return x.Log
	}
	return nil
}

func (x *Bootstrap) GetBusiness() *Business {
	if x != nil {
		return x.Business
	}
	return nil
}

type Business struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Strategy      map[string]string         `protobuf:"bytes,1,rep,name=strategy,proto3" json:"strategy,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ForbiddenRule map[string]*ForbiddenItem `protobuf:"bytes,2,rep,name=forbiddenRule,proto3" json:"forbiddenRule,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	YachRobot     *YachRobot                `protobuf:"bytes,3,opt,name=yachRobot,proto3" json:"yachRobot,omitempty"`
}

func (x *Business) Reset() {
	*x = Business{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Business) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Business) ProtoMessage() {}

func (x *Business) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Business.ProtoReflect.Descriptor instead.
func (*Business) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1}
}

func (x *Business) GetStrategy() map[string]string {
	if x != nil {
		return x.Strategy
	}
	return nil
}

func (x *Business) GetForbiddenRule() map[string]*ForbiddenItem {
	if x != nil {
		return x.ForbiddenRule
	}
	return nil
}

func (x *Business) GetYachRobot() *YachRobot {
	if x != nil {
		return x.YachRobot
	}
	return nil
}

type ForbiddenItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Duration int64 `protobuf:"varint,1,opt,name=duration,proto3" json:"duration,omitempty"`
	Times    int64 `protobuf:"varint,2,opt,name=times,proto3" json:"times,omitempty"`
	Freeze   int64 `protobuf:"varint,3,opt,name=freeze,proto3" json:"freeze,omitempty"`
}

func (x *ForbiddenItem) Reset() {
	*x = ForbiddenItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForbiddenItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForbiddenItem) ProtoMessage() {}

func (x *ForbiddenItem) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForbiddenItem.ProtoReflect.Descriptor instead.
func (*ForbiddenItem) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2}
}

func (x *ForbiddenItem) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *ForbiddenItem) GetTimes() int64 {
	if x != nil {
		return x.Times
	}
	return 0
}

func (x *ForbiddenItem) GetFreeze() int64 {
	if x != nil {
		return x.Freeze
	}
	return 0
}

type YachRobot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable      bool   `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	AccessToken string `protobuf:"bytes,2,opt,name=accessToken,proto3" json:"accessToken,omitempty"`
	Secret      string `protobuf:"bytes,3,opt,name=secret,proto3" json:"secret,omitempty"`
}

func (x *YachRobot) Reset() {
	*x = YachRobot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YachRobot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YachRobot) ProtoMessage() {}

func (x *YachRobot) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YachRobot.ProtoReflect.Descriptor instead.
func (*YachRobot) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{3}
}

func (x *YachRobot) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *YachRobot) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *YachRobot) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

type Server struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Http *Server_HTTP `protobuf:"bytes,1,opt,name=http,proto3" json:"http,omitempty"`
	Grpc *Server_GRPC `protobuf:"bytes,2,opt,name=grpc,proto3" json:"grpc,omitempty"`
	Env  string       `protobuf:"bytes,3,opt,name=env,proto3" json:"env,omitempty"`
}

func (x *Server) Reset() {
	*x = Server{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{4}
}

func (x *Server) GetHttp() *Server_HTTP {
	if x != nil {
		return x.Http
	}
	return nil
}

func (x *Server) GetGrpc() *Server_GRPC {
	if x != nil {
		return x.Grpc
	}
	return nil
}

func (x *Server) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

type Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database   *DatabaseConfig   `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	Redis      *RedisConfig      `protobuf:"bytes,2,opt,name=redis,proto3" json:"redis,omitempty"`
	ThirdParty *ThirdPartyConfig `protobuf:"bytes,3,opt,name=thirdParty,proto3" json:"thirdParty,omitempty"`
	Llm        *LlmConfig        `protobuf:"bytes,4,opt,name=llm,proto3" json:"llm,omitempty"`
}

func (x *Data) Reset() {
	*x = Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data) ProtoMessage() {}

func (x *Data) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data.ProtoReflect.Descriptor instead.
func (*Data) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{5}
}

func (x *Data) GetDatabase() *DatabaseConfig {
	if x != nil {
		return x.Database
	}
	return nil
}

func (x *Data) GetRedis() *RedisConfig {
	if x != nil {
		return x.Redis
	}
	return nil
}

func (x *Data) GetThirdParty() *ThirdPartyConfig {
	if x != nil {
		return x.ThirdParty
	}
	return nil
}

func (x *Data) GetLlm() *LlmConfig {
	if x != nil {
		return x.Llm
	}
	return nil
}

// 数据库配置相关的消息
type DatabaseConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Driver string `protobuf:"bytes,1,opt,name=driver,proto3" json:"driver,omitempty"` // 数据库驱动程序，如 MySQL、PostgreSQL
	Source string `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"` // 数据源，如连接字符串或数据库路径
}

func (x *DatabaseConfig) Reset() {
	*x = DatabaseConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DatabaseConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DatabaseConfig) ProtoMessage() {}

func (x *DatabaseConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DatabaseConfig.ProtoReflect.Descriptor instead.
func (*DatabaseConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{6}
}

func (x *DatabaseConfig) GetDriver() string {
	if x != nil {
		return x.Driver
	}
	return ""
}

func (x *DatabaseConfig) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

type RedisConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host     string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Port     int32  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Password string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	Db       int32  `protobuf:"varint,4,opt,name=db,proto3" json:"db,omitempty"`
}

func (x *RedisConfig) Reset() {
	*x = RedisConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedisConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedisConfig) ProtoMessage() {}

func (x *RedisConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedisConfig.ProtoReflect.Descriptor instead.
func (*RedisConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{7}
}

func (x *RedisConfig) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *RedisConfig) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *RedisConfig) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *RedisConfig) GetDb() int32 {
	if x != nil {
		return x.Db
	}
	return 0
}

// ThirdParty配置相关的消息
type ThirdPartyConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LegalCheck *ThirdPartyConfig_LegalCheckConfig `protobuf:"bytes,1,opt,name=legalCheck,proto3" json:"legalCheck,omitempty"`
}

func (x *ThirdPartyConfig) Reset() {
	*x = ThirdPartyConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThirdPartyConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThirdPartyConfig) ProtoMessage() {}

func (x *ThirdPartyConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThirdPartyConfig.ProtoReflect.Descriptor instead.
func (*ThirdPartyConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{8}
}

func (x *ThirdPartyConfig) GetLegalCheck() *ThirdPartyConfig_LegalCheckConfig {
	if x != nil {
		return x.LegalCheck
	}
	return nil
}

type LlmConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilterPunctuation string            `protobuf:"bytes,1,opt,name=filterPunctuation,proto3" json:"filterPunctuation,omitempty"`
	Azure             *AzureConfig      `protobuf:"bytes,2,opt,name=azure,proto3" json:"azure,omitempty"`
	Tal               *TalConfig        `protobuf:"bytes,3,opt,name=tal,proto3" json:"tal,omitempty"`
	Baidu             *BaiduConfig      `protobuf:"bytes,4,opt,name=baidu,proto3" json:"baidu,omitempty"`
	Baichuan          *BaiChuanConfig   `protobuf:"bytes,5,opt,name=baichuan,proto3" json:"baichuan,omitempty"`
	Ernie             *ErnieConfig      `protobuf:"bytes,6,opt,name=ernie,proto3" json:"ernie,omitempty"`
	Tongyi            *TongyiConfig     `protobuf:"bytes,7,opt,name=tongyi,proto3" json:"tongyi,omitempty"`
	Doubao            *DouBaoConfig     `protobuf:"bytes,8,opt,name=doubao,proto3" json:"doubao,omitempty"`
	DeepSeek          *DeepSeekConfig   `protobuf:"bytes,9,opt,name=deepSeek,proto3" json:"deepSeek,omitempty"`
	MultiModal        *MultiModalConfig `protobuf:"bytes,10,opt,name=multiModal,proto3" json:"multiModal,omitempty"`
	Anthropic         *AnthropicConfig  `protobuf:"bytes,11,opt,name=anthropic,proto3" json:"anthropic,omitempty"`
}

func (x *LlmConfig) Reset() {
	*x = LlmConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LlmConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LlmConfig) ProtoMessage() {}

func (x *LlmConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LlmConfig.ProtoReflect.Descriptor instead.
func (*LlmConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{9}
}

func (x *LlmConfig) GetFilterPunctuation() string {
	if x != nil {
		return x.FilterPunctuation
	}
	return ""
}

func (x *LlmConfig) GetAzure() *AzureConfig {
	if x != nil {
		return x.Azure
	}
	return nil
}

func (x *LlmConfig) GetTal() *TalConfig {
	if x != nil {
		return x.Tal
	}
	return nil
}

func (x *LlmConfig) GetBaidu() *BaiduConfig {
	if x != nil {
		return x.Baidu
	}
	return nil
}

func (x *LlmConfig) GetBaichuan() *BaiChuanConfig {
	if x != nil {
		return x.Baichuan
	}
	return nil
}

func (x *LlmConfig) GetErnie() *ErnieConfig {
	if x != nil {
		return x.Ernie
	}
	return nil
}

func (x *LlmConfig) GetTongyi() *TongyiConfig {
	if x != nil {
		return x.Tongyi
	}
	return nil
}

func (x *LlmConfig) GetDoubao() *DouBaoConfig {
	if x != nil {
		return x.Doubao
	}
	return nil
}

func (x *LlmConfig) GetDeepSeek() *DeepSeekConfig {
	if x != nil {
		return x.DeepSeek
	}
	return nil
}

func (x *LlmConfig) GetMultiModal() *MultiModalConfig {
	if x != nil {
		return x.MultiModal
	}
	return nil
}

func (x *LlmConfig) GetAnthropic() *AnthropicConfig {
	if x != nil {
		return x.Anthropic
	}
	return nil
}

type MultiModalConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QwenVlMax          *CommonLLMConfig `protobuf:"bytes,1,opt,name=qwenVlMax,proto3" json:"qwenVlMax,omitempty"`
	Glm4Vflash         *CommonLLMConfig `protobuf:"bytes,2,opt,name=glm4vflash,proto3" json:"glm4vflash,omitempty"`
	DoubaoProVision32K *CommonLLMConfig `protobuf:"bytes,3,opt,name=doubaoProVision32k,proto3" json:"doubaoProVision32k,omitempty"`
}

func (x *MultiModalConfig) Reset() {
	*x = MultiModalConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiModalConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiModalConfig) ProtoMessage() {}

func (x *MultiModalConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiModalConfig.ProtoReflect.Descriptor instead.
func (*MultiModalConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{10}
}

func (x *MultiModalConfig) GetQwenVlMax() *CommonLLMConfig {
	if x != nil {
		return x.QwenVlMax
	}
	return nil
}

func (x *MultiModalConfig) GetGlm4Vflash() *CommonLLMConfig {
	if x != nil {
		return x.Glm4Vflash
	}
	return nil
}

func (x *MultiModalConfig) GetDoubaoProVision32K() *CommonLLMConfig {
	if x != nil {
		return x.DoubaoProVision32K
	}
	return nil
}

// Azure配置相关的消息
type AzureConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Temperature          float32  `protobuf:"fixed32,1,opt,name=temperature,proto3" json:"temperature,omitempty"`
	TopP                 float32  `protobuf:"fixed32,2,opt,name=topP,proto3" json:"topP,omitempty"`
	ApiKeys              []string `protobuf:"bytes,3,rep,name=apiKeys,proto3" json:"apiKeys,omitempty"`
	Gpt4ApiKeys          []string `protobuf:"bytes,4,rep,name=gpt4ApiKeys,proto3" json:"gpt4ApiKeys,omitempty"`
	Gpt4TurboApiKeys     []string `protobuf:"bytes,5,rep,name=gpt4TurboApiKeys,proto3" json:"gpt4TurboApiKeys,omitempty"`
	Gpt4ExclusiveApiKeys []string `protobuf:"bytes,6,rep,name=gpt4ExclusiveApiKeys,proto3" json:"gpt4ExclusiveApiKeys,omitempty"`
	Url                  string   `protobuf:"bytes,7,opt,name=url,proto3" json:"url,omitempty"`
	Gpt4Url              string   `protobuf:"bytes,8,opt,name=gpt4Url,proto3" json:"gpt4Url,omitempty"`
	Gpt4TurboUrl         string   `protobuf:"bytes,9,opt,name=gpt4TurboUrl,proto3" json:"gpt4TurboUrl,omitempty"`
	Gpt4ExclusiveUrl     string   `protobuf:"bytes,10,opt,name=gpt4ExclusiveUrl,proto3" json:"gpt4ExclusiveUrl,omitempty"`
	InitSwitch           int32    `protobuf:"varint,11,opt,name=initSwitch,proto3" json:"initSwitch,omitempty"`
	N                    float32  `protobuf:"fixed32,12,opt,name=n,proto3" json:"n,omitempty"`
}

func (x *AzureConfig) Reset() {
	*x = AzureConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AzureConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AzureConfig) ProtoMessage() {}

func (x *AzureConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AzureConfig.ProtoReflect.Descriptor instead.
func (*AzureConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{11}
}

func (x *AzureConfig) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *AzureConfig) GetTopP() float32 {
	if x != nil {
		return x.TopP
	}
	return 0
}

func (x *AzureConfig) GetApiKeys() []string {
	if x != nil {
		return x.ApiKeys
	}
	return nil
}

func (x *AzureConfig) GetGpt4ApiKeys() []string {
	if x != nil {
		return x.Gpt4ApiKeys
	}
	return nil
}

func (x *AzureConfig) GetGpt4TurboApiKeys() []string {
	if x != nil {
		return x.Gpt4TurboApiKeys
	}
	return nil
}

func (x *AzureConfig) GetGpt4ExclusiveApiKeys() []string {
	if x != nil {
		return x.Gpt4ExclusiveApiKeys
	}
	return nil
}

func (x *AzureConfig) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *AzureConfig) GetGpt4Url() string {
	if x != nil {
		return x.Gpt4Url
	}
	return ""
}

func (x *AzureConfig) GetGpt4TurboUrl() string {
	if x != nil {
		return x.Gpt4TurboUrl
	}
	return ""
}

func (x *AzureConfig) GetGpt4ExclusiveUrl() string {
	if x != nil {
		return x.Gpt4ExclusiveUrl
	}
	return ""
}

func (x *AzureConfig) GetInitSwitch() int32 {
	if x != nil {
		return x.InitSwitch
	}
	return 0
}

func (x *AzureConfig) GetN() float32 {
	if x != nil {
		return x.N
	}
	return 0
}

// Tal配置相关的消息
type TalConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tal70B                *TalConfig_TalLlmConfig `protobuf:"bytes,1,opt,name=tal70b,proto3" json:"tal70b,omitempty"`
	Tal7B                 *TalConfig_TalLlmConfig `protobuf:"bytes,2,opt,name=tal7b,proto3" json:"tal7b,omitempty"`
	Llama3_70B            *TalConfig_TalLlmConfig `protobuf:"bytes,3,opt,name=llama3_70b,json=llama370b,proto3" json:"llama3_70b,omitempty"`
	EnComposition         *TalConfig_TalLlmConfig `protobuf:"bytes,4,opt,name=en_composition,json=enComposition,proto3" json:"en_composition,omitempty"`
	ChComposition         *TalConfig_TalLlmConfig `protobuf:"bytes,5,opt,name=ch_composition,json=chComposition,proto3" json:"ch_composition,omitempty"`
	Zh                    *TalConfig_TalLlmConfig `protobuf:"bytes,6,opt,name=zh,proto3" json:"zh,omitempty"`
	En                    *TalConfig_TalLlmConfig `protobuf:"bytes,7,opt,name=en,proto3" json:"en,omitempty"`
	MathGptBaseBackend    *TalConfig_TalLlmConfig `protobuf:"bytes,8,opt,name=mathGptBaseBackend,proto3" json:"mathGptBaseBackend,omitempty"`
	EnDialogue            *TalConfig_TalLlmConfig `protobuf:"bytes,9,opt,name=en_dialogue,json=enDialogue,proto3" json:"en_dialogue,omitempty"`
	Llama3_70BModelSquare *CommonLLMConfig        `protobuf:"bytes,10,opt,name=llama3_70b_model_square,json=llama370bModelSquare,proto3" json:"llama3_70b_model_square,omitempty"`
}

func (x *TalConfig) Reset() {
	*x = TalConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TalConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TalConfig) ProtoMessage() {}

func (x *TalConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TalConfig.ProtoReflect.Descriptor instead.
func (*TalConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{12}
}

func (x *TalConfig) GetTal70B() *TalConfig_TalLlmConfig {
	if x != nil {
		return x.Tal70B
	}
	return nil
}

func (x *TalConfig) GetTal7B() *TalConfig_TalLlmConfig {
	if x != nil {
		return x.Tal7B
	}
	return nil
}

func (x *TalConfig) GetLlama3_70B() *TalConfig_TalLlmConfig {
	if x != nil {
		return x.Llama3_70B
	}
	return nil
}

func (x *TalConfig) GetEnComposition() *TalConfig_TalLlmConfig {
	if x != nil {
		return x.EnComposition
	}
	return nil
}

func (x *TalConfig) GetChComposition() *TalConfig_TalLlmConfig {
	if x != nil {
		return x.ChComposition
	}
	return nil
}

func (x *TalConfig) GetZh() *TalConfig_TalLlmConfig {
	if x != nil {
		return x.Zh
	}
	return nil
}

func (x *TalConfig) GetEn() *TalConfig_TalLlmConfig {
	if x != nil {
		return x.En
	}
	return nil
}

func (x *TalConfig) GetMathGptBaseBackend() *TalConfig_TalLlmConfig {
	if x != nil {
		return x.MathGptBaseBackend
	}
	return nil
}

func (x *TalConfig) GetEnDialogue() *TalConfig_TalLlmConfig {
	if x != nil {
		return x.EnDialogue
	}
	return nil
}

func (x *TalConfig) GetLlama3_70BModelSquare() *CommonLLMConfig {
	if x != nil {
		return x.Llama3_70BModelSquare
	}
	return nil
}

// Baidu配置相关的消息
type BaiduConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId     string `protobuf:"bytes,1,opt,name=clientId,proto3" json:"clientId,omitempty"`
	ClientSecret string `protobuf:"bytes,2,opt,name=clientSecret,proto3" json:"clientSecret,omitempty"`
	GrantType    string `protobuf:"bytes,3,opt,name=grantType,proto3" json:"grantType,omitempty"`
	Url          string `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	ChatUrl      string `protobuf:"bytes,5,opt,name=chatUrl,proto3" json:"chatUrl,omitempty"`
	Version      string `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *BaiduConfig) Reset() {
	*x = BaiduConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaiduConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaiduConfig) ProtoMessage() {}

func (x *BaiduConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaiduConfig.ProtoReflect.Descriptor instead.
func (*BaiduConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{13}
}

func (x *BaiduConfig) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *BaiduConfig) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

func (x *BaiduConfig) GetGrantType() string {
	if x != nil {
		return x.GrantType
	}
	return ""
}

func (x *BaiduConfig) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *BaiduConfig) GetChatUrl() string {
	if x != nil {
		return x.ChatUrl
	}
	return ""
}

func (x *BaiduConfig) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// BaiChuan配置相关的消息
type BaiChuanConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model             string  `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`
	Temperature       float32 `protobuf:"fixed32,2,opt,name=temperature,proto3" json:"temperature,omitempty"`
	WithSearchEnhance bool    `protobuf:"varint,3,opt,name=with_search_enhance,json=withSearchEnhance,proto3" json:"with_search_enhance,omitempty"`
	ApiKey            string  `protobuf:"bytes,4,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
	Url               string  `protobuf:"bytes,5,opt,name=url,proto3" json:"url,omitempty"`
	IsCheck           int32   `protobuf:"varint,6,opt,name=isCheck,proto3" json:"isCheck,omitempty"`
}

func (x *BaiChuanConfig) Reset() {
	*x = BaiChuanConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaiChuanConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaiChuanConfig) ProtoMessage() {}

func (x *BaiChuanConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaiChuanConfig.ProtoReflect.Descriptor instead.
func (*BaiChuanConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{14}
}

func (x *BaiChuanConfig) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *BaiChuanConfig) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *BaiChuanConfig) GetWithSearchEnhance() bool {
	if x != nil {
		return x.WithSearchEnhance
	}
	return false
}

func (x *BaiChuanConfig) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *BaiChuanConfig) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *BaiChuanConfig) GetIsCheck() int32 {
	if x != nil {
		return x.IsCheck
	}
	return 0
}

// Ernie配置相关的消息
type ErnieConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErnieBot   *ErnieConfig_ErnieBotConfig `protobuf:"bytes,1,opt,name=ernieBot,proto3" json:"ernieBot,omitempty"`
	ErnieBot8K *ErnieConfig_ErnieBotConfig `protobuf:"bytes,2,opt,name=ernieBot8k,proto3" json:"ernieBot8k,omitempty"`
}

func (x *ErnieConfig) Reset() {
	*x = ErnieConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErnieConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErnieConfig) ProtoMessage() {}

func (x *ErnieConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErnieConfig.ProtoReflect.Descriptor instead.
func (*ErnieConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{15}
}

func (x *ErnieConfig) GetErnieBot() *ErnieConfig_ErnieBotConfig {
	if x != nil {
		return x.ErnieBot
	}
	return nil
}

func (x *ErnieConfig) GetErnieBot8K() *ErnieConfig_ErnieBotConfig {
	if x != nil {
		return x.ErnieBot8K
	}
	return nil
}

// Tongyi配置相关的消息
type TongyiConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QwenMax  *CommonLLMConfig `protobuf:"bytes,1,opt,name=qwenMax,proto3" json:"qwenMax,omitempty"`
	Qwen72B  *CommonLLMConfig `protobuf:"bytes,2,opt,name=qwen72b,proto3" json:"qwen72b,omitempty"`
	QwenPlus *CommonLLMConfig `protobuf:"bytes,3,opt,name=qwenPlus,proto3" json:"qwenPlus,omitempty"`
}

func (x *TongyiConfig) Reset() {
	*x = TongyiConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TongyiConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TongyiConfig) ProtoMessage() {}

func (x *TongyiConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TongyiConfig.ProtoReflect.Descriptor instead.
func (*TongyiConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{16}
}

func (x *TongyiConfig) GetQwenMax() *CommonLLMConfig {
	if x != nil {
		return x.QwenMax
	}
	return nil
}

func (x *TongyiConfig) GetQwen72B() *CommonLLMConfig {
	if x != nil {
		return x.Qwen72B
	}
	return nil
}

func (x *TongyiConfig) GetQwenPlus() *CommonLLMConfig {
	if x != nil {
		return x.QwenPlus
	}
	return nil
}

// DouBao配置相关的消息
type DouBaoConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DouBaoLite32K *CommonLLMConfig `protobuf:"bytes,1,opt,name=douBaoLite32k,proto3" json:"douBaoLite32k,omitempty"`
	DouBaoPro32K  *CommonLLMConfig `protobuf:"bytes,2,opt,name=douBaoPro32k,proto3" json:"douBaoPro32k,omitempty"`
}

func (x *DouBaoConfig) Reset() {
	*x = DouBaoConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DouBaoConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DouBaoConfig) ProtoMessage() {}

func (x *DouBaoConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DouBaoConfig.ProtoReflect.Descriptor instead.
func (*DouBaoConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{17}
}

func (x *DouBaoConfig) GetDouBaoLite32K() *CommonLLMConfig {
	if x != nil {
		return x.DouBaoLite32K
	}
	return nil
}

func (x *DouBaoConfig) GetDouBaoPro32K() *CommonLLMConfig {
	if x != nil {
		return x.DouBaoPro32K
	}
	return nil
}

type AnthropicConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Claude37Sonnet *CommonLLMConfig `protobuf:"bytes,1,opt,name=claude37Sonnet,proto3" json:"claude37Sonnet,omitempty"`
}

func (x *AnthropicConfig) Reset() {
	*x = AnthropicConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnthropicConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnthropicConfig) ProtoMessage() {}

func (x *AnthropicConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnthropicConfig.ProtoReflect.Descriptor instead.
func (*AnthropicConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{18}
}

func (x *AnthropicConfig) GetClaude37Sonnet() *CommonLLMConfig {
	if x != nil {
		return x.Claude37Sonnet
	}
	return nil
}

// DouBao配置相关的消息
type DeepSeekConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeepSeekR1               *CommonLLMConfig `protobuf:"bytes,1,opt,name=deepSeekR1,proto3" json:"deepSeekR1,omitempty"`
	DeepSeekR1DistillQwen32B *CommonLLMConfig `protobuf:"bytes,2,opt,name=deepSeekR1DistillQwen32B,proto3" json:"deepSeekR1DistillQwen32B,omitempty"`
	DeepSeekV3               *CommonLLMConfig `protobuf:"bytes,3,opt,name=deepSeekV3,proto3" json:"deepSeekV3,omitempty"`
}

func (x *DeepSeekConfig) Reset() {
	*x = DeepSeekConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeepSeekConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeepSeekConfig) ProtoMessage() {}

func (x *DeepSeekConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeepSeekConfig.ProtoReflect.Descriptor instead.
func (*DeepSeekConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{19}
}

func (x *DeepSeekConfig) GetDeepSeekR1() *CommonLLMConfig {
	if x != nil {
		return x.DeepSeekR1
	}
	return nil
}

func (x *DeepSeekConfig) GetDeepSeekR1DistillQwen32B() *CommonLLMConfig {
	if x != nil {
		return x.DeepSeekR1DistillQwen32B
	}
	return nil
}

func (x *DeepSeekConfig) GetDeepSeekV3() *CommonLLMConfig {
	if x != nil {
		return x.DeepSeekV3
	}
	return nil
}

type CommonLLMConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model       string  `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`
	Temperature float32 `protobuf:"fixed32,2,opt,name=temperature,proto3" json:"temperature,omitempty"`
	ApiKey      string  `protobuf:"bytes,3,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
	Url         string  `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	IsCheck     int32   `protobuf:"varint,5,opt,name=isCheck,proto3" json:"isCheck,omitempty"`
}

func (x *CommonLLMConfig) Reset() {
	*x = CommonLLMConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonLLMConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonLLMConfig) ProtoMessage() {}

func (x *CommonLLMConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonLLMConfig.ProtoReflect.Descriptor instead.
func (*CommonLLMConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{20}
}

func (x *CommonLLMConfig) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *CommonLLMConfig) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *CommonLLMConfig) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *CommonLLMConfig) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *CommonLLMConfig) GetIsCheck() int32 {
	if x != nil {
		return x.IsCheck
	}
	return 0
}

type Log struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filename  string `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`
	MaxSize   int32  `protobuf:"varint,2,opt,name=max_size,json=maxSize,proto3" json:"max_size,omitempty"`
	MaxBackup int32  `protobuf:"varint,3,opt,name=max_backup,json=maxBackup,proto3" json:"max_backup,omitempty"`
	MaxAge    int32  `protobuf:"varint,4,opt,name=max_age,json=maxAge,proto3" json:"max_age,omitempty"`
	Compress  bool   `protobuf:"varint,5,opt,name=compress,proto3" json:"compress,omitempty"`
}

func (x *Log) Reset() {
	*x = Log{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Log) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Log) ProtoMessage() {}

func (x *Log) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Log.ProtoReflect.Descriptor instead.
func (*Log) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{21}
}

func (x *Log) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *Log) GetMaxSize() int32 {
	if x != nil {
		return x.MaxSize
	}
	return 0
}

func (x *Log) GetMaxBackup() int32 {
	if x != nil {
		return x.MaxBackup
	}
	return 0
}

func (x *Log) GetMaxAge() int32 {
	if x != nil {
		return x.MaxAge
	}
	return 0
}

func (x *Log) GetCompress() bool {
	if x != nil {
		return x.Compress
	}
	return false
}

type Server_HTTP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network string               `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr    string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout *durationpb.Duration `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *Server_HTTP) Reset() {
	*x = Server_HTTP{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_HTTP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_HTTP) ProtoMessage() {}

func (x *Server_HTTP) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_HTTP.ProtoReflect.Descriptor instead.
func (*Server_HTTP) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{4, 0}
}

func (x *Server_HTTP) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_HTTP) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_HTTP) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server_GRPC struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network string               `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr    string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout *durationpb.Duration `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *Server_GRPC) Reset() {
	*x = Server_GRPC{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_GRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_GRPC) ProtoMessage() {}

func (x *Server_GRPC) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_GRPC.ProtoReflect.Descriptor instead.
func (*Server_GRPC) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{4, 1}
}

func (x *Server_GRPC) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_GRPC) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_GRPC) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type ThirdPartyConfig_CensorBizConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId string `protobuf:"bytes,1,opt,name=serviceId,proto3" json:"serviceId,omitempty"`
	Input     string `protobuf:"bytes,2,opt,name=input,proto3" json:"input,omitempty"`
	Output    string `protobuf:"bytes,3,opt,name=output,proto3" json:"output,omitempty"`
}

func (x *ThirdPartyConfig_CensorBizConfig) Reset() {
	*x = ThirdPartyConfig_CensorBizConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThirdPartyConfig_CensorBizConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThirdPartyConfig_CensorBizConfig) ProtoMessage() {}

func (x *ThirdPartyConfig_CensorBizConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThirdPartyConfig_CensorBizConfig.ProtoReflect.Descriptor instead.
func (*ThirdPartyConfig_CensorBizConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ThirdPartyConfig_CensorBizConfig) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ThirdPartyConfig_CensorBizConfig) GetInput() string {
	if x != nil {
		return x.Input
	}
	return ""
}

func (x *ThirdPartyConfig_CensorBizConfig) GetOutput() string {
	if x != nil {
		return x.Output
	}
	return ""
}

type ThirdPartyConfig_LegalCheckConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host   string                                       `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	BizMap map[string]*ThirdPartyConfig_CensorBizConfig `protobuf:"bytes,2,rep,name=bizMap,proto3" json:"bizMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ThirdPartyConfig_LegalCheckConfig) Reset() {
	*x = ThirdPartyConfig_LegalCheckConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThirdPartyConfig_LegalCheckConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThirdPartyConfig_LegalCheckConfig) ProtoMessage() {}

func (x *ThirdPartyConfig_LegalCheckConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThirdPartyConfig_LegalCheckConfig.ProtoReflect.Descriptor instead.
func (*ThirdPartyConfig_LegalCheckConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{8, 1}
}

func (x *ThirdPartyConfig_LegalCheckConfig) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *ThirdPartyConfig_LegalCheckConfig) GetBizMap() map[string]*ThirdPartyConfig_CensorBizConfig {
	if x != nil {
		return x.BizMap
	}
	return nil
}

type TalConfig_TalLlmConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url     string            `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Key     string            `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Secret  string            `protobuf:"bytes,3,opt,name=secret,proto3" json:"secret,omitempty"`
	Mod     map[string]string `protobuf:"bytes,4,rep,name=mod,proto3" json:"mod,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	IsCheck int32             `protobuf:"varint,5,opt,name=isCheck,proto3" json:"isCheck,omitempty"`
}

func (x *TalConfig_TalLlmConfig) Reset() {
	*x = TalConfig_TalLlmConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TalConfig_TalLlmConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TalConfig_TalLlmConfig) ProtoMessage() {}

func (x *TalConfig_TalLlmConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TalConfig_TalLlmConfig.ProtoReflect.Descriptor instead.
func (*TalConfig_TalLlmConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{12, 0}
}

func (x *TalConfig_TalLlmConfig) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *TalConfig_TalLlmConfig) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *TalConfig_TalLlmConfig) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *TalConfig_TalLlmConfig) GetMod() map[string]string {
	if x != nil {
		return x.Mod
	}
	return nil
}

func (x *TalConfig_TalLlmConfig) GetIsCheck() int32 {
	if x != nil {
		return x.IsCheck
	}
	return 0
}

type ErnieConfig_ErnieBotConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model       string  `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`
	Temperature float32 `protobuf:"fixed32,2,opt,name=temperature,proto3" json:"temperature,omitempty"`
	ApiKey      string  `protobuf:"bytes,3,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
	Url         string  `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	IsCheck     int32   `protobuf:"varint,5,opt,name=isCheck,proto3" json:"isCheck,omitempty"`
}

func (x *ErnieConfig_ErnieBotConfig) Reset() {
	*x = ErnieConfig_ErnieBotConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErnieConfig_ErnieBotConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErnieConfig_ErnieBotConfig) ProtoMessage() {}

func (x *ErnieConfig_ErnieBotConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErnieConfig_ErnieBotConfig.ProtoReflect.Descriptor instead.
func (*ErnieConfig_ErnieBotConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{15, 0}
}

func (x *ErnieConfig_ErnieBotConfig) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *ErnieConfig_ErnieBotConfig) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *ErnieConfig_ErnieBotConfig) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *ErnieConfig_ErnieBotConfig) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ErnieConfig_ErnieBotConfig) GetIsCheck() int32 {
	if x != nil {
		return x.IsCheck
	}
	return 0
}

var File_internal_conf_conf_proto protoreflect.FileDescriptor

var file_internal_conf_conf_proto_rawDesc = []byte{
	0x0a, 0x18, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb2, 0x01, 0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x74, 0x73,
	0x74, 0x72, 0x61, 0x70, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x12, 0x24, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x03, 0x6c, 0x6f, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x4c, 0x6f, 0x67, 0x52, 0x03, 0x6c, 0x6f, 0x67, 0x12, 0x30, 0x0a, 0x08, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x22, 0xe8, 0x02, 0x0a, 0x08,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x3e, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6b, 0x72, 0x61,
	0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x2e, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x4d, 0x0a, 0x0d, 0x66, 0x6f, 0x72, 0x62,
	0x69, 0x64, 0x64, 0x65, 0x6e, 0x52, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x52,
	0x75, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x66, 0x6f, 0x72, 0x62, 0x69, 0x64,
	0x64, 0x65, 0x6e, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x33, 0x0a, 0x09, 0x79, 0x61, 0x63, 0x68, 0x52,
	0x6f, 0x62, 0x6f, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x72, 0x61,
	0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x59, 0x61, 0x63, 0x68, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x52, 0x09, 0x79, 0x61, 0x63, 0x68, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x1a, 0x3b, 0x0a, 0x0d,
	0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x5b, 0x0a, 0x12, 0x46, 0x6f, 0x72,
	0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x6f,
	0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x59, 0x0a, 0x0d, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64,
	0x64, 0x65, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x72, 0x65,
	0x65, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x66, 0x72, 0x65, 0x65, 0x7a,
	0x65, 0x22, 0x5d, 0x0a, 0x09, 0x59, 0x61, 0x63, 0x68, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x22, 0xca, 0x02, 0x0a, 0x06, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x04, 0x68,
	0x74, 0x74, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x48, 0x54,
	0x54, 0x50, 0x52, 0x04, 0x68, 0x74, 0x74, 0x70, 0x12, 0x2b, 0x0a, 0x04, 0x67, 0x72, 0x70, 0x63,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x52, 0x50, 0x43, 0x52,
	0x04, 0x67, 0x72, 0x70, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x76, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x1a, 0x69, 0x0a, 0x04, 0x48, 0x54, 0x54, 0x50, 0x12,
	0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x33, 0x0a,
	0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x1a, 0x69, 0x0a, 0x04, 0x47, 0x52, 0x50, 0x43, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x22, 0xd4, 0x01,
	0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x36, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61,
	0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12, 0x2d,
	0x0a, 0x05, 0x72, 0x65, 0x64, 0x69, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x64, 0x69, 0x73,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x72, 0x65, 0x64, 0x69, 0x73, 0x12, 0x3c, 0x0a,
	0x0a, 0x74, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54,
	0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x0a, 0x74, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x12, 0x27, 0x0a, 0x03, 0x6c,
	0x6c, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4c, 0x6c, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x03, 0x6c, 0x6c, 0x6d, 0x22, 0x40, 0x0a, 0x0e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x61, 0x0a, 0x0b, 0x52, 0x65, 0x64, 0x69, 0x73, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x64, 0x62, 0x22, 0xa5, 0x03, 0x0a, 0x10, 0x54, 0x68,
	0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4d,
	0x0a, 0x0a, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0a, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x1a, 0x5d, 0x0a,
	0x0f, 0x43, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x42, 0x69, 0x7a, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x6e, 0x70, 0x75, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x1a, 0xe2, 0x01, 0x0a,
	0x10, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x51, 0x0a, 0x06, 0x62, 0x69, 0x7a, 0x4d, 0x61, 0x70, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x42, 0x69, 0x7a, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x06, 0x62, 0x69, 0x7a, 0x4d, 0x61, 0x70, 0x1a, 0x67, 0x0a, 0x0b, 0x42, 0x69, 0x7a, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x42, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x42, 0x69, 0x7a,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xbc, 0x04, 0x0a, 0x09, 0x4c, 0x6c, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x2c, 0x0a, 0x11, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x50, 0x75, 0x6e, 0x63, 0x74, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x50, 0x75, 0x6e, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a,
	0x05, 0x61, 0x7a, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x7a, 0x75, 0x72, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x61, 0x7a, 0x75, 0x72, 0x65, 0x12, 0x27, 0x0a, 0x03,
	0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x03, 0x74, 0x61, 0x6c, 0x12, 0x2d, 0x0a, 0x05, 0x62, 0x61, 0x69, 0x64, 0x75, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x42, 0x61, 0x69, 0x64, 0x75, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x62,
	0x61, 0x69, 0x64, 0x75, 0x12, 0x36, 0x0a, 0x08, 0x62, 0x61, 0x69, 0x63, 0x68, 0x75, 0x61, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x42, 0x61, 0x69, 0x43, 0x68, 0x75, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x08, 0x62, 0x61, 0x69, 0x63, 0x68, 0x75, 0x61, 0x6e, 0x12, 0x2d, 0x0a, 0x05,
	0x65, 0x72, 0x6e, 0x69, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x6e, 0x69, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x65, 0x72, 0x6e, 0x69, 0x65, 0x12, 0x30, 0x0a, 0x06, 0x74,
	0x6f, 0x6e, 0x67, 0x79, 0x69, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x6f, 0x6e, 0x67, 0x79, 0x69, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x74, 0x6f, 0x6e, 0x67, 0x79, 0x69, 0x12, 0x30, 0x0a,
	0x06, 0x64, 0x6f, 0x75, 0x62, 0x61, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x6f, 0x75, 0x42, 0x61,
	0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x64, 0x6f, 0x75, 0x62, 0x61, 0x6f, 0x12,
	0x36, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x53, 0x65, 0x65, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44,
	0x65, 0x65, 0x70, 0x53, 0x65, 0x65, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x08, 0x64,
	0x65, 0x65, 0x70, 0x53, 0x65, 0x65, 0x6b, 0x12, 0x3c, 0x0a, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x4d, 0x6f,
	0x64, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x12, 0x39, 0x0a, 0x09, 0x61, 0x6e, 0x74, 0x68, 0x72, 0x6f, 0x70,
	0x69, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x6e, 0x74, 0x68, 0x72, 0x6f, 0x70, 0x69, 0x63, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x61, 0x6e, 0x74, 0x68, 0x72, 0x6f, 0x70, 0x69, 0x63,
	0x22, 0xd7, 0x01, 0x0a, 0x10, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x39, 0x0a, 0x09, 0x71, 0x77, 0x65, 0x6e, 0x56, 0x6c, 0x4d,
	0x61, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x4c, 0x4d, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x71, 0x77, 0x65, 0x6e, 0x56, 0x6c, 0x4d, 0x61, 0x78,
	0x12, 0x3b, 0x0a, 0x0a, 0x67, 0x6c, 0x6d, 0x34, 0x76, 0x66, 0x6c, 0x61, 0x73, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x4c, 0x4d, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0a, 0x67, 0x6c, 0x6d, 0x34, 0x76, 0x66, 0x6c, 0x61, 0x73, 0x68, 0x12, 0x4b, 0x0a,
	0x12, 0x64, 0x6f, 0x75, 0x62, 0x61, 0x6f, 0x50, 0x72, 0x6f, 0x56, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x33, 0x32, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x4c, 0x4d,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x12, 0x64, 0x6f, 0x75, 0x62, 0x61, 0x6f, 0x50, 0x72,
	0x6f, 0x56, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x33, 0x32, 0x6b, 0x22, 0x89, 0x03, 0x0a, 0x0b, 0x41,
	0x7a, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x65,
	0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x6f, 0x70, 0x50, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x74, 0x6f, 0x70, 0x50,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x67, 0x70,
	0x74, 0x34, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0b, 0x67, 0x70, 0x74, 0x34, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x2a, 0x0a, 0x10,
	0x67, 0x70, 0x74, 0x34, 0x54, 0x75, 0x72, 0x62, 0x6f, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x67, 0x70, 0x74, 0x34, 0x54, 0x75, 0x72, 0x62,
	0x6f, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x32, 0x0a, 0x14, 0x67, 0x70, 0x74, 0x34,
	0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x67, 0x70, 0x74, 0x34, 0x45, 0x78, 0x63, 0x6c,
	0x75, 0x73, 0x69, 0x76, 0x65, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x18,
	0x0a, 0x07, 0x67, 0x70, 0x74, 0x34, 0x55, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x67, 0x70, 0x74, 0x34, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x67, 0x70, 0x74, 0x34,
	0x54, 0x75, 0x72, 0x62, 0x6f, 0x55, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x67, 0x70, 0x74, 0x34, 0x54, 0x75, 0x72, 0x62, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x10,
	0x67, 0x70, 0x74, 0x34, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x55, 0x72, 0x6c,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x67, 0x70, 0x74, 0x34, 0x45, 0x78, 0x63, 0x6c,
	0x75, 0x73, 0x69, 0x76, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x69, 0x74,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x69, 0x6e,
	0x69, 0x74, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x0c, 0x0a, 0x01, 0x6e, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x01, 0x6e, 0x22, 0x8d, 0x07, 0x0a, 0x09, 0x54, 0x61, 0x6c, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x3a, 0x0a, 0x06, 0x74, 0x61, 0x6c, 0x37, 0x30, 0x62, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x54, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x61, 0x6c, 0x4c,
	0x6c, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x74, 0x61, 0x6c, 0x37, 0x30, 0x62,
	0x12, 0x38, 0x0a, 0x05, 0x74, 0x61, 0x6c, 0x37, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x61, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x61, 0x6c, 0x4c, 0x6c, 0x6d, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x05, 0x74, 0x61, 0x6c, 0x37, 0x62, 0x12, 0x41, 0x0a, 0x0a, 0x6c, 0x6c,
	0x61, 0x6d, 0x61, 0x33, 0x5f, 0x37, 0x30, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x61, 0x6c, 0x4c, 0x6c, 0x6d, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x09, 0x6c, 0x6c, 0x61, 0x6d, 0x61, 0x33, 0x37, 0x30, 0x62, 0x12, 0x49, 0x0a,
	0x0e, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x54, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x61, 0x6c,
	0x4c, 0x6c, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x65, 0x6e, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x0e, 0x63, 0x68, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x61,
	0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x61, 0x6c, 0x4c, 0x6c, 0x6d, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x63, 0x68, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x02, 0x7a, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x61, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x61, 0x6c, 0x4c, 0x6c, 0x6d, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x02, 0x7a, 0x68, 0x12, 0x32, 0x0a, 0x02, 0x65, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x54, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x61, 0x6c, 0x4c, 0x6c,
	0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x02, 0x65, 0x6e, 0x12, 0x52, 0x0a, 0x12, 0x6d,
	0x61, 0x74, 0x68, 0x47, 0x70, 0x74, 0x42, 0x61, 0x73, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54,
	0x61, 0x6c, 0x4c, 0x6c, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x12, 0x6d, 0x61, 0x74,
	0x68, 0x47, 0x70, 0x74, 0x42, 0x61, 0x73, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x12,
	0x43, 0x0a, 0x0b, 0x65, 0x6e, 0x5f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x54, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x61, 0x6c, 0x4c,
	0x6c, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x65, 0x6e, 0x44, 0x69, 0x61, 0x6c,
	0x6f, 0x67, 0x75, 0x65, 0x12, 0x52, 0x0a, 0x17, 0x6c, 0x6c, 0x61, 0x6d, 0x61, 0x33, 0x5f, 0x37,
	0x30, 0x62, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x4c, 0x4d, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x14, 0x6c, 0x6c, 0x61, 0x6d, 0x61, 0x33, 0x37, 0x30, 0x62, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x53, 0x71, 0x75, 0x61, 0x72, 0x65, 0x1a, 0xdb, 0x01, 0x0a, 0x0c, 0x54, 0x61, 0x6c,
	0x4c, 0x6c, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x3d, 0x0a, 0x03, 0x6d, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x54, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x61, 0x6c, 0x4c, 0x6c, 0x6d,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x6f, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x03, 0x6d, 0x6f, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x69, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x1a, 0x36,
	0x0a, 0x08, 0x4d, 0x6f, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb1, 0x01, 0x0a, 0x0b, 0x42, 0x61, 0x69, 0x64, 0x75,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x61, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x55, 0x72,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x74, 0x55, 0x72, 0x6c,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xbc, 0x01, 0x0a, 0x0e, 0x42,
	0x61, 0x69, 0x43, 0x68, 0x75, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x5f, 0x65, 0x6e, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x11, 0x77, 0x69, 0x74, 0x68, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x6e,
	0x68, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12,
	0x18, 0x0a, 0x07, 0x69, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x69, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x22, 0xa8, 0x02, 0x0a, 0x0b, 0x45, 0x72,
	0x6e, 0x69, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x42, 0x0a, 0x08, 0x65, 0x72, 0x6e,
	0x69, 0x65, 0x42, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x6e, 0x69, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x45, 0x72, 0x6e, 0x69, 0x65, 0x42, 0x6f, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x08, 0x65, 0x72, 0x6e, 0x69, 0x65, 0x42, 0x6f, 0x74, 0x12, 0x46, 0x0a,
	0x0a, 0x65, 0x72, 0x6e, 0x69, 0x65, 0x42, 0x6f, 0x74, 0x38, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45,
	0x72, 0x6e, 0x69, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x45, 0x72, 0x6e, 0x69, 0x65,
	0x42, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x65, 0x72, 0x6e, 0x69, 0x65,
	0x42, 0x6f, 0x74, 0x38, 0x6b, 0x1a, 0x8c, 0x01, 0x0a, 0x0e, 0x45, 0x72, 0x6e, 0x69, 0x65, 0x42,
	0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x20,
	0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x69, 0x73, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x22, 0xb5, 0x01, 0x0a, 0x0c, 0x54, 0x6f, 0x6e, 0x67, 0x79, 0x69, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x35, 0x0a, 0x07, 0x71, 0x77, 0x65, 0x6e, 0x4d, 0x61, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x4c, 0x4d, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x07, 0x71, 0x77, 0x65, 0x6e, 0x4d, 0x61, 0x78, 0x12, 0x35, 0x0a, 0x07,
	0x71, 0x77, 0x65, 0x6e, 0x37, 0x32, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x4c, 0x4c, 0x4d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x07, 0x71, 0x77, 0x65, 0x6e,
	0x37, 0x32, 0x62, 0x12, 0x37, 0x0a, 0x08, 0x71, 0x77, 0x65, 0x6e, 0x50, 0x6c, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x4c, 0x4d, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x08, 0x71, 0x77, 0x65, 0x6e, 0x50, 0x6c, 0x75, 0x73, 0x22, 0x92, 0x01, 0x0a,
	0x0c, 0x44, 0x6f, 0x75, 0x42, 0x61, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x41, 0x0a,
	0x0d, 0x64, 0x6f, 0x75, 0x42, 0x61, 0x6f, 0x4c, 0x69, 0x74, 0x65, 0x33, 0x32, 0x6b, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x4c, 0x4d, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0d, 0x64, 0x6f, 0x75, 0x42, 0x61, 0x6f, 0x4c, 0x69, 0x74, 0x65, 0x33, 0x32, 0x6b,
	0x12, 0x3f, 0x0a, 0x0c, 0x64, 0x6f, 0x75, 0x42, 0x61, 0x6f, 0x50, 0x72, 0x6f, 0x33, 0x32, 0x6b,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x4c, 0x4d, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0c, 0x64, 0x6f, 0x75, 0x42, 0x61, 0x6f, 0x50, 0x72, 0x6f, 0x33, 0x32,
	0x6b, 0x22, 0x56, 0x0a, 0x0f, 0x41, 0x6e, 0x74, 0x68, 0x72, 0x6f, 0x70, 0x69, 0x63, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x43, 0x0a, 0x0e, 0x63, 0x6c, 0x61, 0x75, 0x64, 0x65, 0x33, 0x37,
	0x53, 0x6f, 0x6e, 0x6e, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x4c, 0x4c, 0x4d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x63, 0x6c, 0x61, 0x75, 0x64,
	0x65, 0x33, 0x37, 0x53, 0x6f, 0x6e, 0x6e, 0x65, 0x74, 0x22, 0xe3, 0x01, 0x0a, 0x0e, 0x44, 0x65,
	0x65, 0x70, 0x53, 0x65, 0x65, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3b, 0x0a, 0x0a,
	0x64, 0x65, 0x65, 0x70, 0x53, 0x65, 0x65, 0x6b, 0x52, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x4c, 0x4d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x64,
	0x65, 0x65, 0x70, 0x53, 0x65, 0x65, 0x6b, 0x52, 0x31, 0x12, 0x57, 0x0a, 0x18, 0x64, 0x65, 0x65,
	0x70, 0x53, 0x65, 0x65, 0x6b, 0x52, 0x31, 0x44, 0x69, 0x73, 0x74, 0x69, 0x6c, 0x6c, 0x51, 0x77,
	0x65, 0x6e, 0x33, 0x32, 0x42, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c,
	0x4c, 0x4d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x18, 0x64, 0x65, 0x65, 0x70, 0x53, 0x65,
	0x65, 0x6b, 0x52, 0x31, 0x44, 0x69, 0x73, 0x74, 0x69, 0x6c, 0x6c, 0x51, 0x77, 0x65, 0x6e, 0x33,
	0x32, 0x42, 0x12, 0x3b, 0x0a, 0x0a, 0x64, 0x65, 0x65, 0x70, 0x53, 0x65, 0x65, 0x6b, 0x56, 0x33,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x4c, 0x4d, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0a, 0x64, 0x65, 0x65, 0x70, 0x53, 0x65, 0x65, 0x6b, 0x56, 0x33, 0x22,
	0x8d, 0x01, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x4c, 0x4d, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x65, 0x6d,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b,
	0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69,
	0x4b, 0x65, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x69, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x22,
	0x90, 0x01, 0x0a, 0x03, 0x4c, 0x6f, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x12, 0x17, 0x0a,
	0x07, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x6d, 0x61, 0x78, 0x41, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x42, 0x20, 0x5a, 0x1e, 0x6c, 0x6c, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x3b,
	0x63, 0x6f, 0x6e, 0x66, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_internal_conf_conf_proto_rawDescOnce sync.Once
	file_internal_conf_conf_proto_rawDescData = file_internal_conf_conf_proto_rawDesc
)

func file_internal_conf_conf_proto_rawDescGZIP() []byte {
	file_internal_conf_conf_proto_rawDescOnce.Do(func() {
		file_internal_conf_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_conf_conf_proto_rawDescData)
	})
	return file_internal_conf_conf_proto_rawDescData
}

var file_internal_conf_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_internal_conf_conf_proto_goTypes = []interface{}{
	(*Bootstrap)(nil),                         // 0: kratos.api.Bootstrap
	(*Business)(nil),                          // 1: kratos.api.Business
	(*ForbiddenItem)(nil),                     // 2: kratos.api.ForbiddenItem
	(*YachRobot)(nil),                         // 3: kratos.api.YachRobot
	(*Server)(nil),                            // 4: kratos.api.Server
	(*Data)(nil),                              // 5: kratos.api.Data
	(*DatabaseConfig)(nil),                    // 6: kratos.api.DatabaseConfig
	(*RedisConfig)(nil),                       // 7: kratos.api.RedisConfig
	(*ThirdPartyConfig)(nil),                  // 8: kratos.api.ThirdPartyConfig
	(*LlmConfig)(nil),                         // 9: kratos.api.LlmConfig
	(*MultiModalConfig)(nil),                  // 10: kratos.api.MultiModalConfig
	(*AzureConfig)(nil),                       // 11: kratos.api.AzureConfig
	(*TalConfig)(nil),                         // 12: kratos.api.TalConfig
	(*BaiduConfig)(nil),                       // 13: kratos.api.BaiduConfig
	(*BaiChuanConfig)(nil),                    // 14: kratos.api.BaiChuanConfig
	(*ErnieConfig)(nil),                       // 15: kratos.api.ErnieConfig
	(*TongyiConfig)(nil),                      // 16: kratos.api.TongyiConfig
	(*DouBaoConfig)(nil),                      // 17: kratos.api.DouBaoConfig
	(*AnthropicConfig)(nil),                   // 18: kratos.api.AnthropicConfig
	(*DeepSeekConfig)(nil),                    // 19: kratos.api.DeepSeekConfig
	(*CommonLLMConfig)(nil),                   // 20: kratos.api.CommonLLMConfig
	(*Log)(nil),                               // 21: kratos.api.Log
	nil,                                       // 22: kratos.api.Business.StrategyEntry
	nil,                                       // 23: kratos.api.Business.ForbiddenRuleEntry
	(*Server_HTTP)(nil),                       // 24: kratos.api.Server.HTTP
	(*Server_GRPC)(nil),                       // 25: kratos.api.Server.GRPC
	(*ThirdPartyConfig_CensorBizConfig)(nil),  // 26: kratos.api.ThirdPartyConfig.CensorBizConfig
	(*ThirdPartyConfig_LegalCheckConfig)(nil), // 27: kratos.api.ThirdPartyConfig.LegalCheckConfig
	nil,                                // 28: kratos.api.ThirdPartyConfig.LegalCheckConfig.BizMapEntry
	(*TalConfig_TalLlmConfig)(nil),     // 29: kratos.api.TalConfig.TalLlmConfig
	nil,                                // 30: kratos.api.TalConfig.TalLlmConfig.ModEntry
	(*ErnieConfig_ErnieBotConfig)(nil), // 31: kratos.api.ErnieConfig.ErnieBotConfig
	(*durationpb.Duration)(nil),        // 32: google.protobuf.Duration
}
var file_internal_conf_conf_proto_depIdxs = []int32{
	4,  // 0: kratos.api.Bootstrap.server:type_name -> kratos.api.Server
	5,  // 1: kratos.api.Bootstrap.data:type_name -> kratos.api.Data
	21, // 2: kratos.api.Bootstrap.log:type_name -> kratos.api.Log
	1,  // 3: kratos.api.Bootstrap.business:type_name -> kratos.api.Business
	22, // 4: kratos.api.Business.strategy:type_name -> kratos.api.Business.StrategyEntry
	23, // 5: kratos.api.Business.forbiddenRule:type_name -> kratos.api.Business.ForbiddenRuleEntry
	3,  // 6: kratos.api.Business.yachRobot:type_name -> kratos.api.YachRobot
	24, // 7: kratos.api.Server.http:type_name -> kratos.api.Server.HTTP
	25, // 8: kratos.api.Server.grpc:type_name -> kratos.api.Server.GRPC
	6,  // 9: kratos.api.Data.database:type_name -> kratos.api.DatabaseConfig
	7,  // 10: kratos.api.Data.redis:type_name -> kratos.api.RedisConfig
	8,  // 11: kratos.api.Data.thirdParty:type_name -> kratos.api.ThirdPartyConfig
	9,  // 12: kratos.api.Data.llm:type_name -> kratos.api.LlmConfig
	27, // 13: kratos.api.ThirdPartyConfig.legalCheck:type_name -> kratos.api.ThirdPartyConfig.LegalCheckConfig
	11, // 14: kratos.api.LlmConfig.azure:type_name -> kratos.api.AzureConfig
	12, // 15: kratos.api.LlmConfig.tal:type_name -> kratos.api.TalConfig
	13, // 16: kratos.api.LlmConfig.baidu:type_name -> kratos.api.BaiduConfig
	14, // 17: kratos.api.LlmConfig.baichuan:type_name -> kratos.api.BaiChuanConfig
	15, // 18: kratos.api.LlmConfig.ernie:type_name -> kratos.api.ErnieConfig
	16, // 19: kratos.api.LlmConfig.tongyi:type_name -> kratos.api.TongyiConfig
	17, // 20: kratos.api.LlmConfig.doubao:type_name -> kratos.api.DouBaoConfig
	19, // 21: kratos.api.LlmConfig.deepSeek:type_name -> kratos.api.DeepSeekConfig
	10, // 22: kratos.api.LlmConfig.multiModal:type_name -> kratos.api.MultiModalConfig
	18, // 23: kratos.api.LlmConfig.anthropic:type_name -> kratos.api.AnthropicConfig
	20, // 24: kratos.api.MultiModalConfig.qwenVlMax:type_name -> kratos.api.CommonLLMConfig
	20, // 25: kratos.api.MultiModalConfig.glm4vflash:type_name -> kratos.api.CommonLLMConfig
	20, // 26: kratos.api.MultiModalConfig.doubaoProVision32k:type_name -> kratos.api.CommonLLMConfig
	29, // 27: kratos.api.TalConfig.tal70b:type_name -> kratos.api.TalConfig.TalLlmConfig
	29, // 28: kratos.api.TalConfig.tal7b:type_name -> kratos.api.TalConfig.TalLlmConfig
	29, // 29: kratos.api.TalConfig.llama3_70b:type_name -> kratos.api.TalConfig.TalLlmConfig
	29, // 30: kratos.api.TalConfig.en_composition:type_name -> kratos.api.TalConfig.TalLlmConfig
	29, // 31: kratos.api.TalConfig.ch_composition:type_name -> kratos.api.TalConfig.TalLlmConfig
	29, // 32: kratos.api.TalConfig.zh:type_name -> kratos.api.TalConfig.TalLlmConfig
	29, // 33: kratos.api.TalConfig.en:type_name -> kratos.api.TalConfig.TalLlmConfig
	29, // 34: kratos.api.TalConfig.mathGptBaseBackend:type_name -> kratos.api.TalConfig.TalLlmConfig
	29, // 35: kratos.api.TalConfig.en_dialogue:type_name -> kratos.api.TalConfig.TalLlmConfig
	20, // 36: kratos.api.TalConfig.llama3_70b_model_square:type_name -> kratos.api.CommonLLMConfig
	31, // 37: kratos.api.ErnieConfig.ernieBot:type_name -> kratos.api.ErnieConfig.ErnieBotConfig
	31, // 38: kratos.api.ErnieConfig.ernieBot8k:type_name -> kratos.api.ErnieConfig.ErnieBotConfig
	20, // 39: kratos.api.TongyiConfig.qwenMax:type_name -> kratos.api.CommonLLMConfig
	20, // 40: kratos.api.TongyiConfig.qwen72b:type_name -> kratos.api.CommonLLMConfig
	20, // 41: kratos.api.TongyiConfig.qwenPlus:type_name -> kratos.api.CommonLLMConfig
	20, // 42: kratos.api.DouBaoConfig.douBaoLite32k:type_name -> kratos.api.CommonLLMConfig
	20, // 43: kratos.api.DouBaoConfig.douBaoPro32k:type_name -> kratos.api.CommonLLMConfig
	20, // 44: kratos.api.AnthropicConfig.claude37Sonnet:type_name -> kratos.api.CommonLLMConfig
	20, // 45: kratos.api.DeepSeekConfig.deepSeekR1:type_name -> kratos.api.CommonLLMConfig
	20, // 46: kratos.api.DeepSeekConfig.deepSeekR1DistillQwen32B:type_name -> kratos.api.CommonLLMConfig
	20, // 47: kratos.api.DeepSeekConfig.deepSeekV3:type_name -> kratos.api.CommonLLMConfig
	2,  // 48: kratos.api.Business.ForbiddenRuleEntry.value:type_name -> kratos.api.ForbiddenItem
	32, // 49: kratos.api.Server.HTTP.timeout:type_name -> google.protobuf.Duration
	32, // 50: kratos.api.Server.GRPC.timeout:type_name -> google.protobuf.Duration
	28, // 51: kratos.api.ThirdPartyConfig.LegalCheckConfig.bizMap:type_name -> kratos.api.ThirdPartyConfig.LegalCheckConfig.BizMapEntry
	26, // 52: kratos.api.ThirdPartyConfig.LegalCheckConfig.BizMapEntry.value:type_name -> kratos.api.ThirdPartyConfig.CensorBizConfig
	30, // 53: kratos.api.TalConfig.TalLlmConfig.mod:type_name -> kratos.api.TalConfig.TalLlmConfig.ModEntry
	54, // [54:54] is the sub-list for method output_type
	54, // [54:54] is the sub-list for method input_type
	54, // [54:54] is the sub-list for extension type_name
	54, // [54:54] is the sub-list for extension extendee
	0,  // [0:54] is the sub-list for field type_name
}

func init() { file_internal_conf_conf_proto_init() }
func file_internal_conf_conf_proto_init() {
	if File_internal_conf_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_internal_conf_conf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bootstrap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Business); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForbiddenItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*YachRobot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DatabaseConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedisConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThirdPartyConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LlmConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultiModalConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AzureConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TalConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaiduConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaiChuanConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErnieConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TongyiConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DouBaoConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnthropicConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeepSeekConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonLLMConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Log); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_HTTP); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_GRPC); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThirdPartyConfig_CensorBizConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThirdPartyConfig_LegalCheckConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TalConfig_TalLlmConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErnieConfig_ErnieBotConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_conf_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_internal_conf_conf_proto_goTypes,
		DependencyIndexes: file_internal_conf_conf_proto_depIdxs,
		MessageInfos:      file_internal_conf_conf_proto_msgTypes,
	}.Build()
	File_internal_conf_conf_proto = out.File
	file_internal_conf_conf_proto_rawDesc = nil
	file_internal_conf_conf_proto_goTypes = nil
	file_internal_conf_conf_proto_depIdxs = nil
}
