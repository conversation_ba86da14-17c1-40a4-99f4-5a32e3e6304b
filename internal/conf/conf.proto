syntax = "proto3";
package kratos.api;

option go_package = "llm_service/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  Log log = 3;
  Business business = 4;
}

message Business {
  map<string,string> strategy = 1;
  map<string,ForbiddenItem> forbiddenRule = 2;
  YachRobot yachRobot = 3;
}

message ForbiddenItem {
  int64 duration = 1;
  int64 times = 2;
  int64 freeze = 3;
}

message YachRobot{
  bool enable = 1;
  string accessToken = 2;
  string secret = 3;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
  string env = 3;
}

message Data {
  DatabaseConfig database = 1;
  RedisConfig redis = 2;
  ThirdPartyConfig thirdParty = 3;
  LlmConfig llm = 4;
}

// 数据库配置相关的消息
message DatabaseConfig {
  string driver = 1; // 数据库驱动程序，如 MySQL、PostgreSQL
  string source = 2; // 数据源，如连接字符串或数据库路径
}

message RedisConfig {
  string host = 1;
  int32 port = 2;
  string password = 3;
  int32 db = 4;
}

// ThirdParty配置相关的消息
message ThirdPartyConfig {
  message CensorBizConfig {
    string serviceId = 1;
    string input = 2;
    string output = 3;
  }
  message LegalCheckConfig {
    string host = 1;
    map<string, CensorBizConfig> bizMap = 2;
  }
  LegalCheckConfig legalCheck = 1;
}

message LlmConfig {
  string filterPunctuation = 1;
  AzureConfig azure = 2;
  TalConfig tal = 3;
  BaiduConfig baidu = 4;
  BaiChuanConfig baichuan = 5;
  ErnieConfig ernie = 6;
  TongyiConfig tongyi = 7;
  DouBaoConfig doubao = 8;
  DeepSeekConfig deepSeek = 9;
  MultiModalConfig multiModal = 10;
  AnthropicConfig anthropic = 11;
}

message MultiModalConfig {
  CommonLLMConfig qwenVlMax = 1;
  CommonLLMConfig glm4vflash = 2;
  CommonLLMConfig doubaoProVision32k = 3;
}

// Azure配置相关的消息
message AzureConfig {
  float temperature = 1;
  float topP = 2;
  repeated string apiKeys = 3;
  repeated string gpt4ApiKeys = 4;
  repeated string gpt4TurboApiKeys = 5;
  repeated string gpt4ExclusiveApiKeys = 6;
  string url = 7;
  string gpt4Url = 8;
  string gpt4TurboUrl = 9;
  string gpt4ExclusiveUrl = 10;
  int32 initSwitch = 11;
  float n = 12;
}

// Tal配置相关的消息
message TalConfig {
  TalLlmConfig tal70b = 1;
  TalLlmConfig tal7b = 2;
  TalLlmConfig llama3_70b = 3;
  TalLlmConfig en_composition = 4;
  TalLlmConfig ch_composition = 5;
  TalLlmConfig zh = 6;
  TalLlmConfig en = 7;
  TalLlmConfig mathGptBaseBackend = 8;
  TalLlmConfig en_dialogue = 9;
  CommonLLMConfig llama3_70b_model_square = 10;

  message TalLlmConfig {
    string url = 1;
    string key = 2;
    string secret = 3;
    map<string,string> mod = 4;
    int32 isCheck = 5;
  }
}

// Baidu配置相关的消息
message BaiduConfig {
  string clientId = 1;
  string clientSecret = 2;
  string grantType = 3;
  string url = 4;
  string chatUrl = 5;
  string version = 6;
}

// BaiChuan配置相关的消息
message BaiChuanConfig {
  string model = 1;
  float temperature = 2;
  bool with_search_enhance = 3;
  string apiKey = 4;
  string url = 5;
  int32 isCheck = 6;
}

// Ernie配置相关的消息
message ErnieConfig {
  ErnieBotConfig ernieBot = 1;
  ErnieBotConfig ernieBot8k = 2;

  message ErnieBotConfig {
    string model = 1;
    float temperature = 2;
    string apiKey = 3;
    string url = 4;
    int32 isCheck = 5;
  }
}

// Tongyi配置相关的消息
message TongyiConfig {
  CommonLLMConfig qwenMax = 1;
  CommonLLMConfig qwen72b = 2;
  CommonLLMConfig qwenPlus = 3;
}

// DouBao配置相关的消息
message DouBaoConfig {
  CommonLLMConfig douBaoLite32k = 1;
  CommonLLMConfig douBaoPro32k = 2;
}

message AnthropicConfig{
  CommonLLMConfig claude37Sonnet = 1;
}

// DouBao配置相关的消息
message DeepSeekConfig {
  CommonLLMConfig deepSeekR1 = 1;
  CommonLLMConfig deepSeekR1DistillQwen32B = 2;
  CommonLLMConfig deepSeekV3 = 3;
}

message CommonLLMConfig {
  string model = 1;
  float temperature = 2;
  string apiKey = 3;
  string url = 4;
  int32 isCheck = 5;
}

message Log {
  string filename = 1;
  int32 max_size = 2;
  int32 max_backup = 3;
  int32 max_age = 4;
  bool compress = 5;
}