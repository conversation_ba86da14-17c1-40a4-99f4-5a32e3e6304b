package util

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"reflect"
)

func bodyFormat(bodyParams map[string]interface{}) (result string) {
	params := url.Values{}
	for k, v := range bodyParams {
		switch reflect.TypeOf(v).Kind() {
		case reflect.String:
			params.Add(k, v.(string))
			break
		default:
			vJson, _ := json.Marshal(v)
			params.Add(k, string(vJson))
			break
		}
	}
	return params.Encode()
}

func DoGet(url string, contentType string) (r []byte, e error) {
	client := &http.Client{}
	reqest, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	reqest.Header.Add("Content-Type", contentType)
	response, err := client.Do(reqest)
	if err != nil {
		return nil, err
	}
	if response.StatusCode != http.StatusOK {
		e = errors.New(fmt.Sprintf("do request url(%s) status code %d", url, response.StatusCode))
		return
	}
	defer response.Body.Close()
	return ioutil.ReadAll(response.Body)
}

func DoPost(url string, contentType string, bodyParams map[string]interface{}) (r []byte, e error) {
	var body io.Reader
	if contentType == ApplicationXWwwFormUrlencoded {
		body = bytes.NewBufferString(bodyFormat(bodyParams))
	} else if contentType == MultipartFormData {
		body := bodyParams[MultipartFormDataBody]
		println(body)
	} else if contentType == Binary {
		body := bytes.NewBuffer(bodyParams[BinaryBody].([]byte))
		println(body)
	} else {
		bytesData, err := json.Marshal(bodyParams)
		if err != nil {
			return nil, errors.New("json.Marshal body_params error")
		}
		body = bytes.NewReader(bytesData)
	}

	client := &http.Client{}
	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", contentType)
	response, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	if response.StatusCode != http.StatusOK {
		e = errors.New(fmt.Sprintf("do request url(%s) status code %d", url, response.StatusCode))
		return
	}
	defer response.Body.Close()
	return ioutil.ReadAll(response.Body)
}

func DoPatch(url string, contentType string, bodyParams map[string]interface{}) (r []byte, e error) {
	var body io.Reader
	if contentType == ApplicationXWwwFormUrlencoded {
		body = bytes.NewBufferString(bodyFormat(bodyParams))
	} else if contentType == MultipartFormData {
		body := bodyParams[MultipartFormDataBody]
		println(body)
	} else if contentType == Binary {
		body := bytes.NewBuffer(bodyParams[BinaryBody].([]byte))
		println(body)
	} else {
		bytesData, err := json.Marshal(bodyParams)
		if err != nil {
			return nil, errors.New("json.Marshal body_params error")
		}
		body = bytes.NewReader(bytesData)
	}

	client := &http.Client{}
	req, err := http.NewRequest("PATCH", url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", contentType)
	response, err := client.Do(req)
	if err != nil {
		println(err)
	}
	defer response.Body.Close()
	return ioutil.ReadAll(response.Body)
}

func DoPut(url string, contentType string, bodyParams map[string]interface{}) (r []byte, e error) {
	var body io.Reader
	if contentType == ApplicationXWwwFormUrlencoded {
		body = bytes.NewBufferString(bodyFormat(bodyParams))
	} else if contentType == MultipartFormData {
		body := bodyParams[MultipartFormDataBody]
		println(body)
	} else if contentType == Binary {
		body := bytes.NewBuffer(bodyParams[BinaryBody].([]byte))
		println(body)
	} else {
		bytesData, err := json.Marshal(bodyParams)
		if err != nil {
			return nil, errors.New("json.Marshal body_params error")
		}
		body = bytes.NewReader(bytesData)
	}

	client := &http.Client{}
	req, err := http.NewRequest("PUT", url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", contentType)
	response, err := client.Do(req)
	if err != nil {
		println(err)
	}
	defer response.Body.Close()
	return ioutil.ReadAll(response.Body)
}

func DoDelete(url string, contentType string) (r []byte, e error) {
	client := &http.Client{}
	reqest, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		return nil, err
	}
	reqest.Header.Add("Content-Type", contentType)
	response, err := client.Do(reqest)
	defer response.Body.Close()
	return ioutil.ReadAll(response.Body)
}
