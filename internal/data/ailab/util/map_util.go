package util

func MapisemptStringinterface(m map[string]interface{}) (result bool) {
	if m == nil && len(m) == 0 {
		return true
	}
	return false
}

func MapisnotemptStringinterface(m map[string]interface{}) (result bool) {
	if m == nil && len(m) == 0 {
		return false
	}
	return true
}

func MapisemptStringstring(m map[string]interface{}) (result bool) {
	if m == nil && len(m) == 0 {
		return true
	}
	return false
}

func MapisnotemptStringstring(m map[string]interface{}) (result bool) {
	if m == nil && len(m) == 0 {
		return false
	}
	return true
}
