package util

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/gob"
	"encoding/json"
	"net/url"
	"reflect"
	"sort"
	"strings"
)

const requestBody = "request_body"
const ApplicationJson = "application/json"
const ApplicationXWwwFormUrlencoded = "application/x-www-form-urlencoded"
const MultipartFormData = "multipart/form-data"
const MultipartFormDataBody = "multipartformDataBody"
const Binary = "binary"
const BinaryBody = "BinaryBody"

// HmacSha1 使用HmacSha1计算签名
func HmacSha1(secret string, query string) string {
	secret = secret + "&"
	key := []byte(secret)
	mac := hmac.New(sha1.New, key)
	mac.Write([]byte(query))
	query = base64.StdEncoding.EncodeToString(mac.Sum(nil))
	return query
}

// GetSignature 格式化入参，并计算签名
func GetSignature(
	urlParams map[string]string,
	bodyParams map[string]interface{},
	requestMethod string,
	contentType string,
	accessKeySecret string) (signature string, signatureNonce string) {

	signatureNonce = GenUUIDv4()

	signParams := make(map[string]interface{})
	signParams["signature_nonce"] = signatureNonce

	//只有Application_x_www_form_urlencoded和Application_x_www_form_urlencoded，且是POST/PATCH/PUT时，body才参与鉴权
	if bodyParams != nil && len(bodyParams) != 0 && (requestMethod == "POST" || requestMethod == "PATCH" || requestMethod == "PUT") && (contentType == ApplicationXWwwFormUrlencoded || contentType == ApplicationJson) {
		if contentType == ApplicationXWwwFormUrlencoded {
			bodyParamsEncode := url.Values{}
			for k, v := range bodyParams {
				//str, _ := json.Marshal(v)
				//bodyParamsEncode.Add(k, string(str))

				switch reflect.TypeOf(v).Kind() {
				case reflect.String:
					bodyParamsEncode.Add(k, v.(string))
					break
				default:
					vJson, _ := json.Marshal(v)
					bodyParamsEncode.Add(k, string(vJson))
					break
				}
			}
			//对body进行format，并不是URLEncode
			body := bodyParamsEncode.Encode()
			signParams[requestBody] = body
		} else {
			bodyJson, _ := json.Marshal(bodyParams)
			signParams[requestBody] = string(bodyJson)
		}
	}

	for k, v := range urlParams {
		signParams[k] = v
	}

	sortKeys := SortMapKey(signParams)

	stringToSign := SingFormat(sortKeys, signParams)
	signature = HmacSha1(accessKeySecret, stringToSign)
	return signature, signatureNonce
}

//func url_format(sort_key[]string,sign_params map[string]interface{})(result string)  {
//	var params []string
//	for _, key := range sort_key {
//		value := sign_params[key]
//		//param, _ := json.MarshalIndent(value, "", "    ")
//		value_str, _ := json.Marshal(value)
//
//		param := key + "=" + string(value_str)
//		params = append(params, param)
//	}
//
//	result = strings.Join(params,"&")
//	return result
//}

func GetInterfaceToBytes(key interface{}) (result []byte, err error) {
	//var buf bytes.Buffer
	//enc := gob.NewEncoder(&buf)
	//err := enc.Encode(key)
	//if err != nil {
	//	return nil, err
	//}
	//return buf.Bytes(), nil

	var rawRoomIdBuffer bytes.Buffer
	enc := gob.NewEncoder(&rawRoomIdBuffer)
	if err = enc.Encode(key); err != nil {
		return nil, err
	}
	return rawRoomIdBuffer.Bytes(), nil

}

// SingFormat 计算签名参数格式化
func SingFormat(sortKeys []string, parameters map[string]interface{}) (result string) {
	var paramList []string
	for _, k := range sortKeys {
		v := parameters[k]

		var buffer bytes.Buffer
		buffer.WriteString(k)
		buffer.WriteString("=")
		//vByte,_  := GetInterfaceToBytes(v)
		//buffer.Write(vByte)

		switch reflect.TypeOf(v).Kind() {
		case reflect.String:
			buffer.WriteString(v.(string))
		default:
			vJson, _ := json.Marshal(v)
			buffer.WriteString(string(vJson))
		}
		paramList = append(paramList, buffer.String())
	}
	return strings.Join(paramList, "&")
}

// UrlFormat 入参格式化为URL参数形式
func UrlFormat(parameters map[string]string) (result string) {
	params := url.Values{}
	for k, v := range parameters {
		params.Add(k, v)
	}
	return params.Encode()
}

// SortMapKey 排序sourceMap，升序
func SortMapKey(sourceMap map[string]interface{}) (sortKeys []string) {
	for key := range sourceMap {
		sortKeys = append(sortKeys, key)
	}
	sort.Strings(sortKeys)
	return sortKeys
}
