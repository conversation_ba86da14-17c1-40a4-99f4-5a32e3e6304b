package data

import (
	"context"
	"fmt"
	"git.100tal.com/znxx_xpp/go-libs/traces"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"llm_service/internal/conf"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewData, NewChatDao)

// Data .
type Data struct {
	log *log.Helper
	db  *gorm.DB
	rdb *redis.Client
}

// NewData .
func NewData(conf *conf.Data, logger log.Logger) (*Data, func(), error) {
	db, err := traces.MakeGormClient(&traces.GormConfig{Dsn: conf.Database.Source})
	if err != nil {
		return nil, nil, err
	}

	rdb := traces.MakeRedisClient(&traces.RedisConfig{
		Host:     conf.Redis.Host,
		Port:     int(conf.Redis.Port),
		Password: conf.Redis.Password,
		Db:       int(conf.Redis.Db),
	})
	_, err = rdb.Ping(context.Background()).Result()
	if err != nil {
		fmt.Printf("err :%v", err.Error())
		return nil, nil, err
	}

	d := &Data{
		log: log.NewHelper(logger),
		db:  db,
		rdb: rdb,
	}
	cleanup := func() {
		_db, err := d.db.DB()
		if err != nil {
			d.log.Errorf("database close err:%+v", err)
		}
		_ = _db.Close()
		d.log.Info("closing the mysql")
	}
	return d, cleanup, nil
}
