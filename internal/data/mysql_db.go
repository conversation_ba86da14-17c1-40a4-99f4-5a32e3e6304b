package data

import (
	"context"
	"encoding/json"
	"llm_service/internal/data/mysql_model"
	"time"
)

const promptCacheKey = "prompt:app_id:%s:temp_id:%s"
const promptCacheExpire = 3 * time.Minute

func (g ChatDao) GetTemplate(c context.Context, appId, tempId string) (temp *mysql_model.Templates, err error) {
	temp = new(mysql_model.Templates)
	template, err := g.GetPromptByCache(c, appId, tempId)
	if err != nil {
		g.log.WithContext(c).Errorf("查询缓存失败 err=%v", err)
	} else if template != "" {
		err = json.Unmarshal([]byte(template), temp)
		if err == nil {
			return
		}
		g.log.WithContext(c).Errorf("json unmarshal失败 err=%v", err)
	}

	err = g.data.db.Table("gpt_templates").WithContext(c).
		//Where("tmp_id=? and app_id=?", tempId, appId).First(&temp).Error
		Where("tmp_id=?", tempId).First(&temp).Error
	if err != nil {
		g.log.WithContext(c).Warnf("查询模板失败 err=%v", err)
		return
	}
	tb, err := json.Marshal(temp)
	if err != nil {
		g.log.WithContext(c).Errorf("json marshal失败 err=%v", err)
		return
	}
	g.log.WithContext(c).Infof("DB 获取模板信息: %v", string(tb))
	g.SetPromptByCache(c, appId, tempId, string(tb))
	return
}
