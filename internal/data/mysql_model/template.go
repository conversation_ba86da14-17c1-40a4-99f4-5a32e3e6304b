package mysql_model

import "time"

type Templates struct {
	ID        int64     `gorm:"column:id" json:"id"`
	AppId     string    `gorm:"column:app_id" json:"app_id"` //  应用id
	TmpId     string    `gorm:"column:tmp_id" json:"tmp_id"` //  模板id
	TmpName   string    `gorm:"column:tmp_name" json:"tmp_name"`
	GptType   int64     `gorm:"column:gpt_type" json:"gpt_type"`     //   gpt 类 型
	Content   string    `gorm:"column:content" json:"content"`       //   模 板 内 容
	CreatedAt time.Time `gorm:"column:created_at" json:"created_at"` //   创 建 时 间
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at"` //   更 新 时 间
	DeletedAt time.Time `gorm:"column:deleted_at" json:"deleted_at"` //   删 除 时 间
}

type TemplateContent struct {
	GptType int64  `json:"gpt_type"` //   gpt 类 型
	System  string `json:"system"`   //   系 统 标 识
	Msg     string `json:"msg"`      //   模 板 内 容
}

func (Templates) TableName() string {
	return "gpt_templates"
}
