package data

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"llm_service/internal/pkg/kgin"
	"time"

	"github.com/redis/go-redis/v9"
)

const (
	AigcWenxinToken = "aigc:baidu:wenxin:token"
)

type Chat<PERSON>ao struct {
	log  *log.Helper
	data *Data
}

func NewChatDao(data *Data, logger log.Logger) (sb *ChatDao) {
	return &ChatDao{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (g ChatDao) RedisTtl(ctx context.Context, key string) (result time.Duration, err error) {
	return g.data.rdb.TTL(ctx, key).Result()
}

func (g ChatDao) RedisSet(ctx context.Context, key, value string, expiration time.Duration) error {
	return g.data.rdb.Set(ctx, key, value, expiration).Err()
}

func (g ChatDao) RedisGet(ctx context.Context, key string) (string, error) {
	return g.data.rdb.Get(ctx, key).Result()
}

func (g ChatDao) RedisExists(ctx context.Context, key string) bool {
	return g.data.rdb.Exists(ctx, key).Val() > 0
}

func (g ChatDao) RedisDel(ctx context.Context, key string) error {
	return g.data.rdb.Del(ctx, key).Err()
}

func (g ChatDao) RedisRpush(ctx context.Context, key string, value interface{}) error {
	return g.data.rdb.RPush(ctx, key, value).Err()
}

func (g ChatDao) RedisLpush(ctx context.Context, key string, value interface{}) error {
	return g.data.rdb.LPush(ctx, key, value).Err()
}

func (g ChatDao) RedisLpop(ctx context.Context, key string) (string, error) {
	return g.data.rdb.LPop(ctx, key).Result()
}

func (g ChatDao) RedisInSet(ctx *kgin.Context, key, value string) bool {
	return g.data.rdb.ZScore(ctx, key, value).Val() > 0
}

func (g ChatDao) RedisLlen(ctx context.Context, key string) (int64, error) {
	return g.data.rdb.LLen(ctx, key).Result()
}

func (g ChatDao) RedisLock(ctx context.Context, key string, second int) bool {
	expiration := time.Duration(second) * time.Second
	return g.data.rdb.SetNX(ctx, key, 1, expiration).Val()
}

func (g ChatDao) RedisUnlock(ctx context.Context, key string) {
	g.data.rdb.Del(ctx, key)
}

func (g ChatDao) RedisZAdd(ctx context.Context, key string, score float64, value interface{}) (int64, error) {
	return g.data.rdb.ZAdd(ctx, key, redis.Z{
		Score:  score,
		Member: value,
	}).Result()
}

func (g ChatDao) RedisZRangeByScore(ctx context.Context, key, min, max string, count int64) ([]string, error) {
	return g.data.rdb.ZRangeByScore(ctx, key, &redis.ZRangeBy{
		Min:   min,
		Max:   max,
		Count: count,
	}).Result()
}

func (g ChatDao) RedisZRevRangeWithScores(ctx context.Context, key string, start, stop int64) ([]redis.Z, error) {
	return g.data.rdb.ZRevRangeWithScores(ctx, key, start, stop).Result()
}

func (g ChatDao) RedisZRemRangeByRank(ctx context.Context, key string, start, stop int64) (int64, error) {
	return g.data.rdb.ZRemRangeByRank(ctx, key, start, stop).Result()
}

func (g ChatDao) RedisZRem(ctx context.Context, key string, member interface{}) (int64, error) {
	return g.data.rdb.ZRem(ctx, key, member).Result()
}

func (g ChatDao) RedisSAdd(ctx context.Context, key string, member interface{}) (int64, error) {
	return g.data.rdb.SAdd(ctx, key, member).Result()
}

func (g ChatDao) RedisSRem(ctx context.Context, key string, member interface{}) (int64, error) {
	return g.data.rdb.SRem(ctx, key, member).Result()
}

func (g ChatDao) RedisSMembers(ctx context.Context, key string) ([]string, error) {
	return g.data.rdb.SMembers(ctx, key).Result()
}

func (g ChatDao) RedisSCard(ctx context.Context, key string) (int64, error) {
	return g.data.rdb.SCard(ctx, key).Result()
}

func (g ChatDao) GetPromptByCache(c context.Context, appId, tempId string) (string, error) {
	key := fmt.Sprintf(promptCacheKey, appId, tempId)
	token, err := g.data.rdb.Get(c, key).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		g.log.WithContext(c).Errorf("获取缓存失败 err=%v", err)
		return "", err
	}
	g.log.WithContext(c).Infof("从缓存中查询key:%v ,value:%v", key, token)
	return token, nil
}

func (g ChatDao) SetPromptByCache(c context.Context, appId, tempId string, template string) {
	key := fmt.Sprintf(promptCacheKey, appId, tempId)
	_, err := g.data.rdb.Set(c, key, template, promptCacheExpire).Result()
	if err != nil {
		g.log.WithContext(c).Errorf("设置缓存失败 err=%v", err)
	}
}

func (g ChatDao) GetWenXinTokenByCache(c context.Context) (string, error) {
	key := fmt.Sprintf(AigcWenxinToken)
	token, err := g.data.rdb.Get(c, key).Result()
	if err != nil {
		return "", err
	}
	return token, nil
}

func (g ChatDao) SetWenXinTokenByCache(c context.Context, token string) {
	key := fmt.Sprintf(AigcWenxinToken)
	_, err := g.data.rdb.Set(c, key, token, 20*24*time.Hour).Result()
	if err != nil {
		g.log.WithContext(c).Errorf("设置百度文心token失败 err=%v", err)
	}
}
