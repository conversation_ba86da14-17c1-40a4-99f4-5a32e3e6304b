package data

import (
	"context"
	"errors"
	"fmt"
	"llm_service/internal/pkg/kgin"
	"strconv"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"github.com/redis/go-redis/v9"
)

// ModelStats 模型统计信息
type ModelStats struct {
	ModelName        string `json:"model_name"`
	CurrentRPM       int64  `json:"current_rpm"`        // 当前分钟请求数
	CurrentQPS       int64  `json:"current_qps"`        // 当前秒请求数
	HourlyErrorCount int64  `json:"hourly_error_count"` // 当前小时错误数
	HourlyReqCount   int64  `json:"hourly_req_count"`   // 当前小时请求数
	ErrorRate        string `json:"error_rate"`         // 错误率
}

const (
	AigcWenxinToken = "aigc:baidu:wenxin:token"

	// 统计相关的Redis key前缀 - 优化后使用Hash结构
	StatsRPMPrefix    = "llm:stats:rpm:"    // RPM统计 Hash Key: llm:stats:rpm:{model}:{date}:{hour}
	StatsQPSPrefix    = "llm:stats:qps:"    // QPS统计 Hash Key: llm:stats:qps:{model}:{date}:{hour}
	StatsErrorPrefix  = "llm:stats:error:"  // 错误统计 Hash Key: llm:stats:error:{model}:{date}:{hour}
	StatsStatusPrefix = "llm:stats:status:" // 状态码统计 Hash Key: llm:stats:status:{model}:{date}:{hour}

	// 时间格式常量
	DateFormat = "2006-01-02"

	// Hash Key格式常量 - 大幅减少key数量
	RPMHashKeyFormat    = "%s%s:%s:%02d" // prefix:model:date:hour (Hash存储60个分钟字段)
	QPSHashKeyFormat    = "%s%s:%s:%02d" // prefix:model:date:hour (Hash存储12个5分钟字段)
	ErrorHashKeyFormat  = "%s%s:%s:%02d" // prefix:model:date:hour (Hash存储不同错误类型字段)
	StatusHashKeyFormat = "%s%s:%s:%02d" // prefix:model:date:hour (Hash存储不同状态码字段)

	// 错误类型常量
	RequestErrorType = "request_error"
	StatusErrorType  = "status_error"

	// QPS时间窗口大小（分钟）- 使用5分钟窗口减少key数量
	QPSWindowMinutes = 5
)

type ChatDao struct {
	log  *log.Helper
	data *Data
}

func NewChatDao(data *Data, logger log.Logger) (sb *ChatDao) {
	return &ChatDao{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (g ChatDao) RedisTtl(ctx context.Context, key string) (result time.Duration, err error) {
	return g.data.rdb.TTL(ctx, key).Result()
}

func (g ChatDao) RedisSet(ctx context.Context, key, value string, expiration time.Duration) error {
	return g.data.rdb.Set(ctx, key, value, expiration).Err()
}

func (g ChatDao) RedisGet(ctx context.Context, key string) (string, error) {
	return g.data.rdb.Get(ctx, key).Result()
}

func (g ChatDao) RedisExists(ctx context.Context, key string) bool {
	return g.data.rdb.Exists(ctx, key).Val() > 0
}

func (g ChatDao) RedisDel(ctx context.Context, key string) error {
	return g.data.rdb.Del(ctx, key).Err()
}

func (g ChatDao) RedisRpush(ctx context.Context, key string, value interface{}) error {
	return g.data.rdb.RPush(ctx, key, value).Err()
}

func (g ChatDao) RedisLpush(ctx context.Context, key string, value interface{}) error {
	return g.data.rdb.LPush(ctx, key, value).Err()
}

func (g ChatDao) RedisLpop(ctx context.Context, key string) (string, error) {
	return g.data.rdb.LPop(ctx, key).Result()
}

func (g ChatDao) RedisInSet(ctx *kgin.Context, key, value string) bool {
	return g.data.rdb.ZScore(ctx, key, value).Val() > 0
}

func (g ChatDao) RedisLlen(ctx context.Context, key string) (int64, error) {
	return g.data.rdb.LLen(ctx, key).Result()
}

func (g ChatDao) RedisLock(ctx context.Context, key string, second int) bool {
	expiration := time.Duration(second) * time.Second
	return g.data.rdb.SetNX(ctx, key, 1, expiration).Val()
}

func (g ChatDao) RedisUnlock(ctx context.Context, key string) {
	g.data.rdb.Del(ctx, key)
}

func (g ChatDao) RedisZAdd(ctx context.Context, key string, score float64, value interface{}) (int64, error) {
	return g.data.rdb.ZAdd(ctx, key, redis.Z{
		Score:  score,
		Member: value,
	}).Result()
}

func (g ChatDao) RedisZRangeByScore(ctx context.Context, key, min, max string, count int64) ([]string, error) {
	return g.data.rdb.ZRangeByScore(ctx, key, &redis.ZRangeBy{
		Min:   min,
		Max:   max,
		Count: count,
	}).Result()
}

func (g ChatDao) RedisZRevRangeWithScores(ctx context.Context, key string, start, stop int64) ([]redis.Z, error) {
	return g.data.rdb.ZRevRangeWithScores(ctx, key, start, stop).Result()
}

func (g ChatDao) RedisZRemRangeByRank(ctx context.Context, key string, start, stop int64) (int64, error) {
	return g.data.rdb.ZRemRangeByRank(ctx, key, start, stop).Result()
}

func (g ChatDao) RedisZRem(ctx context.Context, key string, member interface{}) (int64, error) {
	return g.data.rdb.ZRem(ctx, key, member).Result()
}

func (g ChatDao) RedisSAdd(ctx context.Context, key string, member interface{}) (int64, error) {
	return g.data.rdb.SAdd(ctx, key, member).Result()
}

func (g ChatDao) RedisSRem(ctx context.Context, key string, member interface{}) (int64, error) {
	return g.data.rdb.SRem(ctx, key, member).Result()
}

func (g ChatDao) RedisSMembers(ctx context.Context, key string) ([]string, error) {
	return g.data.rdb.SMembers(ctx, key).Result()
}

func (g ChatDao) RedisSCard(ctx context.Context, key string) (int64, error) {
	return g.data.rdb.SCard(ctx, key).Result()
}

func (g ChatDao) GetPromptByCache(c context.Context, appId, tempId string) (string, error) {
	key := fmt.Sprintf(promptCacheKey, appId, tempId)
	token, err := g.data.rdb.Get(c, key).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		g.log.WithContext(c).Errorf("获取缓存失败 err=%v", err)
		return "", err
	}
	g.log.WithContext(c).Infof("从缓存中查询key:%v ,value:%v", key, token)
	return token, nil
}

func (g ChatDao) SetPromptByCache(c context.Context, appId, tempId string, template string) {
	key := fmt.Sprintf(promptCacheKey, appId, tempId)
	_, err := g.data.rdb.Set(c, key, template, promptCacheExpire).Result()
	if err != nil {
		g.log.WithContext(c).Errorf("设置缓存失败 err=%v", err)
	}
}

func (g ChatDao) GetWenXinTokenByCache(c context.Context) (string, error) {
	key := fmt.Sprintf(AigcWenxinToken)
	token, err := g.data.rdb.Get(c, key).Result()
	if err != nil {
		return "", err
	}
	return token, nil
}

func (g ChatDao) SetWenXinTokenByCache(c context.Context, token string) {
	key := fmt.Sprintf(AigcWenxinToken)
	_, err := g.data.rdb.Set(c, key, token, 20*24*time.Hour).Result()
	if err != nil {
		g.log.WithContext(c).Errorf("设置百度文心token失败 err=%v", err)
	}
}

// ===== 统计相关方法 =====

// IncrementRPM 增加RPM计数 - 优化版本使用Hash结构
func (g ChatDao) IncrementRPM(ctx context.Context, modelName string) error {
	now := time.Now()

	// Hash Key: llm:stats:rpm:gpt-4:2024-01-15:14
	hashKey := fmt.Sprintf(RPMHashKeyFormat,
		StatsRPMPrefix,
		modelName,
		now.Format(DateFormat),
		now.Hour())

	// Hash Field: 分钟数 "30"
	field := fmt.Sprintf("%02d", now.Minute())

	// 使用HINCRBY增加Hash字段的值，大幅减少key数量
	pipe := g.data.rdb.Pipeline()
	pipe.HIncrBy(ctx, hashKey, field, 1)
	pipe.Expire(ctx, hashKey, 25*time.Hour) // 整个Hash过期
	_, err := pipe.Exec(ctx)
	return err
}

// IncrementQPS 增加QPS计数 - 优化版本使用5分钟窗口
func (g ChatDao) IncrementQPS(ctx context.Context, modelName string) error {
	now := time.Now()

	// Hash Key: llm:stats:qps:gpt-4:2024-01-15:14
	hashKey := fmt.Sprintf(QPSHashKeyFormat,
		StatsQPSPrefix,
		modelName,
		now.Format(DateFormat),
		now.Hour())

	// Hash Field: 5分钟窗口 "00", "05", "10", "15", "20", "25", "30", "35", "40", "45", "50", "55"
	windowStart := (now.Minute() / QPSWindowMinutes) * QPSWindowMinutes
	field := fmt.Sprintf("%02d", windowStart)

	// 使用HINCRBY增加Hash字段的值，从每秒一个key变成每小时12个字段
	pipe := g.data.rdb.Pipeline()
	pipe.HIncrBy(ctx, hashKey, field, 1)
	pipe.Expire(ctx, hashKey, 2*time.Hour) // 整个Hash过期
	_, err := pipe.Exec(ctx)
	return err
}

// IncrementError 增加错误计数 - 优化版本使用Hash结构
func (g ChatDao) IncrementError(ctx context.Context, modelName string, errorType string) error {
	now := time.Now()

	// Hash Key: llm:stats:error:gpt-4:2024-01-15:14
	hashKey := fmt.Sprintf(ErrorHashKeyFormat,
		StatsErrorPrefix,
		modelName,
		now.Format(DateFormat),
		now.Hour())

	// Hash Field: 错误类型 "request_error" 或 "status_error"
	field := errorType

	// 使用HINCRBY增加Hash字段的值
	pipe := g.data.rdb.Pipeline()
	pipe.HIncrBy(ctx, hashKey, field, 1)
	pipe.Expire(ctx, hashKey, 25*time.Hour) // 整个Hash过期
	_, err := pipe.Exec(ctx)
	return err
}

// IncrementStatusCode 增加状态码计数 - 优化版本使用Hash结构
func (g ChatDao) IncrementStatusCode(ctx context.Context, modelName string, statusCode int) error {
	now := time.Now()

	// Hash Key: llm:stats:status:gpt-4:2024-01-15:14
	hashKey := fmt.Sprintf(StatusHashKeyFormat,
		StatsStatusPrefix,
		modelName,
		now.Format(DateFormat),
		now.Hour())

	// Hash Field: 状态码 "500", "429", "404" 等
	field := fmt.Sprintf("%d", statusCode)

	// 使用HINCRBY增加Hash字段的值
	pipe := g.data.rdb.Pipeline()
	pipe.HIncrBy(ctx, hashKey, field, 1)
	pipe.Expire(ctx, hashKey, 25*time.Hour) // 整个Hash过期
	_, err := pipe.Exec(ctx)
	return err
}

// GetCurrentRPM 获取当前分钟的RPM - 优化版本从Hash获取
func (g ChatDao) GetCurrentRPM(ctx context.Context, modelName string) (int64, error) {
	now := time.Now()

	// Hash Key: llm:stats:rpm:gpt-4:2024-01-15:14
	hashKey := fmt.Sprintf(RPMHashKeyFormat,
		StatsRPMPrefix,
		modelName,
		now.Format(DateFormat),
		now.Hour())

	// Hash Field: 当前分钟 "30"
	field := fmt.Sprintf("%02d", now.Minute())

	return g.data.rdb.HGet(ctx, hashKey, field).Int64()
}

// GetCurrentQPS 获取当前5分钟窗口的QPS - 优化版本从Hash获取
func (g ChatDao) GetCurrentQPS(ctx context.Context, modelName string) (int64, error) {
	now := time.Now()

	// Hash Key: llm:stats:qps:gpt-4:2024-01-15:14
	hashKey := fmt.Sprintf(QPSHashKeyFormat,
		StatsQPSPrefix,
		modelName,
		now.Format(DateFormat),
		now.Hour())

	// Hash Field: 当前5分钟窗口 "00", "05", "10", "15", "20", "25", "30", "35", "40", "45", "50", "55"
	windowStart := (now.Minute() / QPSWindowMinutes) * QPSWindowMinutes
	field := fmt.Sprintf("%02d", windowStart)

	// 获取5分钟窗口内的总请求数，然后除以5得到平均每分钟请求数作为QPS指标
	totalInWindow, err := g.data.rdb.HGet(ctx, hashKey, field).Int64()
	if err != nil {
		return 0, err
	}

	// 返回5分钟窗口内的平均每分钟请求数作为QPS指标
	return totalInWindow / QPSWindowMinutes, nil
}

// GetHourlyErrorCount 获取当前小时的错误计数 - 优化版本从Hash获取
func (g ChatDao) GetHourlyErrorCount(ctx context.Context, modelName string, errorType string) (int64, error) {
	now := time.Now()

	// Hash Key: llm:stats:error:gpt-4:2024-01-15:14
	hashKey := fmt.Sprintf(ErrorHashKeyFormat,
		StatsErrorPrefix,
		modelName,
		now.Format(DateFormat),
		now.Hour())

	// Hash Field: 错误类型 "request_error" 或 "status_error"
	field := errorType

	return g.data.rdb.HGet(ctx, hashKey, field).Int64()
}

// GetHourlyStatusCount 获取当前小时的状态码计数 - 优化版本从Hash获取
func (g ChatDao) GetHourlyStatusCount(ctx context.Context, modelName string, statusCode int) (int64, error) {
	now := time.Now()

	// Hash Key: llm:stats:status:gpt-4:2024-01-15:14
	hashKey := fmt.Sprintf(StatusHashKeyFormat,
		StatsStatusPrefix,
		modelName,
		now.Format(DateFormat),
		now.Hour())

	// Hash Field: 状态码 "500", "429", "404" 等
	field := fmt.Sprintf("%d", statusCode)

	return g.data.rdb.HGet(ctx, hashKey, field).Int64()
}

// GetModelStats 获取模型的综合统计信息
func (g ChatDao) GetModelStats(ctx context.Context, modelName string) (*ModelStats, error) {
	stats := &ModelStats{
		ModelName: modelName,
	}

	// 获取当前RPM
	rpm, err := g.GetCurrentRPM(ctx, modelName)
	if err != nil && !errors.Is(err, redis.Nil) {
		g.log.WithContext(ctx).Warnf("获取RPM失败: %v", err)
	}
	stats.CurrentRPM = rpm

	// 获取当前QPS
	qps, err := g.GetCurrentQPS(ctx, modelName)
	if err != nil && !errors.Is(err, redis.Nil) {
		g.log.WithContext(ctx).Warnf("获取QPS失败: %v", err)
	}
	stats.CurrentQPS = qps

	// 获取当前小时错误数
	errorCount, err := g.GetHourlyErrorCount(ctx, modelName, RequestErrorType)
	if err != nil && !errors.Is(err, redis.Nil) {
		g.log.WithContext(ctx).Warnf("获取错误计数失败: %v", err)
	}
	stats.HourlyErrorCount = errorCount

	// 计算当前小时总请求数（通过Hash中的RPM数据累加）- 优化版本
	now := time.Now()
	totalReq := int64(0)

	// 获取当前小时的Hash
	hashKey := fmt.Sprintf(RPMHashKeyFormat,
		StatsRPMPrefix,
		modelName,
		now.Format(DateFormat),
		now.Hour())

	// 获取Hash中所有分钟的数据并累加
	allMinutes, err := g.data.rdb.HGetAll(ctx, hashKey).Result()
	if err == nil {
		for _, countStr := range allMinutes {
			if count, parseErr := strconv.ParseInt(countStr, 10, 64); parseErr == nil {
				totalReq += count
			}
		}
	}
	stats.HourlyReqCount = totalReq

	// 计算错误率
	if totalReq > 0 {
		errorRate := float64(errorCount) / float64(totalReq) * 100
		stats.ErrorRate = fmt.Sprintf("%.2f%%", errorRate)
	} else {
		stats.ErrorRate = "0.00%"
	}

	return stats, nil
}
