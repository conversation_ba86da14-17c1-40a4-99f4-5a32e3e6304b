package data

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
)

func TestChatDao_StatsOperations(t *testing.T) {
	// 创建一个模拟的Redis客户端（这里使用真实的Redis进行测试）
	// 在实际项目中，你可能需要使用testcontainers或者mock
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   1, // 使用测试数据库
	})

	// 测试连接
	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		t.Skip("Redis not available, skipping test")
	}

	// 清理测试数据
	defer func() {
		keys, _ := rdb.Keys(ctx, "llm:stats:*").Result()
		if len(keys) > 0 {
			rdb.Del(ctx, keys...)
		}
	}()

	// 创建测试用的Data和ChatDao
	data := &Data{
		rdb: rdb,
		log: log.NewHelper(log.DefaultLogger),
	}
	chatDao := &ChatDao{
		data: data,
		log:  log.NewHelper(log.DefaultLogger),
	}

	modelName := "test-model"

	t.Run("TestIncrementRPM", func(t *testing.T) {
		err := chatDao.IncrementRPM(ctx, modelName)
		assert.NoError(t, err)

		rpm, err := chatDao.GetCurrentRPM(ctx, modelName)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), rpm)
	})

	t.Run("TestIncrementQPS", func(t *testing.T) {
		err := chatDao.IncrementQPS(ctx, modelName)
		assert.NoError(t, err)

		qps, err := chatDao.GetCurrentQPS(ctx, modelName)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), qps)
	})

	t.Run("TestIncrementError", func(t *testing.T) {
		err := chatDao.IncrementError(ctx, modelName, RequestErrorType)
		assert.NoError(t, err)

		errorCount, err := chatDao.GetHourlyErrorCount(ctx, modelName, RequestErrorType)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), errorCount)
	})

	t.Run("TestIncrementStatusCode", func(t *testing.T) {
		err := chatDao.IncrementStatusCode(ctx, modelName, 500)
		assert.NoError(t, err)

		statusCount, err := chatDao.GetHourlyStatusCount(ctx, modelName, 500)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), statusCount)
	})

	t.Run("TestGetModelStats", func(t *testing.T) {
		// 先增加一些统计数据
		_ = chatDao.IncrementRPM(ctx, modelName)
		_ = chatDao.IncrementQPS(ctx, modelName)
		_ = chatDao.IncrementError(ctx, modelName, RequestErrorType)

		stats, err := chatDao.GetModelStats(ctx, modelName)
		assert.NoError(t, err)
		assert.NotNil(t, stats)
		assert.Equal(t, modelName, stats.ModelName)
		assert.True(t, stats.CurrentRPM > 0)
		assert.True(t, stats.CurrentQPS > 0)
		assert.True(t, stats.HourlyErrorCount > 0)
		assert.NotEmpty(t, stats.ErrorRate)
	})
}

func TestChatDao_StatsKeyFormats(t *testing.T) {
	now := time.Now()

	// 测试RPM key格式
	expectedRPMKey := "llm:stats:rpm:test-model:" + now.Format("2006-01-02") + ":" +
		fmt.Sprintf("%02d:%02d", now.Hour(), now.Minute())

	// 测试QPS key格式
	expectedQPSKey := "llm:stats:qps:test-model:" + now.Format("2006-01-02") + ":" +
		fmt.Sprintf("%02d:%02d:%02d", now.Hour(), now.Minute(), now.Second())

	// 测试Error key格式
	expectedErrorKey := "llm:stats:error:test-model:" + RequestErrorType + ":" +
		now.Format("2006-01-02") + ":" + fmt.Sprintf("%02d", now.Hour())

	// 测试Status key格式
	expectedStatusKey := "llm:stats:status:test-model:500:" + now.Format("2006-01-02") + ":" +
		fmt.Sprintf("%02d", now.Hour())

	t.Logf("Expected RPM key: %s", expectedRPMKey)
	t.Logf("Expected QPS key: %s", expectedQPSKey)
	t.Logf("Expected Error key: %s", expectedErrorKey)
	t.Logf("Expected Status key: %s", expectedStatusKey)
}
