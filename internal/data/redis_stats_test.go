package data

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
)

func TestOptimizedRedisStats(t *testing.T) {
	// 创建测试用的Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   1, // 使用测试数据库
	})

	// 测试连接
	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		t.Skip("Redis not available, skipping test")
	}

	// 清理测试数据
	defer func() {
		keys, _ := rdb.Keys(ctx, "llm:stats:*").Result()
		if len(keys) > 0 {
			rdb.Del(ctx, keys...)
		}
	}()

	// 创建测试用的ChatDao
	dataInstance := &Data{rdb: rdb}
	chatDao := &ChatDao{data: dataInstance}

	modelName := "test-gpt-4"

	t.Run("测试优化后的RPM统计", func(t *testing.T) {
		// 记录几次RPM
		for i := 0; i < 5; i++ {
			err := chatDao.IncrementRPM(ctx, modelName)
			assert.NoError(t, err)
		}

		// 获取当前RPM
		rpm, err := chatDao.GetCurrentRPM(ctx, modelName)
		assert.NoError(t, err)
		assert.Equal(t, int64(5), rpm)

		// 验证Hash结构
		now := time.Now()
		hashKey := "llm:stats:rpm:" + modelName + ":" + now.Format("2006-01-02") + ":" +
			fmt.Sprintf("%02d", now.Hour())
		field := fmt.Sprintf("%02d", now.Minute())

		value, err := rdb.HGet(ctx, hashKey, field).Int64()
		assert.NoError(t, err)
		assert.Equal(t, int64(5), value)
	})

	t.Run("测试优化后的QPS统计", func(t *testing.T) {
		// 记录几次QPS
		for i := 0; i < 3; i++ {
			err := chatDao.IncrementQPS(ctx, modelName)
			assert.NoError(t, err)
		}

		// 获取当前QPS（5分钟窗口平均）
		qps, err := chatDao.GetCurrentQPS(ctx, modelName)
		assert.NoError(t, err)
		// 3个请求 / 5分钟 = 0.6，向下取整为0
		assert.Equal(t, int64(0), qps)

		// 验证Hash结构
		now := time.Now()
		hashKey := "llm:stats:qps:" + modelName + ":" + now.Format("2006-01-02") + ":" +
			fmt.Sprintf("%02d", now.Hour())
		windowStart := (now.Minute() / QPSWindowMinutes) * QPSWindowMinutes
		field := fmt.Sprintf("%02d", windowStart)

		value, err := rdb.HGet(ctx, hashKey, field).Int64()
		assert.NoError(t, err)
		assert.Equal(t, int64(3), value)
	})

	t.Run("测试优化后的错误统计", func(t *testing.T) {
		// 记录错误
		err := chatDao.IncrementError(ctx, modelName, RequestErrorType)
		assert.NoError(t, err)

		err = chatDao.IncrementError(ctx, modelName, StatusErrorType)
		assert.NoError(t, err)

		// 获取错误计数
		requestErrors, err := chatDao.GetHourlyErrorCount(ctx, modelName, RequestErrorType)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), requestErrors)

		statusErrors, err := chatDao.GetHourlyErrorCount(ctx, modelName, StatusErrorType)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), statusErrors)
	})

	t.Run("测试优化后的状态码统计", func(t *testing.T) {
		// 记录状态码
		err := chatDao.IncrementStatusCode(ctx, modelName, 500)
		assert.NoError(t, err)

		err = chatDao.IncrementStatusCode(ctx, modelName, 429)
		assert.NoError(t, err)

		// 获取状态码计数
		count500, err := chatDao.GetHourlyStatusCount(ctx, modelName, 500)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), count500)

		count429, err := chatDao.GetHourlyStatusCount(ctx, modelName, 429)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), count429)
	})

	t.Run("验证Key数量优化效果", func(t *testing.T) {
		// 清理之前的测试数据
		keys, _ := rdb.Keys(ctx, "llm:stats:*").Result()
		if len(keys) > 0 {
			rdb.Del(ctx, keys...)
		}

		// 模拟一小时内的请求
		testModel := "optimization-test"

		// 记录60分钟的RPM数据（优化前需要60个key，优化后只需要1个Hash）
		for minute := 0; minute < 60; minute++ {
			// 模拟不同分钟的请求
			testTime := time.Now().Add(-time.Duration(minute) * time.Minute)

			// 这里我们直接操作Redis来模拟不同时间的数据
			hashKey := fmt.Sprintf("llm:stats:rpm:%s:%s:%02d",
				testModel,
				testTime.Format("2006-01-02"),
				testTime.Hour())
			field := fmt.Sprintf("%02d", testTime.Minute())

			rdb.HIncrBy(ctx, hashKey, field, int64(minute+1))
		}

		// 验证只产生了1个Hash key而不是60个独立key
		keys, err := rdb.Keys(ctx, "llm:stats:rpm:*").Result()
		assert.NoError(t, err)
		assert.Equal(t, 1, len(keys), "优化后应该只有1个Hash key，而不是60个独立key")

		// 验证Hash中有60个字段
		hashKey := keys[0]
		fields, err := rdb.HGetAll(ctx, hashKey).Result()
		assert.NoError(t, err)
		assert.Equal(t, 60, len(fields), "Hash中应该有60个分钟字段")

		t.Logf("优化效果：60分钟的RPM数据只用了1个Hash key，包含%d个字段", len(fields))
	})
}
