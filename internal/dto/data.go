package dto

import "llm_service/internal/dto/request"

type Data struct {
	ErrorCode  int      `json:"error_code"`
	ErrorMsg   string   `json:"error_msg"`
	Sensitive  []string `json:"sensitive"`
	TraceId    string   `json:"trace_id"`
	Id         string   `json:"id"`
	Object     string   `json:"object"`
	Created    int      `json:"created"`
	SentenceId int      `json:"sentence_id"`
	IsDeepSeek bool     `json:"is_deep_seek"`
	IsEnd      bool     `json:"is_end"`
	Result     string   `json:"result"`
	Model      string   `json:"model"`
	Source     string   `json:"source"`
	Usage      Usage    `json:"usage"`
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

type SSECommonChat struct {
	History           []request.Message `json:"history"`
	MultiModalHistory interface{}       `json:"multi_modal_history,omitempty"` // 多模态历史记录，如果有值则优先使用
	Version           string            `json:"version"`
	Model             string            `json:"model"`
	MultiMessage      interface{}       `json:"multi_message"`
	Message           string            `json:"message"`
	Temperature       float32           `json:"temperature"`
	TopP              float32           `json:"top_p"`
	Prompt            string            `json:"prompt"`
	WithSearchEnhance *bool             `json:"with_search_enhance"`
	IsCheck           *int              `json:"is_check"`
	ApiKey            string            `yaml:"apiKey"`
	URL               string            `yaml:"url"`
	SessionId         string            `json:"session_id,omitempty"`
	EnableSearch      bool              `json:"enable_search"`
}
