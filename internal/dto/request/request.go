package request

//type GptModel string
//
//const (
//	Gpt3 GptModel = "gpt-3.5-turbo"
//	Gpt4 GptModel = "gpt-4"
//)
//
//func (m GptModel) String() string {
//	return string(m)
//}

type Role string

const (
	AssistantRole Role = "assistant"
	SystemRole    Role = "system"
	UserRole      Role = "user"
)

var RoleMap = map[int]Role{
	1: UserRole,
	2: AssistantRole,
}

var RoleTypeMap = map[Role]int{
	UserRole:      1,
	AssistantRole: 2,
}

func (r Role) String() string {
	return string(r)
}

type LlmBiz int

// 1-对话;2-口语;3-自习室;4-作文
const (
	LlmBizUnknown LlmBiz = iota
	LlmBizLui
	LlmBizKouYu
	LlmBizZHZuoWen
	LlmBizENZuoWen
	LlmBizSuperNotes  = 18
	LlmBizCircle      = 19
	LlmBizAIExplain   = 21
	LlmBizATYuWen     = 25
	LlmBizXSBianCheng = 29
	LlmBizXZYD        = 32 //写作引导
	LlmBizDeepSeek    = 1000000
)

type GptReq struct {
	Msg      string `json:"msg"`
	System   string `json:"system"`
	Strategy string `json:"strategy"`
}

type Record struct {
	Type int    `json:"type"`
	Msg  string `json:"msg"`
}

type Chat struct {
	History   []Record      `json:"history"`
	Message   string        `json:"message"`
	SessionId string        `json:"session_id" mapstructure:"session_id"`
	TmpId     string        `json:"tmp_id" mapstructure:"tmp_id"`
	Args      []interface{} `json:"args"`
}

type Message struct {
	Role    Role   `json:"role" form:"role"`
	Content string `json:"content" form:"content"`
}
type MultiMessage struct {
	Role    Role        `json:"role" form:"role"`
	Content interface{} `json:"content" form:"content"`
}

type History struct {
	Type    int    `form:"type" json:"type"`
	Msg     string `form:"msg" json:"msg"`
	Current int64  `form:"current" json:"current"`
}

// MultiModalMessage 多模态消息单条结构
type MultiModalMessage struct {
	Type     string    `json:"type"`      // 消息类型：text/image_url
	Text     string    `json:"text"`      // 文本内容，当 type=text 时使用
	ImageUrl *ImageUrl `json:"image_url"` // 图片信息，当 type=image_url 时使用
}

// ImageUrl 图片URL结构
type ImageUrl struct {
	Url string `json:"url"` // 图片URL
}

type LlmCommonRequest struct {
	Biz               LlmBiz        `json:"biz"`
	SessionId         string        `json:"session_id"`
	TmpId             string        `json:"tmp_id" binding:"required"`
	EnableSearch      bool          `json:"enable_search"`
	MultiMessage      interface{}   `json:"multi_message"`
	Args              []interface{} `json:"args"`
	Temperature       float32       `json:"temperature"`
	Message           string        `json:"message" binding:"required"`
	History           []Message     `json:"history"`
	MultiModalHistory interface{}   `json:"multi_modal_history,omitempty"` // 多模态历史记录，如果有值则优先使用
	MsgNoCheck        bool          `json:"msg_no_check"`
	OutputNoCheck     bool          `json:"output_no_check"`
	Sn                string        `json:"sn"` //sn
	TalId             string        `json:"tal_id"`
}

type GptTalRequest struct {
	Message   string    `form:"message" json:"message"  binding:"required,min=1"`
	History   []Message `form:"history" json:"history"`
	SessionId string    `form:"session_id" json:"session_id"`
}
