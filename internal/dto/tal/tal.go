package tal

type GptTalRequest struct {
	Type      int             `json:"type"`
	Prompt    string          `json:"prompt"`
	Messages  []GptTalMessage `json:"messages"`
	UserInfo  string          `json:"user_info"`
	UserID    string          `json:"user_id"`
	SessionID string          `json:"session_id"`
	AskID     string          `json:"ask_id"`
	PromptID  int64           `json:"prompt_id"`
}

type GptTalMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type GptTalResponse struct {
	Code      int    `json:"code"`
	Msg       string `json:"msg"`
	RequestID string `json:"requestId"`
	PromptID  int    `json:"prompt_id"`
	Data      struct {
		UserID    string `json:"user_id"`
		SessionID string `json:"session_id"`
		UserInfo  string `json:"user_info"`
		Result    string `json:"result"`
		IsEnd     int    `json:"is_end"`
		Mod       string `json:"mod"`
	} `json:"data"`
}
