package ws

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/websocket"
	"sync"
)

// DuiConnect 扩展websocket连接
type DuiConnect struct {
	Logger              *log.Helper
	WsSocket            *websocket.Conn // 底层websocket
	WsId                string          // socketId
	DeviceId            string          // 客户端设备唯一标识
	StartTs             int64           // 建连时间
	StageMutex          sync.Mutex      // 当前状态锁
	SendMutex           sync.Mutex      // 发送消息锁
	StopMutex           sync.Mutex      // 截停锁
	SpeechWsId          string          // 思必驰RecordId，可以作为与思必驰的连接ID
	CloseChan           chan struct{}   // 关闭通知 0个字节，省内存
	SpeechStartTime     int64           // 首包时间
	SpeechFeedTime      int64           // 上次给思必驰发包的时间 毫秒
	SpeechPcmData       []byte          // 原始pcm音频流
	SpeechRec           string          // 思必驰返回识别结果缓存
	SpeechRecReal       string          // 思必驰返回实时识别结果
	SpeechRecRealPinyin string          // 思必驰返回实时识别结果拼音
	SpeechRecTime       int64           // 思必驰上次返回实时识别结果的时间
}

func (ws *DuiConnect) CloseDuiSocketConnect(c context.Context) {

	err := ws.WsSocket.Close()
	if err != nil {
		ws.Logger.Errorf("[HMI-Dws] Dws close err: %v", err)
	} else {
		ws.Logger.Info("[HMI-Dws] Dws close success")
	}

	close(ws.CloseChan)
}

// SendText 向思必驰发送文本格式数据
func (ws *DuiConnect) SendText(c context.Context, data []byte) error {
	err := ws.WsSocket.WriteMessage(websocket.TextMessage, data)
	if err != nil {
		ws.Logger.WithContext(c).Errorf("[HMI-SendToDws] WriteMessage err: %v; data: %s", err, string(data))
		return err
	}
	ws.Logger.WithContext(c).Infof("[HMI-SendToDws] SendText finish")
	return nil
}

// SendBinary 向思必驰发送二进制格式数据
func (ws *DuiConnect) SendBinary(c context.Context, data []byte) error {

	ws.SendMutex.Lock()
	defer ws.SendMutex.Unlock()
	err := ws.WsSocket.WriteMessage(websocket.BinaryMessage, data)
	if err != nil {
		ws.Logger.Errorf("[HMI-SendToDws] WriteMessage err: %v; data: %v", err, data)
		return err
	}
	ws.Logger.Infof("[HMI-SendToDws] SendBinary finish")
	return nil
}

// ResetSpeechAttachment 还原与思必驰相关的业务参数
func (ws *DuiConnect) ResetSpeechAttachment() {
	ws.SpeechWsId = ""
	ws.SpeechRec = ""
	ws.SpeechRecReal = ""
	ws.SpeechRecRealPinyin = ""
	ws.SpeechRecTime = 0
	ws.SpeechStartTime = 0
	ws.SpeechFeedTime = 0
}
