package server

import (
	netHttp "net/http"
	"strings"
	"time"

	"llm_service/internal/common"

	"github.com/go-kratos/kratos/v2/encoding"
	"github.com/go-kratos/kratos/v2/errors"
)

type Response struct {
	ErrorReason string            `json:"error_reason"`
	ErrorMsg    string            `json:"error_msg"`
	MetaData    map[string]string `json:"meta_data"`
	TraceId     string            `json:"trace_id"`
	ServerTime  int64             `json:"server_time"`
	Data        interface{}       `json:"data"`
}

const (
	commonErrReason = "SERVER_ERROR"
	commonErrMsg    = "服务器错误"
)

// MyErrorEncoder encodes the error to the HTTP response.
func MyErrorEncoder(w netHttp.ResponseWriter, r *netHttp.Request, err error) {
	se := errors.FromError(err)
	codec, _ := CodecForRequest(r, "Accept")

	if se.Code == netHttp.StatusInternalServerError {
		se.Reason = commonErrReason
		se.Message = commonErrMsg
	}
	response := Response{
		ErrorReason: se.Reason,
		ErrorMsg:    se.Message,
		MetaData:    se.Metadata,
		TraceId:     r.Header.Get(common.TraceId),
		ServerTime:  time.Now().Unix(),
		Data:        nil,
	}

	body, err := codec.Marshal(response)
	if err != nil {
		w.WriteHeader(netHttp.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(int(se.Code))
	_, _ = w.Write(body)
}

// CodecForRequest get encoding.Codec via http.Request
func CodecForRequest(r *netHttp.Request, name string) (encoding.Codec, bool) {
	for _, accept := range r.Header[name] {
		codec := encoding.GetCodec(ContentSubtype(accept))
		if codec != nil {
			return codec, true
		}
	}
	return encoding.GetCodec("json"), false
}

func ContentSubtype(contentType string) string {
	left := strings.Index(contentType, "/")
	if left == -1 {
		return ""
	}
	right := strings.Index(contentType, ";")
	if right == -1 {
		right = len(contentType)
	}
	if right < left {
		return ""
	}
	return contentType[left+1 : right]
}
