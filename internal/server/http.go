package server

import (
	"git.100tal.com/znxx_xpp/go-libs/httpserver"
	"git.100tal.com/znxx_xpp/go-libs/traces"
	"github.com/gin-gonic/gin"
	prom "github.com/go-kratos/kratos/contrib/metrics/prometheus/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"llm_service/internal/conf"
	"llm_service/internal/pkg/kgin"
	"llm_service/internal/server/middleware"
	"llm_service/internal/service"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *conf.Server,
	sseService *service.SseService,
	logger log.Logger) *http.Server {

	router := gin.Default()
	router.Use(middleware.Middleware(
		recovery.Recovery(),
		tracing.Server(),
		logging.Server(logger),
		validate.Validator(),
		metrics.Server(
			metrics.WithSeconds(prom.NewHistogram(middleware.MetricSeconds)),
			metrics.WithRequests(prom.NewCounter(middleware.MetricRequests)),
		),
		metadata.Server(),
		traces.HttpTraceEvent(),
		middleware.AddTraceToCtx(),
	))
	router.POST("/sse/common/chat", kgin.HandleFunc(sseService.SseCommonChat))
	router.POST("/common/chat", kgin.HandleFunc(sseService.CommonChat))

	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			tracing.Server(),
			logging.Server(logger),
			validate.Validator(),
			metrics.Server(
				metrics.WithSeconds(prom.NewHistogram(middleware.MetricSeconds)),
				metrics.WithRequests(prom.NewCounter(middleware.MetricRequests)),
			),
			metadata.Server(),
			traces.HttpTraceEvent(),
			middleware.AddTraceToCtx(),
		),
		httpserver.ServerHandle,
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	opts = append(opts, http.ErrorEncoder(MyErrorEncoder))
	opts = append(opts, http.ResponseEncoder(MyResponseEncoder))

	srv := http.NewServer(opts...)
	middleware.Init()
	srv.Handle("/debug/metrics", promhttp.Handler())
	srv.HandlePrefix("/", router)
	return srv
}
