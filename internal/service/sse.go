package service

import (
	"encoding/json"
	"io"
	"llm_service/internal/biz"
	"llm_service/internal/biz/llm/tal_ws"
	"llm_service/internal/common"
	"llm_service/internal/dto/request"
	"llm_service/internal/dto/response"
	"llm_service/internal/pkg/kgin"
	"time"

	"github.com/gin-contrib/sse"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"golang.org/x/net/context"
)

type SseService struct {
	au  *biz.SseUseCase
	tal *tal_ws.TalUseCase
	log *log.Helper
}

func NewSseService(au *biz.SseUseCase, tal *tal_ws.TalUseCase, logger log.Logger) *SseService {
	return &SseService{
		au:  au,
		tal: tal,
		log: log.NewHelper(logger),
	}
}

func (p *SseService) SseCommonChat(c *kgin.Context) {
	var (
		req request.LlmCommonRequest
	)
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		biz.Render(c, p.log, err, "请求参数错误")
		return
	}
	//用于记录请求时长
	ctx := context.WithValue(c.Request.Context(), common.StartTime, time.Now())
	ctx = context.WithValue(ctx, common.TmpId, req.TmpId)
	w := c.Writer
	w.Header().Set(common.HeaderContentType, "text/event-stream")
	w.Header().Set(common.HeaderCacheControl, "no-cache")
	w.Header().Set(common.HeaderConnection, "keep-alive")
	w.Header().Set(common.HeaderTransferEncoding, "chunked")
	appid := c.Request.Header.Get(common.HeaderXGenieAppId)
	language := c.Request.Header.Get(common.HeaderXGenieLanguage)
	deviceId := c.Request.Header.Get(common.HeaderXGenieDeviceId)
	ctx = context.WithValue(ctx, common.AppId, appid)
	ctx = context.WithValue(ctx, common.Language, language)
	ctx = context.WithValue(ctx, common.DeviceId, deviceId)

	// 将完整的请求头信息放入context中
	// 明确转换为map[string][]string类型以确保类型匹配
	headerMap := make(map[string][]string)
	for key, values := range c.Request.Header {
		headerMap[key] = values
	}
	ctx = context.WithValue(ctx, common.RequestHeaders, headerMap)
	p.log.WithContext(ctx).Infof("设置请求头到context: %v", headerMap)

	jReq, _ := json.Marshal(req)
	p.log.WithContext(c.Request.Context()).Infof("SseCommonChat req:%v ,header:%v", string(jReq), c.Request.Header)

	jReq, _ = json.Marshal(req)
	p.log.WithContext(c.Request.Context()).Infof("SseCommonChat req:%v ,header:%v", string(jReq), c.Request.Header)
	//主动清除空间占用
	jReq = []byte{}
	// 默认取 traceId 作为 sessionId
	appId := c.GetHeader("X-Genie-AppId")
	ch, err := p.au.SSECommon(ctx, appId, &req)
	if err != nil {
		biz.Render(c, p.log, err, "请求大模型失败")
		return
	}

	c.Stream(func(w io.Writer) bool {
		select {
		case data, ok := <-ch:
			if !ok {
				return false
			}
			data.TraceId = ctx.Value(common.TraceId).(string)
			tmpId := ctx.Value(common.TmpId).(string)
			jData, _ := json.Marshal(data)
			if data.IsEnd {
				startTime := ctx.Value(common.StartTime)
				var costT time.Duration
				if startTime != nil {
					costT = time.Since(startTime.(time.Time))
					p.log.WithContext(ctx).Infof("模型输出完整时长 source:%s,TmpId:%v,cost:%v", data.Source, tmpId, costT.Milliseconds())
				}
				p.log.WithContext(ctx).Infof("END输出llm-service结果=> TmpId:%v , Source:%v, 输入token数:%v , 输出token数:%v , 总token数:%v , 内容:%v ", tmpId, data.Source, data.Usage.PromptTokens, data.Usage.CompletionTokens, data.Usage.TotalTokens, string(jData))
			}
			p.log.WithContext(ctx).Infof("llm-service结果=> TmpId:%v ,内容:%v", tmpId, string(jData))
			jData = []byte{}
			_ = sse.Encode(w, sse.Event{
				Event: "llm",
				Id:    uuid.New().String(),
				Data:  data,
			})
		case <-c.Request.Context().Done():
			return false
		}
		return true
	})
}

func (p *SseService) CommonChat(c *kgin.Context) {
	var (
		req  request.LlmCommonRequest
		resp *response.LlmCommonResp
	)
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JsonError(common.GetErrorCode(err), "请求参数错误")
		return
	}
	//用于记录请求时长
	ctx := context.WithValue(c.Request.Context(), common.StartTime, time.Now())
	ctx = context.WithValue(ctx, common.TmpId, req.TmpId)
	w := c.Writer
	w.Header().Set(common.HeaderCacheControl, "no-cache")
	w.Header().Set(common.HeaderConnection, "keep-alive")
	appid := c.Request.Header.Get(common.HeaderXGenieAppId)
	language := c.Request.Header.Get(common.HeaderXGenieLanguage)
	ctx = context.WithValue(ctx, common.AppId, appid)
	ctx = context.WithValue(ctx, common.Language, language)

	// 将完整的请求头信息放入context中
	// 明确转换为map[string][]string类型以确保类型匹配
	headerMap := make(map[string][]string)
	for key, values := range c.Request.Header {
		headerMap[key] = values
	}
	ctx = context.WithValue(ctx, common.RequestHeaders, headerMap)
	p.log.WithContext(ctx).Infof("设置请求头到context: %v", headerMap)

	jReq, _ := json.Marshal(req)
	p.log.WithContext(c.Request.Context()).Infof("SseCommonChatNoStream req:%v ,header:%v", string(jReq), c.Request.Header)
	//主动清除空间占用
	jReq = []byte{}
	// 默认取 traceId 作为 sessionId
	appId := c.GetHeader("X-Genie-AppId")
	resp, err := p.au.Common(ctx, appId, &req)
	if err != nil {
		c.JsonError(common.GetErrorCode(err), common.GetErrorMessage(err))
		return
	}
	c.JsonOK(resp)
}
