package tcm

import (
	"log"
	"os"
	"strings"

	"llm_service/internal/common"

	nacosConfig "github.com/go-kratos/kratos/contrib/config/nacos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/nacos-group/nacos-sdk-go/clients"
	"github.com/nacos-group/nacos-sdk-go/common/constant"
	"github.com/nacos-group/nacos-sdk-go/vo"
)

func NewConfig(name string, env common.Env, dataId ...string) (config.Config, error) {
	// 读本地
	if env == common.LocalEnv {
		localDir := "../../configs"
		// 判断working dir
		dir, _ := os.Getwd()
		if !strings.Contains(dir, "/cmd/") {
			localDir = "./configs"
		}
		log.Printf("======load config from local: %s======", localDir)
		return config.New(
			config.WithSource(file.NewSource(localDir)),
		), nil
	}

	// 读tcm
	tcmClient, err := clients.NewConfigClient(
		vo.NacosClientParam{
			ClientConfig:  env.TcmClientConfig(),
			ServerConfigs: []constant.ServerConfig{env.TcmServerConfig()},
		},
	)
	if err != nil {
		return nil, err
	}

	if len(dataId) == 0 {
		dataId = []string{
			"config.yaml",
		}
	}

	log.Printf("======load config from tcm, env:%s, group:%s, dataIds:%+v======", env, name, dataId)

	configSources := make([]config.Source, 0)
	for _, dataId := range dataId {
		configSource := nacosConfig.NewConfigSource(
			tcmClient,
			nacosConfig.WithGroup(name),
			nacosConfig.WithDataID(dataId),
		)
		configSources = append(configSources, configSource)
	}
	return config.New(
		config.WithSource(configSources...),
	), nil
}
